import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/services/app_initialization_manager.dart';
import 'package:contentpal/services/service_locator.dart';

/// 启动性能测试
/// 验证应用启动优化的效果
void main() {
  group('启动性能测试', () {
    setUp(() {
      // 重置初始化状态
      AppInitializationManager.reset();
    });

    testWidgets('关键服务初始化应该快速完成', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();
      
      await AppInitializationManager.initializeCriticalServices();
      
      stopwatch.stop();
      
      // 关键服务初始化应该在1秒内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      expect(AppInitializationManager.criticalServicesInitialized, isTrue);
      
      print('关键服务初始化耗时: ${stopwatch.elapsedMilliseconds}ms');
    });

    testWidgets('非关键服务可以在后台初始化', (WidgetTester tester) async {
      // 先初始化关键服务
      await AppInitializationManager.initializeCriticalServices();
      
      final stopwatch = Stopwatch()..start();
      
      // 非关键服务初始化（不阻塞UI）
      final future = AppInitializationManager.initializeNonCriticalServices();
      
      // 验证可以立即继续执行其他操作
      expect(AppInitializationManager.criticalServicesInitialized, isTrue);
      
      // 等待非关键服务完成
      await future;
      
      stopwatch.stop();
      
      expect(AppInitializationManager.nonCriticalServicesInitialized, isTrue);
      expect(AppInitializationManager.allServicesInitialized, isTrue);
      
      print('非关键服务初始化耗时: ${stopwatch.elapsedMilliseconds}ms');
    });

    testWidgets('服务初始化应该是幂等的', (WidgetTester tester) async {
      // 多次调用初始化方法
      await AppInitializationManager.initializeCriticalServices();
      await AppInitializationManager.initializeCriticalServices();
      await AppInitializationManager.initializeCriticalServices();
      
      expect(AppInitializationManager.criticalServicesInitialized, isTrue);
      
      await AppInitializationManager.initializeNonCriticalServices();
      await AppInitializationManager.initializeNonCriticalServices();
      
      expect(AppInitializationManager.nonCriticalServicesInitialized, isTrue);
    });

    testWidgets('ServiceLocator应该支持分阶段初始化', (WidgetTester tester) async {
      final serviceLocator = ServiceLocator();
      
      // 测试关键服务初始化
      final stopwatch1 = Stopwatch()..start();
      await serviceLocator.initCriticalServices();
      stopwatch1.stop();
      
      expect(serviceLocator.criticalServicesInitialized, isTrue);
      expect(serviceLocator.nonCriticalServicesInitialized, isFalse);
      
      print('ServiceLocator关键服务初始化耗时: ${stopwatch1.elapsedMilliseconds}ms');
      
      // 测试非关键服务初始化
      final stopwatch2 = Stopwatch()..start();
      await serviceLocator.initNonCriticalServices();
      stopwatch2.stop();
      
      expect(serviceLocator.nonCriticalServicesInitialized, isTrue);
      expect(serviceLocator.isInitialized, isTrue);
      
      print('ServiceLocator非关键服务初始化耗时: ${stopwatch2.elapsedMilliseconds}ms');
    });
  });

  group('性能基准测试', () {
    testWidgets('测量完整启动流程性能', (WidgetTester tester) async {
      final totalStopwatch = Stopwatch()..start();
      
      // 阶段1：关键服务初始化
      final criticalStopwatch = Stopwatch()..start();
      await AppInitializationManager.initializeCriticalServices();
      criticalStopwatch.stop();
      
      // 阶段2：非关键服务初始化
      final nonCriticalStopwatch = Stopwatch()..start();
      await AppInitializationManager.initializeNonCriticalServices();
      nonCriticalStopwatch.stop();
      
      totalStopwatch.stop();
      
      // 性能基准
      expect(criticalStopwatch.elapsedMilliseconds, lessThan(1000), 
             reason: '关键服务初始化应该在1秒内完成');
      expect(nonCriticalStopwatch.elapsedMilliseconds, lessThan(5000), 
             reason: '非关键服务初始化应该在5秒内完成');
      expect(totalStopwatch.elapsedMilliseconds, lessThan(6000), 
             reason: '总启动时间应该在6秒内完成');
      
      print('=== 启动性能报告 ===');
      print('关键服务初始化: ${criticalStopwatch.elapsedMilliseconds}ms');
      print('非关键服务初始化: ${nonCriticalStopwatch.elapsedMilliseconds}ms');
      print('总启动时间: ${totalStopwatch.elapsedMilliseconds}ms');
      print('==================');
    });
  });
}
