// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => '内容君';

  @override
  String get appNameChinese => '内容君';

  @override
  String get appDescription => '专业的内容处理工具，让内容创作更轻松';

  @override
  String get home => '首页';

  @override
  String get settings => '设置';

  @override
  String get language => '语言';

  @override
  String get theme => '主题';

  @override
  String get lightTheme => '浅色';

  @override
  String get darkTheme => '深色';

  @override
  String get systemTheme => '跟随系统';

  @override
  String get sharedText => '分享的文本';

  @override
  String failedToGetInitialIntent(Object error) {
    return '获取初始意图失败：$error';
  }

  @override
  String failedToLoadThemeSettings(Object error) {
    return '加载主题设置失败：$error';
  }

  @override
  String get themeMaterialYou => 'Material You';

  @override
  String get themeMorandi => '莫兰迪风格';

  @override
  String get themeMonochrome => '极简黑白';

  @override
  String get themeNature => '自然色系';

  @override
  String get themeTech => '科技感';

  @override
  String get themeChinese => '中国传统色';

  @override
  String get themeMaterialYouDesc => '根据壁纸自动提取的动态主题';

  @override
  String get themeMorandiDesc => '柔和优雅的莫兰迪色系';

  @override
  String get themeMonochromeDesc => '简约纯粹的黑白配色';

  @override
  String get themeNatureDesc => '舒适自然的生态色系';

  @override
  String get themeTechDesc => '充满未来感的科技色彩';

  @override
  String get themeChineseDesc => '传统与现代结合的东方美学';

  @override
  String get markdown => 'Markdown';

  @override
  String get textCards => '文本卡片';

  @override
  String get textCardSelectColor => '选择颜色';

  @override
  String get textCardStyleApplied => '样式已应用';

  @override
  String textCardSplitFailed(Object error) {
    return '拆分失败：$error';
  }

  @override
  String textCardCreateFailed(Object error) {
    return '创建失败：$error';
  }

  @override
  String get textCardEditCard => '编辑卡片';

  @override
  String get textCardPreviousStep => '上一步';

  @override
  String get textCardSaveToContentLibrary => '保存到内容库';

  @override
  String get textCardStartExportingImage => '开始导出图片...';

  @override
  String get textCardImageSavedSuccess => '✅ 图片已成功保存到相册';

  @override
  String get textCardPleaseEnterContent => '请输入卡片内容';

  @override
  String get textCardDeleteCard => '删除卡片';

  @override
  String get textCardDeleteConfirm => '确定要删除这个卡片吗？此操作无法撤销。';

  @override
  String textCardCategory(Object category) {
    return '分类：$category';
  }

  @override
  String textCardDescription(Object description) {
    return '描述：$description';
  }

  @override
  String get textCardClose => '关闭';

  @override
  String get textCardUseTemplate => '使用模板';

  @override
  String get textCardConfirmExport => '确定导出';

  @override
  String get textCardQuality => '质量';

  @override
  String get textCardIncludeWatermark => '包含水印';

  @override
  String get textCardPreviewInfo => '预览信息';

  @override
  String textCardRatio(Object ratio) {
    return '比例: $ratio';
  }

  @override
  String textCardQualityPercent(Object quality) {
    return '质量: $quality%';
  }

  @override
  String textCardWatermarkStatus(Object status) {
    return '水印: $status';
  }

  @override
  String get textCardDimensions => '尺寸';

  @override
  String get textCardInclude => '包含';

  @override
  String get textCardNotInclude => '不包含';

  @override
  String get textCardAddCard => '添加卡片';

  @override
  String get textCardAddNewCard => '添加新卡片';

  @override
  String get textCardEdit => '编辑';

  @override
  String get textCardExportingImage => '正在导出图片...';

  @override
  String get textCardExportSuccess => '✅ 导出成功！已保存到相册';

  @override
  String textCardExportFailed(Object error) {
    return '导出失败：';
  }

  @override
  String get textCardAddWatermark => '添加水印';

  @override
  String get textCardAddWatermarkDesc => '在图片角落添加应用水印';

  @override
  String get textCardIncludeTitle => '包含标题';

  @override
  String get textCardIncludeTitleDesc => '在导出的图片中显示标题';

  @override
  String get textCardIncludeTimestamp => '包含时间戳';

  @override
  String get textCardIncludeTimestampDesc => '在图片中显示创建时间';

  @override
  String get textCardImageQuality => '图片质量';

  @override
  String textCardTextStyleCustomization(Object title) {
    return '文本样式定制';
  }

  @override
  String get textCardExportCurrentCard => '导出当前卡片';

  @override
  String get textCardBatchExport => '批量导出';

  @override
  String get textCardClearSelection => '清除选择';

  @override
  String get textCardResetStyle => '重置样式';

  @override
  String get textCardResetStyleConfirm => '确定要清除所有文本样式吗？此操作不可撤销。';

  @override
  String get textCardExportAsImage => '导出为图片';

  @override
  String get textCardExportAsImageDesc => '保存这个卡片为图片';

  @override
  String get textCardEditCardDesc => '修改标题和内容';

  @override
  String get textCardDeleteCardDesc => '从文档中移除';

  @override
  String textCardDeleteCardConfirm(Object title) {
    return '确定要删除卡片\"$title\"吗？此操作无法撤销。';
  }

  @override
  String get textCardEditDocument => '编辑文档';

  @override
  String get textCardUniformStyle => '统一样式';

  @override
  String get textCardExportImage => '导出图片';

  @override
  String get textCardInsertSeparator => '插入分隔符';

  @override
  String get textCardPleaseEnterTitleAndCards => '请输入标题并确保至少有一个卡片';

  @override
  String textCardSaveFailed(Object error) {
    return '保存失败';
  }

  @override
  String get textCardContentRendering => '内容渲染';

  @override
  String textCardExportWithTitle(Object title) {
    return '导出 - $title';
  }

  @override
  String get textCardShare => '分享';

  @override
  String get textCardSaveToAlbum => '保存到相册';

  @override
  String get textCardUnderstood => '了解了';

  @override
  String get textCardStartExperience => '立即体验';

  @override
  String get textCardFeatureDemo => '功能演示';

  @override
  String get textCardGotIt => '知道了';

  @override
  String get textCardStartUsing => '开始使用';

  @override
  String get textCardPreviewEffect => '预览效果';

  @override
  String get textCardExportInfo => '导出信息';

  @override
  String get textCardImageDimensions => '图片尺寸';

  @override
  String get textCardAspectRatio => '宽高比';

  @override
  String get textCardFileSize => '文件大小';

  @override
  String get textCardUsageScenario => '适用场景';

  @override
  String get textCardBestQuality => '最佳质量 (100%)';

  @override
  String get textCardWatermarkDescription => '在图片底部添加应用标识';

  @override
  String get pdf => 'PDF';

  @override
  String get voice => '语音';

  @override
  String get html => 'HTML';

  @override
  String get svg => 'SVG';

  @override
  String get content => '内容';

  @override
  String get trafficGuide => '内容引流工具';

  @override
  String get create => '创建';

  @override
  String get edit => '编辑';

  @override
  String get delete => '删除';

  @override
  String get save => '保存';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确定';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get ok => '确定';

  @override
  String get loading => '加载中...';

  @override
  String get error => '错误';

  @override
  String get success => '成功';

  @override
  String get warning => '警告';

  @override
  String get info => '信息';

  @override
  String get search => '搜索';

  @override
  String get searchHint => '请输入搜索内容...';

  @override
  String get noResults => '未找到结果';

  @override
  String get tryAgain => '重试';

  @override
  String get refresh => '刷新';

  @override
  String get share => '分享';

  @override
  String get export => '导出';

  @override
  String get import => '导入';

  @override
  String get copy => '复制';

  @override
  String get paste => '粘贴';

  @override
  String get cut => '剪切';

  @override
  String get undo => '撤销';

  @override
  String get redo => '重做';

  @override
  String get selectAll => '全选';

  @override
  String get close => '关闭';

  @override
  String get back => '返回';

  @override
  String get next => '下一步';

  @override
  String get previous => '上一步';

  @override
  String get done => '完成';

  @override
  String get finish => '结束';

  @override
  String get skip => '跳过';

  @override
  String get continueAction => '继续';

  @override
  String get retry => '重试';

  @override
  String get reset => '重置';

  @override
  String get clear => '清除';

  @override
  String get apply => '应用';

  @override
  String get preview => '预览';

  @override
  String get download => '下载';

  @override
  String get upload => '上传';

  @override
  String get file => '文件';

  @override
  String get folder => '文件夹';

  @override
  String get name => '名称';

  @override
  String get title => '标题';

  @override
  String get description => '描述';

  @override
  String get size => '大小';

  @override
  String get date => '日期';

  @override
  String get time => '时间';

  @override
  String get type => '类型';

  @override
  String get status => '状态';

  @override
  String get version => '版本';

  @override
  String get author => '作者';

  @override
  String get tags => '标签';

  @override
  String get category => '分类';

  @override
  String get priority => '优先级';

  @override
  String get high => '高';

  @override
  String get medium => '中';

  @override
  String get low => '低';

  @override
  String get enabled => '已启用';

  @override
  String get disabled => '已禁用';

  @override
  String get online => '在线';

  @override
  String get offline => '离线';

  @override
  String get connected => '已连接';

  @override
  String get disconnected => '已断开';

  @override
  String get available => '可用';

  @override
  String get unavailable => '不可用';

  @override
  String get active => '有效';

  @override
  String get inactive => '非活跃';

  @override
  String get public => '公开';

  @override
  String get private => '私有';

  @override
  String get draft => '草稿';

  @override
  String get published => '已发布';

  @override
  String get archived => '已归档';

  @override
  String get pdfProfessionalTool => 'PDF 专业工具';

  @override
  String get pdfToolDescription => '强大的PDF处理能力，让文档管理更简单';

  @override
  String get securityEncryption => '安全加密';

  @override
  String get passwordProtectionPermissionControl => '密码保护\n权限控制';

  @override
  String get intelligentAnnotation => '智能注释';

  @override
  String get highlightMarkingTextAnnotation => '高亮标记\n文字批注';

  @override
  String get quickSearch => '快速搜索';

  @override
  String get fullTextSearchContentLocation => '全文检索\n内容定位';

  @override
  String get convenientSharing => '便捷分享';

  @override
  String get multipleFormatsOneClickExport => '多种格式\n一键导出';

  @override
  String get welcomeToPdfTool => '欢迎使用PDF专业工具！';

  @override
  String get importFirstPdfDocument => '导入第一个PDF文档';

  @override
  String get appearance => '外观';

  @override
  String get followSystem => '跟随系统';

  @override
  String get languageChangeEffect => '更改语言后，应用将立即生效';

  @override
  String get contentLibrary => '内容库';

  @override
  String get manageAllCards => '管理所有卡片';

  @override
  String get templateLibrary => '模板库';

  @override
  String get browseBeautifulTemplates => '浏览精美模板';

  @override
  String get inputTextToSplit => '输入文本';

  @override
  String get pasteOrInputLongText => '粘贴或输入长文本内容';

  @override
  String get pasteClipboard => '粘贴剪贴板';

  @override
  String get clearContent => '清空内容';

  @override
  String cardNumber(int number) {
    return '卡片 $number';
  }

  @override
  String get loadingDemoData => '正在加载演示数据...';

  @override
  String get modernUIDesign => '✨ 现代化UI设计\n🖼️ 渲染结果预览\n📱 分块模式支持\n⚡ 高性能体验';

  @override
  String editFunction(String title) {
    return '编辑功能：$title';
  }

  @override
  String deleted(String title) {
    return '已删除：$title';
  }

  @override
  String shareFunction(String title) {
    return '分享功能：$title';
  }

  @override
  String get createNewContent => '创建新内容';

  @override
  String get selectContentType => '选择您要创建的内容类型';

  @override
  String get bold => '**粗体**';

  @override
  String get italic => '*斜体*';

  @override
  String get heading1 => '# 标题1';

  @override
  String get heading2 => '## 标题2';

  @override
  String get heading3 => '### 标题3';

  @override
  String get list => '- 列表项\n- 列表项';

  @override
  String get link => '[链接文本](URL)';

  @override
  String get image => '![图片描述](图片URL)';

  @override
  String get code => '`代码`';

  @override
  String get codeBlock => '```\n代码块\n```';

  @override
  String get quote => '> 引用文本';

  @override
  String get table => '| 列1 | 列2 |\n| --- | --- |\n| 内容1 | 内容2 |';

  @override
  String get myContentLibrary => '我的内容库';

  @override
  String get manageAndBrowseContent => '管理和浏览您的所有内容';

  @override
  String get contentTools => '内容工具';

  @override
  String get recommendedTools => '推荐工具';

  @override
  String get markdownTitle => 'Markdown';

  @override
  String get markdownDescription => '文档编辑与渲染';

  @override
  String get textCardsTitle => '文本卡片';

  @override
  String get textCardsDescription => '知识卡片定制渲染';

  @override
  String get trafficGuideTitle => '内容引流工具';

  @override
  String get trafficGuideDescription => '内容引流图片与文本处理';

  @override
  String get fileTools => '文件工具';

  @override
  String get svgTitle => 'SVG';

  @override
  String get svgDescription => '矢量图形处理';

  @override
  String get htmlTitle => 'HTML';

  @override
  String get htmlDescription => '网页内容编辑';

  @override
  String get loadingContent => '正在加载内容...';

  @override
  String languageChangedTo(String language) {
    return '语言已更改为 $language';
  }

  @override
  String get developer => '开发者';

  @override
  String get contentLibraryDemo => '内容库演示';

  @override
  String get viewNewContentLibraryFeatures => '查看新的内容库功能';

  @override
  String get i18nDemo => '国际化演示';

  @override
  String get viewMultiLanguageSupport => '查看多语言支持效果';

  @override
  String get about => '关于';

  @override
  String get versionInfo => '版本信息';

  @override
  String get helpAndFeedback => '帮助与反馈';

  @override
  String get getHelpOrProvideFeedback => '获取帮助或提供反馈';

  @override
  String get helpAndFeedbackContent =>
      '如果您有任何问题或建议，请通过以下方式联系我们：\n\n邮箱：<EMAIL>';

  @override
  String get selectTheme => '选择主题';

  @override
  String get lightMode => '浅色模式';

  @override
  String get darkMode => '深色模式';

  @override
  String get systemMode => '跟随系统';

  @override
  String get personalizeYourAppExperience => '个性化您的应用体验';

  @override
  String get useDefaultInitialText => '使用默认初始文本';

  @override
  String get useDefaultInitialTextDescription => '进入模块时自动填充默认示例内容';

  @override
  String get contentSettings => '内容设置';

  @override
  String get trafficGuideImageGenerator => '引流图片生成器';

  @override
  String get trafficGuideImageGeneratorSubtitle => '生成方便在各平台使用的引流图片';

  @override
  String get trafficGuideTabText => '文本';

  @override
  String get trafficGuideTabTemplate => '模板';

  @override
  String get trafficGuideTabEffects => '效果';

  @override
  String get trafficGuideTextContent => '文本内容';

  @override
  String get trafficGuideTextHint => '输入要显示的文本';

  @override
  String get trafficGuideFontSettings => '字体设置';

  @override
  String get trafficGuideFontSize => '字体大小';

  @override
  String get trafficGuideColorSettings => '颜色设置';

  @override
  String get trafficGuideTextColor => '文字颜色';

  @override
  String get trafficGuideBackgroundColor => '背景颜色';

  @override
  String get trafficGuideVisualEffects => '视觉效果';

  @override
  String get trafficGuideNoiseLevel => '干扰程度';

  @override
  String get trafficGuideDistortionLevel => '扭曲程度';

  @override
  String get trafficGuideAddWatermark => '添加水印';

  @override
  String get trafficGuideWatermarkText => '水印文本';

  @override
  String get trafficGuideWatermarkHint => '输入水印内容...';

  @override
  String get trafficGuideExport => '导出';

  @override
  String get trafficGuideSelectTemplateFirst => '请先选择模板';

  @override
  String get trafficGuideImageSavedSuccess => '图片保存成功';

  @override
  String trafficGuideSaveFailed(Object error) {
    return '保存失败: $error';
  }

  @override
  String get trafficGuidePermissionPermanentlyDenied => '权限被永久拒绝';

  @override
  String get trafficGuidePermissionRequired => '需要权限';

  @override
  String trafficGuideSaveFailedWithMessage(Object message) {
    return '保存失败: $message';
  }

  @override
  String get trafficGuideSaveFailedEmptyResult => '保存失败: 空结果';

  @override
  String get markdownPreview => '预览';

  @override
  String get markdownContentLabel => 'Markdown内容';

  @override
  String get markdownRenderModeLabel => '渲染模式：';

  @override
  String get markdownNormalMode => '普通模式';

  @override
  String get markdownBlockMode => '分块模式';

  @override
  String get markdownConfigTab => '配置';

  @override
  String get markdownManageTab => '管理';

  @override
  String get markdownPreviewTab => '预览';

  @override
  String get markdownBlockInfo => '分块信息';

  @override
  String get markdownTotalBlocks => '总分块数';

  @override
  String get markdownVisibleBlocks => '可见分块';

  @override
  String get markdownEditorTitle => 'Markdown编辑器';

  @override
  String get markdownPreviewTitle => 'Markdown预览';

  @override
  String get markdownTitleLabel => '标题';

  @override
  String get markdownSubtitleLabel => '副标题（可选）';

  @override
  String get markdownUntitledDocument => '未命名文档';

  @override
  String get markdownUntitledSection => '未命名段落';

  @override
  String get markdownSplitSections => '拆分段落';

  @override
  String get markdownSaveDocument => '保存文档';

  @override
  String get markdownActionOptions => '操作选项';

  @override
  String get markdownShareImage => '分享图片';

  @override
  String get markdownCopyContent => '复制内容';

  @override
  String get markdownSaveToAlbum => '保存到相册';

  @override
  String get commonCancel => '取消';

  @override
  String get commonReset => '重置';

  @override
  String get commonSelectAll => '全选';

  @override
  String get commonDeselectAll => '取消全选';

  @override
  String get markdownShowSelected => '显示选中';

  @override
  String get markdownHideSelected => '隐藏选中';

  @override
  String get markdownExportSelected => '导出选中';

  @override
  String get markdownHideBlock => '隐藏分块';

  @override
  String get markdownShowBlock => '显示分块';

  @override
  String get markdownExportAsImage => '导出为图片';

  @override
  String get markdownExportAsMarkdown => '导出为Markdown';

  @override
  String get commonGotIt => '知道了';

  @override
  String get markdownBlockRenderSettings => '分块渲染设置';

  @override
  String get markdownBasicSettings => '基本设置';

  @override
  String get markdownEnableBlockRender => '启用分块渲染';

  @override
  String get markdownSeparatorSettings => '分隔符设置';

  @override
  String get markdownSplitByH1 => '按一级标题分隔';

  @override
  String get markdownSplitByH2 => '按二级标题分隔';

  @override
  String get markdownCustomSeparatorPattern => '自定义分隔符模式（正则表达式）';

  @override
  String get markdownAppearanceSettings => '外观设置';

  @override
  String get markdownBlockSpacing => '分块间距';

  @override
  String get markdownSectionSplitSettings => '段落拆分设置';

  @override
  String get markdownSplitByHorizontalRule => '按水平分割线拆分';

  @override
  String get markdownMaxSectionLength => '最大段落长度';

  @override
  String get commonUnlimited => '不限制';

  @override
  String get markdownSetMaxSectionLength => '设置最大段落长度';

  @override
  String get markdownMaxCharacters => '最大字符数';

  @override
  String get markdownLeaveEmptyUnlimited => '留空表示不限制';

  @override
  String get templateSimpleName => '简约';

  @override
  String get templateSimpleDescription => '简洁优雅的设计风格';

  @override
  String get templateModernName => '现代';

  @override
  String get templateModernDescription => '现代设计风格，带有阴影效果';

  @override
  String get templateElegantName => '优雅';

  @override
  String get templateElegantDescription => '优雅设计风格，带有细边框';

  @override
  String get templateCodeName => '代码';

  @override
  String get templateCodeDescription => '深色主题，适合代码展示';

  @override
  String get templateCardName => '卡片';

  @override
  String get templateCardDescription => '社交媒体卡片风格设计';

  @override
  String get templateMorandiName => '莫兰迪';

  @override
  String get templateMorandiDescription => '高级莫兰迪色系，柔和优雅';

  @override
  String get templateChineseBlueName => '青花';

  @override
  String get templateChineseBlueDescription => '传统中国青花瓷色彩与图案设计';

  @override
  String get templateChineseVermilionName => '中国红';

  @override
  String get templateChineseVermilionDescription => '传统中国朱红色彩，庄重典雅';

  @override
  String get templateGradientPurpleName => '渐变紫';

  @override
  String get templateGradientPurpleDescription => '现代紫蓝渐变背景，时尚优雅';

  @override
  String get templateFestiveRedName => '喜庆红';

  @override
  String get templateFestiveRedDescription => '喜庆主题，适合春节等场合';

  @override
  String get templateBambooSlipName => '竹简';

  @override
  String get templateBambooSlipDescription => '传统竹简风格，富有古韵';

  @override
  String get watermarkPositionTopLeft => '左上';

  @override
  String get watermarkPositionTopCenter => '上中';

  @override
  String get watermarkPositionTopRight => '右上';

  @override
  String get watermarkPositionBottomLeft => '左下';

  @override
  String get watermarkPositionBottomCenter => '下中';

  @override
  String get watermarkPositionBottomRight => '右下';

  @override
  String get watermarkPositionTiled => '平铺';

  @override
  String get markdownEnterContentFirst => '请先输入Markdown内容';

  @override
  String markdownSplitSuccess(int count) {
    return '成功拆分为$count个段落';
  }

  @override
  String markdownSplitError(String error) {
    return '拆分段落时出错：$error';
  }

  @override
  String get markdownNoSectionsToPreview => '没有可预览的段落';

  @override
  String get markdownSplitContentFirst => '请先拆分Markdown内容';

  @override
  String get markdownDocumentSaveSuccess => '文档保存成功';

  @override
  String markdownDocumentSaveError(String error) {
    return '保存文档失败：$error';
  }

  @override
  String get errorStoragePermissionRequired => '需要存储权限才能保存图片';

  @override
  String get markdownNoContentToPreview => '没有内容可预览';

  @override
  String get markdownImageGenerationFailed => '图片生成失败，无法分享';

  @override
  String markdownShareError(String error) {
    return '分享失败：$error';
  }

  @override
  String get markdownEnableBlockModeFirst => '请先启用分块模式';

  @override
  String get markdownNoBlocks => '暂无分块';

  @override
  String get markdownEnableBlockRenderToList => '启用分块渲染以查看分块列表';

  @override
  String get commonContentCopied => '内容已复制到剪贴板';

  @override
  String get markdownResetDemo => '重置演示';

  @override
  String get commonHelp => '帮助';

  @override
  String get markdownBlockRenderHelp => '分块渲染帮助';

  @override
  String get markdownFeatureDescription => '功能说明：';

  @override
  String get markdownOperationMethod => '操作方法：';

  @override
  String get commonTips => '提示：';

  @override
  String get markdownSectionSettings => '段落设置';

  @override
  String get markdownSelectTemplate => '选择模板';

  @override
  String get markdownSelectHtmlTemplate => '选择HTML模板';

  @override
  String get commonPreview => '预览';

  @override
  String get markdownSaveImage => '保存图片';

  @override
  String get commonShare => '分享';

  @override
  String get commonShowAll => '显示全部';

  @override
  String get commonShowVisibleOnly => '仅显示可见';

  @override
  String get commonSortByIndex => '按索引排序';

  @override
  String get commonSortByTitle => '按标题排序';

  @override
  String get commonSortByType => '按类型排序';

  @override
  String get commonSortByLength => '按长度排序';

  @override
  String markdownBlockCount(int count) {
    return '$count个分块';
  }

  @override
  String commonCharacterCount(int count) {
    return '$count个字符';
  }

  @override
  String markdownSelectedBlockCount(int count) {
    return '已选择$count个分块';
  }

  @override
  String get commonTotal => '总计';

  @override
  String get commonVisible => '可见';

  @override
  String get commonHidden => '隐藏';

  @override
  String get markdownContentPlaceholder => '在这里输入Markdown内容...';

  @override
  String get markdownClickSplitButton => '点击拆分按钮将Markdown拆分为段落';

  @override
  String get markdownHorizontalRuleHelper => '遇到三个以上的-、*或_符号时拆分';

  @override
  String get markdownH1SplitHelper => '遇到#一级标题时拆分';

  @override
  String markdownCharacterCount(int count) {
    return '$count个字符';
  }

  @override
  String get markdownAutoSplitHelper => '将过长的段落自动拆分为多个段落';

  @override
  String get markdownSeparatorExample => '示例：三个或更多连续的短横线';

  @override
  String get markdownH1SeparatorHelper => '使用#一级标题作为分块分隔符';

  @override
  String get markdownH2SeparatorHelper => '使用##二级标题作为分块分隔符';

  @override
  String get markdownBlockRenderHelper => '启用后，Markdown内容将按照设定的规则以分块形式显示';

  @override
  String get markdownExportBlocks => '导出分块';

  @override
  String get markdownGenerateSummary => '生成摘要报告';

  @override
  String markdownImageExportSuccess(String filePath) {
    return '图片已导出：$filePath';
  }

  @override
  String markdownExportError(String error) {
    return '导出失败：$error';
  }

  @override
  String markdownMarkdownExportSuccess(String filePath) {
    return 'Markdown文件已导出：$filePath';
  }

  @override
  String markdownSummaryGenerated(String filePath) {
    return '摘要报告已生成：$filePath';
  }

  @override
  String markdownSummaryError(String error) {
    return '生成报告失败：$error';
  }

  @override
  String markdownGeneratingImage(int current, int total) {
    return '正在生成图片（$current/$total）';
  }

  @override
  String markdownImagesSavedSuccess(int count) {
    return '成功保存$count张图片到相册';
  }

  @override
  String get templateChineseBlueWatermark => '青花';

  @override
  String get templateChineseVermilionWatermark => '赤';

  @override
  String get templateFestiveRedWatermark => '福';

  @override
  String get templateBambooSlipWatermark => '竹';

  @override
  String get markdownBlockManagement => '分块管理';

  @override
  String get markdownExportOptions => '导出选项';

  @override
  String get commonDeselect => '取消选择';

  @override
  String get commonSelect => '选择';

  @override
  String get commonLoading => '加载中...';

  @override
  String get commonConfirm => '确认';

  @override
  String get commonEdit => '编辑';

  @override
  String get commonDelete => '删除';

  @override
  String get commonAdd => '添加';

  @override
  String get commonRemove => '移除';

  @override
  String get commonApply => '应用';

  @override
  String get commonClose => '关闭';

  @override
  String get commonOpen => '打开';

  @override
  String get commonView => '查看';

  @override
  String get commonBrowse => '浏览';

  @override
  String get commonSearch => '搜索';

  @override
  String get commonFilter => '筛选';

  @override
  String get commonSort => '排序';

  @override
  String get commonRefresh => '刷新';

  @override
  String get commonReload => '重新加载';

  @override
  String get commonRetry => '重试';

  @override
  String get commonContinue => '继续';

  @override
  String get commonFinish => '完成';

  @override
  String get commonSkip => '跳过';

  @override
  String get commonBack => '返回';

  @override
  String get commonNext => '下一步';

  @override
  String get commonPrevious => '上一步';

  @override
  String get commonDone => '完成';

  @override
  String get commonStart => '开始';

  @override
  String get commonStop => '停止';

  @override
  String get commonPause => '暂停';

  @override
  String get commonResume => '恢复';

  @override
  String get commonPlay => '播放';

  @override
  String get commonMute => '静音';

  @override
  String get commonUnmute => '取消静音';

  @override
  String get commonVolumeUp => '增加音量';

  @override
  String get commonVolumeDown => '减少音量';

  @override
  String get commonFullscreen => '全屏';

  @override
  String get commonExitFullscreen => '退出全屏';

  @override
  String get commonZoomIn => '放大';

  @override
  String get commonZoomOut => '缩小';

  @override
  String get commonZoomReset => '重置缩放';

  @override
  String get commonRotateLeft => '向左旋转';

  @override
  String get commonRotateRight => '向右旋转';

  @override
  String get commonFlipHorizontal => '水平翻转';

  @override
  String get commonFlipVertical => '垂直翻转';

  @override
  String get commonCrop => '裁剪';

  @override
  String get commonResize => '调整大小';

  @override
  String get commonRotate => '旋转';

  @override
  String get commonFlip => '翻转';

  @override
  String get commonMirror => '镜像';

  @override
  String get commonSkew => '倾斜';

  @override
  String get commonDistort => '扭曲';

  @override
  String get commonBlur => '模糊';

  @override
  String get commonSharpen => '锐化';

  @override
  String get commonBrightness => '亮度';

  @override
  String get commonContrast => '对比度';

  @override
  String get commonSaturation => '饱和度';

  @override
  String get commonHue => '色相';

  @override
  String get commonGamma => '伽马';

  @override
  String get commonExposure => '曝光';

  @override
  String get commonVignette => '暗角';

  @override
  String get commonGrain => '颗粒';

  @override
  String get commonNoise => '噪点';

  @override
  String get commonPixelate => '像素化';

  @override
  String get commonPosterize => '色调分离';

  @override
  String get commonDither => '抖动';

  @override
  String get commonThreshold => '阈值';

  @override
  String get commonQuantize => '量化';

  @override
  String get commonDesaturate => '去饱和';

  @override
  String get commonSaturate => '饱和';

  @override
  String get commonInvert => '反色';

  @override
  String get commonGrayscale => '灰度';

  @override
  String get commonSepia => '褐色';

  @override
  String get commonVintage => '复古';

  @override
  String get commonRetro => '怀旧';

  @override
  String get commonBlackAndWhite => '黑白';

  @override
  String get commonCool => '冷色';

  @override
  String get commonWarm => '暖色';

  @override
  String get commonFade => '褪色';

  @override
  String get commonDuotone => '双色调';

  @override
  String get commonTricolor => '三色调';

  @override
  String get commonMonochrome => '单色';

  @override
  String get commonPolychrome => '多色';

  @override
  String get commonRainbow => '彩虹';

  @override
  String get commonGradient => '渐变';

  @override
  String get commonPattern => '图案';

  @override
  String get commonTexture => '纹理';

  @override
  String get commonBorder => '边框';

  @override
  String get commonFrame => '框架';

  @override
  String get commonShadow => '阴影';

  @override
  String get commonGlow => '发光';

  @override
  String get commonNeon => '霓虹';

  @override
  String get commonLight => '亮光';

  @override
  String get commonDark => '暗光';

  @override
  String get commonBright => '明亮';

  @override
  String get commonDim => '暗淡';

  @override
  String get commonClear => '清晰';

  @override
  String get commonCloudy => '多云';

  @override
  String get commonFoggy => '有雾';

  @override
  String get commonHazy => '朦胧';

  @override
  String get commonSmoky => '烟雾';

  @override
  String get commonDusty => '灰尘';

  @override
  String get commonMisty => '薄雾';

  @override
  String get commonFrosty => '霜冻';

  @override
  String get commonIcy => '结冰';

  @override
  String get commonSnowy => '下雪';

  @override
  String get commonRainy => '下雨';

  @override
  String get commonStormy => '暴风雨';

  @override
  String get commonWindy => '刮风';

  @override
  String get commonBreezy => '微风';

  @override
  String get commonCalm => '平静';

  @override
  String get commonStill => '静止';

  @override
  String get commonQuiet => '安静';

  @override
  String get commonSilent => '沉默';

  @override
  String get commonPeaceful => '和平';

  @override
  String get commonSerene => '宁静';

  @override
  String get commonTranquil => '安详';

  @override
  String get commonPlacid => '平稳';

  @override
  String get commonSmooth => '平滑';

  @override
  String get commonRough => '粗糙';

  @override
  String get commonCoarse => '粗糙';

  @override
  String get commonFine => '精细';

  @override
  String get commonSoft => '柔软';

  @override
  String get commonHard => '困难';

  @override
  String get commonTough => '坚韧';

  @override
  String get commonStrong => '强壮';

  @override
  String get commonWeak => '虚弱';

  @override
  String get commonGentle => '温柔';

  @override
  String get commonMild => '温和';

  @override
  String get commonHarsh => '严厉';

  @override
  String get commonSevere => '严重';

  @override
  String get commonExtreme => '极端';

  @override
  String get commonIntense => '强烈';

  @override
  String get commonModerate => '适中';

  @override
  String get commonAverage => '平均';

  @override
  String get commonNormal => '正常';

  @override
  String get commonStandard => '标准';

  @override
  String get commonRegular => '常规';

  @override
  String get commonTypical => '典型';

  @override
  String get commonUsual => '通常';

  @override
  String get commonCommon => '常见';

  @override
  String get commonOrdinary => '普通';

  @override
  String get commonGeneral => '通用';

  @override
  String get commonBasic => '基本';

  @override
  String get commonSimple => '简单';

  @override
  String get commonEasy => '容易';

  @override
  String get commonDifficult => '困难';

  @override
  String get commonComplex => '复杂';

  @override
  String get commonComplicated => '复杂';

  @override
  String get commonAdvanced => '高级';

  @override
  String get commonExpert => '专家';

  @override
  String get commonProfessional => '专业';

  @override
  String get commonSpecialized => '专门';

  @override
  String get commonTechnical => '技术';

  @override
  String get commonScientific => '科学';

  @override
  String get commonAcademic => '学术';

  @override
  String get commonEducational => '教育';

  @override
  String get commonInstructional => '指导';

  @override
  String get commonTutorial => '教程';

  @override
  String get commonGuide => '引导';

  @override
  String get commonManual => '手册';

  @override
  String get commonHandbook => '手册';

  @override
  String get commonReference => '参考';

  @override
  String get commonDocumentation => '文档';

  @override
  String get commonSupport => '支持';

  @override
  String get commonAssistance => '协助';

  @override
  String get commonAid => '援助';

  @override
  String get commonService => '服务';

  @override
  String get commonMaintenance => '维护';

  @override
  String get commonRepair => '修复';

  @override
  String get commonFix => '修复';

  @override
  String get commonSolve => '解决';

  @override
  String get commonResolve => '解决';

  @override
  String get commonAddress => '地址';

  @override
  String get commonHandle => '处理';

  @override
  String get commonManage => '管理';

  @override
  String get commonControl => '控制';

  @override
  String get commonDirect => '指导';

  @override
  String get commonLead => '领导';

  @override
  String get commonConduct => '进行';

  @override
  String get commonOperate => '操作';

  @override
  String get commonRun => '运行';

  @override
  String get commonExecute => '执行';

  @override
  String get commonPerform => '执行';

  @override
  String get commonImplement => '实施';

  @override
  String get commonCarryOut => '执行';

  @override
  String get commonAccomplish => '完成';

  @override
  String get commonAchieve => '实现';

  @override
  String get commonAttain => '达到';

  @override
  String get commonReach => '到达';

  @override
  String get commonObtain => '获得';

  @override
  String get commonGet => '获取';

  @override
  String get commonAcquire => '获取';

  @override
  String get commonGain => '获得';

  @override
  String get commonReceive => '接收';

  @override
  String get commonCollect => '收集';

  @override
  String get commonGather => '聚集';

  @override
  String get commonAssemble => '组装';

  @override
  String get commonCompile => '编译';

  @override
  String get commonCombine => '结合';

  @override
  String get commonMerge => '合并';

  @override
  String get commonJoin => '加入';

  @override
  String get commonUnite => '联合';

  @override
  String get commonConnect => '连接';

  @override
  String get commonLink => '链接';

  @override
  String get commonAttach => '附加';

  @override
  String get commonFasten => '固定';

  @override
  String get commonSecure => '保护';

  @override
  String get commonTie => '捆绑';

  @override
  String get commonBind => '绑定';

  @override
  String get commonWrap => '包裹';

  @override
  String get commonCover => '覆盖';

  @override
  String get commonEnclose => '包围';

  @override
  String get commonSurround => '环绕';

  @override
  String get commonEnvelop => '包裹';

  @override
  String get commonContain => '包含';

  @override
  String get commonInclude => '包括';

  @override
  String get commonInvolve => '涉及';

  @override
  String get commonEmbrace => '拥抱';

  @override
  String get commonEncompass => '包含';

  @override
  String get commonSpan => '跨度';

  @override
  String get commonExtend => '扩展';

  @override
  String get commonStretch => '拉伸';

  @override
  String get commonExpand => '扩展';

  @override
  String get commonGrow => '成长';

  @override
  String get commonIncrease => '增加';

  @override
  String get commonEnlarge => '放大';

  @override
  String get commonMagnify => '放大';

  @override
  String get commonAmplify => '放大';

  @override
  String get commonBoost => '提升';

  @override
  String get commonEnhance => '增强';

  @override
  String get commonImprove => '改进';

  @override
  String get commonBetter => '更好';

  @override
  String get commonUpgrade => '升级';

  @override
  String get commonAdvance => '前进';

  @override
  String get commonProgress => '进步';

  @override
  String get commonDevelop => '发展';

  @override
  String get commonEvolve => '进化';

  @override
  String get commonMature => '成熟';

  @override
  String get commonRipe => '成熟';

  @override
  String get commonPerfect => '完美';

  @override
  String get complete => '完成';

  @override
  String get commonComplete => '完成';

  @override
  String get commonEnd => '结束';

  @override
  String get commonTerminate => '终止';

  @override
  String get commonConclude => '总结';

  @override
  String get commonFinalize => '最终确定';

  @override
  String get commonShut => '关闭';

  @override
  String get commonSeal => '密封';

  @override
  String get commonLock => '锁定';

  @override
  String get commonTighten => '收紧';

  @override
  String get commonOrganize => '组织';

  @override
  String get commonArrange => '安排';

  @override
  String get commonOrder => '订单';

  @override
  String get commonClassify => '分类';

  @override
  String get commonCategorize => '归类';

  @override
  String get commonGroup => '分组';

  @override
  String get commonCluster => '集群';

  @override
  String get commonBunch => '束';

  @override
  String get commonBundle => '捆绑';

  @override
  String get commonPack => '打包';

  @override
  String get commonPackage => '包装';

  @override
  String get commonHold => '持有';

  @override
  String get commonCarry => '携带';

  @override
  String get commonBear => '承受';

  @override
  String get commonSustain => '维持';

  @override
  String get commonMaintain => '维护';

  @override
  String get commonKeep => '保持';

  @override
  String get commonRetain => '保留';

  @override
  String get commonPreserve => '保存';

  @override
  String get commonConserve => '保护';

  @override
  String get commonSave => '保存';

  @override
  String get commonStore => '存储';

  @override
  String get commonReserve => '保留';

  @override
  String get commonSetAside => '留出';

  @override
  String get commonPutAway => '收起';

  @override
  String get commonPlace => '放置';

  @override
  String get commonPosition => '位置';

  @override
  String get commonLocate => '定位';

  @override
  String get commonSituate => '安置';

  @override
  String get commonInstall => '安装';

  @override
  String get commonSet => '设置';

  @override
  String get commonEstablish => '建立';

  @override
  String get commonFound => '建立';

  @override
  String get commonCreate => '创建';

  @override
  String get commonMake => '制作';

  @override
  String get commonBuild => '构建';

  @override
  String get commonConstruct => '构建';

  @override
  String get commonForm => '形成';

  @override
  String get commonShape => '塑造';

  @override
  String get commonMold => '塑造';

  @override
  String get commonFashion => '时尚';

  @override
  String get commonDesign => '设计';

  @override
  String get commonPlan => '计划';

  @override
  String get commonDevise => '设计';

  @override
  String get commonConceive => '构思';

  @override
  String get commonImagine => '想象';

  @override
  String get commonEnvision => '设想';

  @override
  String get commonVisualize => '可视化';

  @override
  String get commonDream => '梦想';

  @override
  String get commonThink => '思考';

  @override
  String get commonConsider => '考虑';

  @override
  String get commonPonder => '思考';

  @override
  String get commonReflect => '反思';

  @override
  String get commonMeditate => '冥想';

  @override
  String get commonContemplate => '沉思';

  @override
  String get commonStudy => '学习';

  @override
  String get commonLearn => '学习';

  @override
  String get commonDiscover => '发现';

  @override
  String get commonFind => '查找';

  @override
  String get commonUncover => '揭露';

  @override
  String get commonReveal => '揭示';

  @override
  String get commonExpose => '暴露';

  @override
  String get commonDisclose => '披露';

  @override
  String get commonDivulge => '泄露';

  @override
  String get commonTell => '告诉';

  @override
  String get commonInform => '通知';

  @override
  String get commonNotify => '通知';

  @override
  String get commonAnnounce => '宣布';

  @override
  String get commonDeclare => '声明';

  @override
  String get commonProclaim => '宣告';

  @override
  String get commonPronounce => '发音';

  @override
  String get commonState => '状态';

  @override
  String get commonExpress => '表达';

  @override
  String get commonVoice => '声音';

  @override
  String get commonArticulate => '清晰表达';

  @override
  String get commonUtter => '发出';

  @override
  String get commonSay => '说';

  @override
  String get commonSpeak => '说话';

  @override
  String get commonTalk => '谈话';

  @override
  String get commonConverse => '交谈';

  @override
  String get commonCommunicate => '交流';

  @override
  String get commonCorrespond => '对应';

  @override
  String get commonContact => '联系';

  @override
  String get commonApproach => '接近';

  @override
  String get commonAccost => '搭讪';

  @override
  String get commonGreet => '问候';

  @override
  String get commonWelcome => '欢迎';

  @override
  String get commonAccept => '接受';

  @override
  String get commonTake => '拿';

  @override
  String get commonProcure => '获得';

  @override
  String get commonPurchase => '购买';

  @override
  String get commonBuy => '购买';

  @override
  String get commonShop => '购物';

  @override
  String get commonTrade => '交易';

  @override
  String get commonExchange => '交换';

  @override
  String get commonSwap => '交换';

  @override
  String get commonSwitch => '切换';

  @override
  String get commonChange => '改变';

  @override
  String get commonAlter => '改变';

  @override
  String get commonModify => '修改';

  @override
  String get commonAdjust => '调整';

  @override
  String get commonTweak => '微调';

  @override
  String get commonFineTune => '微调';

  @override
  String get commonOptimize => '优化';

  @override
  String get commonRefine => '精炼';

  @override
  String get commonPolish => '润色';

  @override
  String get commonCease => '停止';

  @override
  String get commonHalt => '停止';

  @override
  String get commonBreak => '中断';

  @override
  String get commonInterrupt => '中断';

  @override
  String get commonSuspend => '暂停';

  @override
  String get commonDelay => '延迟';

  @override
  String get commonPostpone => '推迟';

  @override
  String get commonDefer => '推迟';

  @override
  String get commonWait => '等待';

  @override
  String get commonRemain => '保持';

  @override
  String get commonStay => '停留';

  @override
  String get commonProceed => '继续';

  @override
  String get commonMove => '移动';

  @override
  String get commonGo => '去';

  @override
  String get commonTravel => '旅行';

  @override
  String get commonJourney => '旅程';

  @override
  String get commonPass => '通过';

  @override
  String get commonCross => '穿过';

  @override
  String get commonTransit => '运输';

  @override
  String get commonTransfer => '转移';

  @override
  String get commonConvey => '传达';

  @override
  String get commonTransport => '运输';

  @override
  String get commonBring => '带来';

  @override
  String get commonFetch => '取';

  @override
  String get commonSalute => '致敬';

  @override
  String get commonHail => '欢呼';

  @override
  String get commonNigh => '接近';

  @override
  String get commonDrawNear => '靠近';

  @override
  String get commonCome => '来';

  @override
  String get commonArrive => '到达';

  @override
  String get commonLand => '着陆';

  @override
  String get commonEnter => '进入';

  @override
  String get commonAccess => '访问';

  @override
  String get commonGoOn => 'Go On';

  @override
  String get commonKeepOn => 'Keep On';

  @override
  String get commonCarryOn => 'Carry On';

  @override
  String get commonPersist => 'Persist';

  @override
  String get commonPersevere => 'Persevere';

  @override
  String get commonEndure => 'Endure';

  @override
  String get commonLast => 'Last';

  @override
  String get markdownSaveButton => '保存';

  @override
  String get markdownCancelButton => '取消';

  @override
  String get markdownConfirmButton => '确认';

  @override
  String get markdownResetButton => '重置';

  @override
  String get markdownApplyButton => '应用';

  @override
  String get markdownCloseButton => '关闭';

  @override
  String get markdownSelectButton => '选择';

  @override
  String get markdownBrowseButton => '浏览';

  @override
  String get markdownSearchButton => '搜索';

  @override
  String get markdownClearButton => '清除';

  @override
  String get markdownDeleteButton => '删除';

  @override
  String get markdownEditButton => '编辑';

  @override
  String get markdownExportButton => '导出';

  @override
  String get markdownImportButton => '导入';

  @override
  String get markdownShareButton => '分享';

  @override
  String get markdownCopyButton => '复制';

  @override
  String get markdownPasteButton => '粘贴';

  @override
  String get markdownCutButton => '剪切';

  @override
  String get markdownUndoButton => '撤销';

  @override
  String get markdownRedoButton => '重做';

  @override
  String get markdownEditTab => '编辑';

  @override
  String get markdownTemplateTab => '模板';

  @override
  String get markdownStyleTab => '样式';

  @override
  String get markdownWatermarkTab => '水印';

  @override
  String get markdownBlockTab => '分块';

  @override
  String get markdownTemplateSelector => '模板选择器';

  @override
  String get markdownStyleSelector => '样式选择器';

  @override
  String get markdownWatermarkSettings => '水印设置';

  @override
  String get markdownBlockSettings => '分块设置';

  @override
  String get markdownBlockConfigPanel => '分块配置面板';

  @override
  String get markdownBlockManagerPanel => '分块管理面板';

  @override
  String get markdownTextLabel => '文本';

  @override
  String get markdownMarkdownContent => 'Markdown内容';

  @override
  String get markdownWatermarkText => '水印文本';

  @override
  String get markdownEnterWatermarkText => '请输入水印文本';

  @override
  String get markdownEnterMarkdownContent => '请输入Markdown内容...';

  @override
  String get markdownFontSettings => '字体设置';

  @override
  String get markdownFontSize => '字体大小';

  @override
  String get markdownFontFamily => '字体族';

  @override
  String get markdownCodeFont => '代码字体';

  @override
  String get markdownColorSettings => '颜色设置';

  @override
  String get markdownTextColor => '文字颜色';

  @override
  String get markdownBackgroundColor => '背景颜色';

  @override
  String get markdownBorderColor => '边框颜色';

  @override
  String get markdownBorderWidth => '边框宽度';

  @override
  String get markdownShadowSettings => '阴影设置';

  @override
  String get markdownShadowColor => '阴影颜色';

  @override
  String get markdownBorderRadius => '边框圆角';

  @override
  String get markdownPadding => '内边距';

  @override
  String get markdownMargin => '外边距';

  @override
  String get markdownWatermarkContent => '水印内容';

  @override
  String get markdownWatermarkTextStyle => '文字样式';

  @override
  String get markdownWatermarkNormal => '普通';

  @override
  String get markdownWatermarkBold => '粗体';

  @override
  String get markdownWatermarkItalic => '斜体';

  @override
  String get markdownWatermarkPosition => '显示位置';

  @override
  String get markdownWatermarkTextColor => '文本颜色';

  @override
  String get markdownWatermarkOpacity => '透明度';

  @override
  String get markdownWatermarkFontSize => '字体大小';

  @override
  String get markdownWatermarkRotation => '旋转角度';

  @override
  String get markdownWatermarkTileSettings => '平铺设置';

  @override
  String get markdownWatermarkHorizontalSpacing => '水平间距';

  @override
  String get markdownWatermarkVerticalSpacing => '垂直间距';

  @override
  String get markdownSelectWatermarkColor => '选择水印颜色';

  @override
  String get markdownResetToAppName => '重置为应用名称';

  @override
  String get markdownShowBlockTitle => '显示分块标题';

  @override
  String get markdownShowBlockBorder => '显示分块边框';

  @override
  String get markdownSortByIndex => '按索引排序';

  @override
  String get markdownSortByTitle => '按标题排序';

  @override
  String get markdownSortByType => '按类型排序';

  @override
  String get markdownSortByLength => '按长度排序';

  @override
  String get markdownShareResult => '分享结果';

  @override
  String get markdownExportResult => '导出结果';

  @override
  String get markdownSaveSuccess => '保存成功';

  @override
  String get markdownSaveFailed => '保存失败';

  @override
  String get markdownTemplateDescription => '模板描述';

  @override
  String get markdownTemplateFeatures => '模板特性';

  @override
  String get markdownBorderStyle => '边框样式';

  @override
  String get markdownShadowEffect => '阴影效果';

  @override
  String get markdownShowHeader => '显示标题';

  @override
  String get markdownInnerShadow => '内阴影';

  @override
  String get markdownHeadingAlignment => '标题对齐';

  @override
  String get markdownLeftAlign => '左对齐';

  @override
  String get markdownCenterAlign => '居中对齐';

  @override
  String get markdownRightAlign => '右对齐';

  @override
  String get markdownGradientBackground => '渐变背景';

  @override
  String get markdownBackgroundPattern => '背景图案';

  @override
  String get markdownListItemStyle => '列表项样式';

  @override
  String get markdownCheckboxStyle => '复选框样式';

  @override
  String get markdownMoreActions => '更多操作';

  @override
  String get markdownShareImageSubtitle => '将渲染结果分享给他人';

  @override
  String get markdownCopyContentSubtitle => '将Markdown文本复制到剪贴板';

  @override
  String get markdownSaveToAlbumSubtitle => '将图片保存到本地相册';

  @override
  String get markdownOperationOptions => '操作选项';

  @override
  String get markdownSelectColor => '选择颜色';

  @override
  String get markdownChooseColor => '选择颜色';

  @override
  String get markdownColorPicker => '颜色选择器';

  @override
  String get markdownResetSettings => '重置设置';

  @override
  String get markdownApplySettings => '应用设置';

  @override
  String get markdownLoading => '加载中...';

  @override
  String get markdownGenerating => '生成中...';

  @override
  String get markdownProcessing => '处理中...';

  @override
  String get markdownSaving => '保存中...';

  @override
  String get markdownExporting => '导出中...';

  @override
  String get markdownSharing => '分享中...';

  @override
  String get markdownCopying => '复制中...';

  @override
  String get markdownSuccess => '成功';

  @override
  String get markdownError => '错误';

  @override
  String get markdownWarning => '警告';

  @override
  String get markdownInfo => '信息';

  @override
  String get markdownComplete => '完成';

  @override
  String get markdownFailed => '失败';

  @override
  String get markdownCancelled => '已取消';

  @override
  String get markdownContentSaved => '内容已保存到内容库';

  @override
  String markdownTemplateSelected(String name) {
    return '已选择模板 \"$name\"';
  }

  @override
  String markdownSaveError(String error) {
    return '保存失败：$error';
  }

  @override
  String markdownLoadError(String error) {
    return '加载错误：$error';
  }

  @override
  String markdownProcessError(String error) {
    return '处理错误：$error';
  }

  @override
  String get markdownBlockModeEnabled => '分块模式已启用';

  @override
  String get markdownBlockModeDisabled => '分块模式已禁用';

  @override
  String get markdownBlockAdded => '分块已添加';

  @override
  String get markdownBlockRemoved => '分块已移除';

  @override
  String get markdownBlockUpdated => '分块已更新';

  @override
  String get markdownBlockHidden => '分块已隐藏';

  @override
  String get markdownBlockShown => '分块已显示';

  @override
  String get markdownBlockSelected => '分块已选择';

  @override
  String get markdownBlockDeselected => '分块已取消选择';

  @override
  String get markdownBlockMoved => '分块已移动';

  @override
  String get markdownBlockResized => '分块已调整大小';

  @override
  String get markdownBlockReordered => '分块已重新排序';

  @override
  String get markdownBlockExported => '分块已导出';

  @override
  String get markdownBlockImported => '分块已导入';

  @override
  String get markdownBlockRenderFeature1 => '• 分块渲染可以将长文档拆分为多个独立的分块';

  @override
  String get markdownBlockRenderFeature2 => '• 每个分块可以独立显示或隐藏';

  @override
  String get markdownBlockRenderFeature3 => '• 支持多种分隔方式：标题、自定义分隔符、手动分隔';

  @override
  String get markdownBlockRenderFeature4 => '• 在左侧配置面板中调整分块设置';

  @override
  String get markdownBlockRenderFeature5 => '• 在预览区域点击可以添加新的分隔条';

  @override
  String get markdownBlockRenderFeature6 => '• 拖动分隔条可以重新调整分块位置';

  @override
  String get markdownBlockRenderFeature7 => '• 点击分块标题栏的眼睛图标可以隐藏/显示分块';

  @override
  String get markdownBlockRenderFeature8 => '• 不同类型的分块用不同颜色的边框区分';

  @override
  String get markdownBlockRenderFeature9 => '• 蓝色：H1标题分块';

  @override
  String get markdownBlockRenderFeature10 => '• 绿色：H2标题分块';

  @override
  String get markdownBlockRenderFeature11 => '• 橙色：自定义分隔符分块';

  @override
  String get markdownBlockRenderFeature12 => '• 灰色：手动分隔符分块';

  @override
  String get markdownGotIt => '知道了';

  @override
  String get markdownIKnow => '我知道了';

  @override
  String get markdownUnderstood => '了解了';

  @override
  String get markdownAlignLeft => '左对齐';

  @override
  String get markdownAlignCenter => '居中对齐';

  @override
  String get markdownAlignRight => '右对齐';

  @override
  String get markdownPositionTopLeft => '左上';

  @override
  String get markdownPositionTopCenter => '上中';

  @override
  String get markdownPositionTopRight => '右上';

  @override
  String get markdownPositionBottomLeft => '左下';

  @override
  String get markdownPositionBottomCenter => '下中';

  @override
  String get markdownPositionBottomRight => '右下';

  @override
  String get markdownPositionTiled => '平铺';

  @override
  String get markdownExportingBlocks => '正在导出分块...';

  @override
  String get markdownGeneratingReport => '正在生成报告...';

  @override
  String get markdownProcessingComplete => '处理完成';

  @override
  String get markdownOperationSuccessful => '操作成功';

  @override
  String get markdownOperationFailed => '操作失败';

  @override
  String get markdownWatermarkVisible => '显示水印';

  @override
  String get markdownWatermarkHidden => '隐藏水印';

  @override
  String get markdownWatermarkPositionAppearance => '位置与外观';

  @override
  String get markdownWatermarkDisplayPosition => '显示位置';

  @override
  String get markdownWatermarkHorizontalGap => '水平间距';

  @override
  String get markdownWatermarkVerticalGap => '垂直间距';

  @override
  String get markdownWatermarkSelectColor => '选择水印颜色';

  @override
  String get markdownWatermarkCancel => '取消';

  @override
  String get markdownWatermarkConfirm => '确定';

  @override
  String voiceInitializationFailed(Object error) {
    return '初始化失败';
  }

  @override
  String get voicePlayingAllRecordings => '正在播放所有录音';

  @override
  String get voiceMyRecordings => '我的录音';

  @override
  String get voicePlayAll => '播放所有';

  @override
  String get voiceNoRecordings => '暂无语音记录';

  @override
  String get voiceStartRecording => '开始录制';

  @override
  String get voiceTapToStartRecording => '点击下方按钮开始录制您的第一条语音';

  @override
  String get voiceConfirmDelete => '确认删除';

  @override
  String get voiceDeleteConfirmation => '确定要删除这条语音记录吗？';

  @override
  String get voiceCancel => '取消';

  @override
  String get voiceDelete => '删除';

  @override
  String get voiceRecordingDeleted => '记录已删除';

  @override
  String get voiceTranscriptionContent => '转录内容:';

  @override
  String get voiceToday => '今天';

  @override
  String get voiceYesterday => '昨天';

  @override
  String get voiceRecording => '录音';

  @override
  String get voiceRecordingPageTitle => '语音录制';

  @override
  String get voiceRequestPermission => '请求权限';

  @override
  String get voiceOpenSettings => '打开设置';

  @override
  String get voiceRecordingInProgress => '正在录音...';

  @override
  String get voiceReadyToRecord => '准备好开始录音';

  @override
  String get voiceStartSpeaking => '请开始说话...';

  @override
  String get voiceClickToStart => '点击开始';

  @override
  String get voiceClickToStop => '点击停止';

  @override
  String get voiceRecordingStopped => '录音已停止';

  @override
  String get voiceRecordingFailed => '录音失败';

  @override
  String voiceStopRecordingFailed(Object error) {
    return '停止录音失败: $error';
  }

  @override
  String get voiceRecordingFileInvalid => '录音文件无效，请重试';

  @override
  String get voiceRecordingFileNotFound => '录音文件不存在，可能录音失败';

  @override
  String get voiceSaveRecording => '保存录音';

  @override
  String get voiceTitle => '标题';

  @override
  String get voiceDuration => '时长';

  @override
  String get voiceTranscription => '转录';

  @override
  String get voiceRecordingAndTranscriptionSaved => '录音和转录文本已保存';

  @override
  String get voiceRecordingSaved => '录音已保存';

  @override
  String voiceSaveRecordingFailed(Object error) {
    return '保存录音记录失败: $error';
  }

  @override
  String get voiceNoMicrophonePermission => '没有麦克风权限，无法进行录音';

  @override
  String get voicePermissionRequired => '需要权限';

  @override
  String get voicePermissionInstructions => '请按照以下步骤开启权限:';

  @override
  String get voicePermissionStep1 => '1. 点击\"去设置\"按钮';

  @override
  String get voicePermissionStep2 => '2. 在设置中点击\"隐私与安全性\"';

  @override
  String get voicePermissionStep3 => '3. 分别点击\"麦克风\"和\"语音识别\"';

  @override
  String get voicePermissionStep4 => '4. 在列表中找到\"内容君\"并开启权限';

  @override
  String get voicePermissionNote =>
      '注意: 如果看不到App，请回到应用中重新点击\"请求权限\"按钮，然后再次查看设置';

  @override
  String get voiceGoToSettings => '去设置';

  @override
  String get voiceEnableMicrophonePermission => '请到设置中开启麦克风权限，以便进行录音';

  @override
  String get voiceInitializationError => '初始化错误';

  @override
  String get voiceRequestPermissionAgain => '重新请求权限';

  @override
  String voiceStartRecordingFailed(Object error) {
    return '开始录音失败';
  }

  @override
  String get voiceNeedMicrophonePermission => '需要麦克风权限才能使用录音功能';

  @override
  String get voiceNeedMicrophonePermissionForRecording => '需要麦克风权限才能进行录音';

  @override
  String get voiceSpeechRecognitionInitFailed => '语音识别初始化失败，请确保允许使用麦克风权限';

  @override
  String get voiceRecordingTitle => '语音录制';

  @override
  String get voicePermissionGuide => '请按照以下步骤开启权限:';

  @override
  String get voicePermissionGuideStep1 => '1. 点击\"去设置\"按钮';

  @override
  String get voicePermissionGuideStep2 => '2. 在设置中点击\"隐私与安全性\"';

  @override
  String get voicePermissionGuideStep3 => '3. 分别点击\"麦克风\"和\"语音识别\"';

  @override
  String get voicePermissionGuideStep4 => '4. 在列表中找到\"内容君\"并开启权限';

  @override
  String get voicePermissionGuideNote =>
      '注意: 如果看不到App，请回到应用中重新点击\"请求权限\"按钮，然后再次查看设置';

  @override
  String get voiceRecordingTitleLabel => '标题';

  @override
  String voiceRecordingDuration(Object duration) {
    return '时长: $duration';
  }

  @override
  String voiceRecordingTranscription(Object transcription) {
    return '转录: $transcription';
  }

  @override
  String get voiceRecordingFileNotExist => '录音文件不存在，可能录音失败';

  @override
  String get voicePleaseStartSpeaking => '请开始说话...';

  @override
  String get voiceClickToStartRecording => '点击下方按钮开始录音\n语音将实时转为文字';

  @override
  String get voiceSpeechRecognitionInProgress => '语音识别中，实时转录显示在上方';

  @override
  String get voiceSave => '保存';

  @override
  String get trafficGuideContentTools => '功能工具';

  @override
  String get trafficGuideToolsDescription => '选择合适的工具来创建引流内容';

  @override
  String get trafficGuideTextTransformer => '文本转换';

  @override
  String get trafficGuideTextTransformerSubtitle => 'Emoji转换和字符干扰';

  @override
  String get trafficGuideWatermarkProcessor => '水印处理';

  @override
  String get trafficGuideWatermarkProcessorSubtitle => '添加和移除隐形水印';

  @override
  String get trafficGuideNewProject => '新建项目';

  @override
  String get trafficGuideNewProjectSubtitle => '创建引流项目配置';

  @override
  String get trafficGuideMyProjects => '我的项目';

  @override
  String get trafficGuideProjectsDescription => '管理您的引流项目配置';

  @override
  String get trafficGuideNoProjects => '暂无项目';

  @override
  String get trafficGuideNoProjectsDescription => '点击\"新建项目\"开始创建您的第一个引流项目';

  @override
  String get trafficGuideRefresh => '刷新';

  @override
  String get trafficGuideLoading => '加载中...';

  @override
  String trafficGuideLastUpdated(Object date) {
    return '更新时间: $date';
  }

  @override
  String get trafficGuideConfirmDelete => '确认删除';

  @override
  String trafficGuideDeleteConfirmation(Object name) {
    return '确定要删除项目\"$name\"吗？';
  }

  @override
  String trafficGuideProjectDeleted(Object name) {
    return '项目\"$name\"已删除';
  }

  @override
  String get trafficGuideEdit => '编辑';

  @override
  String get trafficGuideDelete => '删除';

  @override
  String get trafficGuideProjectEditor => '项目编辑';

  @override
  String get trafficGuideBasicInfo => '基本信息';

  @override
  String get trafficGuideProjectName => '项目名称';

  @override
  String get trafficGuideProjectNameHint => '输入项目名称';

  @override
  String get trafficGuideProjectDescription => '项目描述';

  @override
  String get trafficGuideProjectDescriptionHint => '输入项目描述';

  @override
  String get trafficGuideProjectNameRequired => '请输入项目名称';

  @override
  String get trafficGuideImageConfig => '图片配置';

  @override
  String get trafficGuideDefaultText => '默认文本';

  @override
  String get trafficGuideDefaultTextHint => '输入默认显示的文本';

  @override
  String get trafficGuideFontFamily => '字体';

  @override
  String get trafficGuideTextTransformConfig => '文本转换配置';

  @override
  String get trafficGuideEmojiConversion => 'Emoji转换';

  @override
  String get trafficGuideEmojiConversionSubtitle => '将数字和字母转换为特殊Unicode字符';

  @override
  String get trafficGuideUnicodeVariation => 'Unicode变体';

  @override
  String get trafficGuideUnicodeVariationSubtitle => '添加变音字符和特殊Unicode';

  @override
  String get trafficGuideInvisibleChars => '不可见字符';

  @override
  String get trafficGuideInvisibleCharsSubtitle => '在文本中插入不可见字符';

  @override
  String get trafficGuideSensitiveWordMasking => '敏感词干扰';

  @override
  String get trafficGuideSensitiveWordMaskingSubtitle => '对敏感词进行字符干扰';

  @override
  String get trafficGuideSensitiveWords => '敏感词列表';

  @override
  String get trafficGuideSensitiveWordsHint => '输入敏感词，用逗号分隔';

  @override
  String get trafficGuideWatermarkConfig => '水印配置';

  @override
  String get trafficGuideWatermarkTextHint => '输入水印内容';

  @override
  String get trafficGuideInvisibleWatermark => '隐形水印';

  @override
  String get trafficGuideOpacity => '透明度';

  @override
  String get trafficGuideWatermarkFontSize => '字体大小';

  @override
  String get trafficGuideProjectSaved => '项目保存成功';

  @override
  String get trafficGuideSaveProject => '保存项目';

  @override
  String get trafficGuideSaving => '保存中...';

  @override
  String get trafficGuideNewProjectName => '新项目';

  @override
  String get trafficGuideNewProjectDescription => '引流项目配置';

  @override
  String get trafficGuideImageGeneratorTitle => '引流图片生成';

  @override
  String get trafficGuideImageConfiguration => '图片配置';

  @override
  String get trafficGuideTextRequired => '请输入文本内容';

  @override
  String get trafficGuideInterferenceSettings => '干扰设置';

  @override
  String get trafficGuideInterferenceLevel => '干扰程度';

  @override
  String get trafficGuideWatermarkSettings => '水印设置';

  @override
  String get trafficGuideWatermarkContent => '水印内容';

  @override
  String get trafficGuideWatermarkContentHint => '输入要添加的水印内容...';

  @override
  String get trafficGuidePreview => '预览';

  @override
  String get trafficGuideSaveToAlbum => '保存到相册';

  @override
  String get trafficGuideShare => '分享';

  @override
  String get trafficGuideSelectColor => '选择颜色';

  @override
  String get trafficGuideBlack => '黑色';

  @override
  String get trafficGuideWhite => '白色';

  @override
  String get trafficGuideRed => '红色';

  @override
  String get trafficGuideGreen => '绿色';

  @override
  String get trafficGuideBlue => '蓝色';

  @override
  String get trafficGuideYellow => '黄色';

  @override
  String get trafficGuidePurple => '紫色';

  @override
  String get trafficGuideCyan => '青色';

  @override
  String get trafficGuideGenerateImage => '生成图片';

  @override
  String get trafficGuideGenerating => '生成中...';

  @override
  String trafficGuideImageGenerationFailed(Object error) {
    return '生成图片失败: $error';
  }

  @override
  String get trafficGuideLongPressToSave => '长按图片可保存到相册';

  @override
  String get trafficGuideShareFeatureInProgress => '分享功能开发中...';

  @override
  String get trafficGuideTextTransformerTitle => '文本转换';

  @override
  String get trafficGuideTransformSettings => '转换设置';

  @override
  String get trafficGuideTransformText => '转换文本';

  @override
  String get trafficGuideTransforming => '转换中...';

  @override
  String get trafficGuideInputText => '输入文本';

  @override
  String get trafficGuideCharacters => '字符';

  @override
  String get trafficGuideInputHint => '输入要转换的文本...';

  @override
  String get trafficGuideTransformResult => '转换结果';

  @override
  String get trafficGuideResultHint => '转换后的文本将显示在这里...';

  @override
  String trafficGuideTransformFailed(Object error) {
    return '转换失败: $error';
  }

  @override
  String get trafficGuideCopyResult => '复制结果';

  @override
  String get trafficGuideClear => '清空';

  @override
  String get trafficGuideSettings => '设置';

  @override
  String get trafficGuideAdvancedSettings => '高级设置';

  @override
  String get trafficGuideCopiedToClipboard => '已复制到剪贴板';

  @override
  String get trafficGuideCustomCharacterMapping => '自定义字符映射';

  @override
  String get trafficGuideMappingFormat => '格式: 原字符=目标字符 (每行一个)';

  @override
  String get trafficGuideMappingExample => '例如:\na=ᴀ\nb=ʙ';

  @override
  String get trafficGuideConfirm => '确定';

  @override
  String get trafficGuideProcessingMode => '处理模式';

  @override
  String get trafficGuideAddWatermarkMode => '添加水印';

  @override
  String get trafficGuideRemoveWatermarkMode => '移除水印';

  @override
  String get trafficGuideProcessText => '处理文本';

  @override
  String get trafficGuideProcessing => '处理中...';

  @override
  String get trafficGuideOriginalText => '原始文本';

  @override
  String get trafficGuideWatermarkedText => '已添加水印文本';

  @override
  String get trafficGuideProcessHint => '处理结果将在此显示';

  @override
  String get trafficGuideWatermarkIdentifier => '水印标识符';

  @override
  String get trafficGuideWatermarkIdentifierHint => '输入要移除的水印标识...';

  @override
  String get trafficGuideRotationAngle => '旋转角度';

  @override
  String get trafficGuideEnterTextToProcess => '请输入要处理的文本';

  @override
  String get trafficGuideEnterWatermarkContent => '请输入水印内容';

  @override
  String get trafficGuideSensitiveWordsList => '敏感词列表';

  @override
  String get trafficGuideSensitiveWordsListHint => '输入敏感词，用逗号分隔';

  @override
  String get trafficGuideWatermarkAddHint => '在此输入需要添加水印的文本内容';

  @override
  String get trafficGuideWatermarkRemoveHint => '在此输入需要移除水印的文本内容';

  @override
  String get textCardsHomePageTitle => '文本卡片';

  @override
  String get textCardsHomePageSubtitle => '现代风格 • 内联编辑 • 高清导出';

  @override
  String get textCardsStartCreating => '开始创作';

  @override
  String get textCardsQuickActions => '快速操作';

  @override
  String get textCardsTemplateLibrary => '模板库';

  @override
  String get textCardsTemplateLibrarySubtitle => '16+ 精美模板';

  @override
  String get textCardsSmartSplit => '智能拆分';

  @override
  String get textCardsSmartSplitSubtitle => '长文本分段';

  @override
  String get textCardsContentLibrary => '内容库';

  @override
  String get textCardsContentLibrarySubtitle => '管理所有卡片';

  @override
  String get textCardsShare => '分享';

  @override
  String get textCardsShareSubtitle => '导出高清图片';

  @override
  String get textCardsFeatures => '功能特色';

  @override
  String get textCardsModernTemplates => '现代风格模板';

  @override
  String get textCardsModernTemplatesDesc => '精心设计的现代社交和阅读风格模板，让你的内容更有吸引力';

  @override
  String get textCardsInlineEditing => '内联文本编辑';

  @override
  String get textCardsInlineEditingDesc => '选中任意文本片段，实时调整字体、颜色、大小，所见即所得';

  @override
  String get textCardsHDExport => '高清图片导出';

  @override
  String get textCardsHDExportDesc => '支持多种分辨率和宽高比，一键保存到相册，完美适配各平台';

  @override
  String get textCardsViewContentLibrary => '查看我的内容库';

  @override
  String get textCardsManageCards => '管理和浏览所有创建的卡片';

  @override
  String get textCardsCreate => '创作';

  @override
  String get textCardsPleaseCreateCardFirst => '请先创建卡片';

  @override
  String get textCardsCardCreatedSuccess => '卡片创建成功！已保存到内容库';

  @override
  String textCardsBatchCreateSuccess(Object count) {
    return '批量创建成功！共创建 $count 张卡片';
  }

  @override
  String textCardsBatchCreatePartial(Object success, Object total) {
    return '批量创建完成！成功创建 $success/$total 张卡片';
  }

  @override
  String textCardsCreateFailed(Object error) {
    return '创建失败：$error';
  }

  @override
  String textCardsBatchCreateFailed(Object error) {
    return '批量创建失败：$error';
  }

  @override
  String get textCardsEditorTitle => '卡片编辑器';

  @override
  String get textCardsCreateCard => '创建卡片';

  @override
  String get textCardsEditCard => '编辑卡片';

  @override
  String get textCardsSave => '保存';

  @override
  String get textCardsEdit => '编辑';

  @override
  String get textCardsPreview => '预览';

  @override
  String get textCardsEnterCardTitle => '输入卡片标题...';

  @override
  String get textCardsEnterContent =>
      '输入内容...\n\n支持 Markdown 格式：\n• **粗体**\n• *斜体*\n• • 无序列表\n• 1. 有序列表';

  @override
  String get textCardsContentRequired => '请输入内容';

  @override
  String textCardsSaveFailed(Object error) {
    return '保存失败：$error';
  }

  @override
  String get textCardsChangeTemplate => '更换模板';

  @override
  String get textCardsHideTitle => '隐藏标题';

  @override
  String get textCardsShowTitle => '显示标题';

  @override
  String get textCardsBoldText => '加粗文本';

  @override
  String get textCardsItalicText => '斜体文本';

  @override
  String get textCardsUnderlineText => '下划线文本';

  @override
  String get textCardsPreviewPlaceholder => '在编辑标签页输入内容以查看预览效果...';

  @override
  String get textCardsContentEditor => '内容编辑器';

  @override
  String get textCardsEnterTitle => '输入标题（可选）';

  @override
  String get textCardsAddSplitMarker => '添加拆分标记';

  @override
  String get textCardsEnterRenderer => '进入渲染器';

  @override
  String textCardsSplitMarkerInfo(Object count, Object sections) {
    return '已设置 $count 个拆分标记，将生成 $sections 个卡片';
  }

  @override
  String get textCardsEnterContentHint =>
      '在这里输入或粘贴您的内容...\n\n提示：\n- 使用 # 创建标题\n- 使用 - 或 * 创建列表\n- 使用 > 创建引用\n- 点击拆分按钮在光标位置添加拆分标记';

  @override
  String get textCardsTextPreview => '文本预览';

  @override
  String get textCardsPreviewWillAppear => '输入内容后将显示预览';

  @override
  String get textCardsSelectSplitMarker => '选择拆分标记';

  @override
  String get textCardsPredefinedMarkers => '预定义标记：';

  @override
  String get textCardsCustomMarker => '自定义标记：';

  @override
  String get textCardsEnterCustomMarker => '输入自定义拆分标记';

  @override
  String get textCardsUseCustom => '使用自定义';

  @override
  String get textCardsUnnamedCard => '未命名卡片';

  @override
  String get textCardsUnnamedDocument => '未命名文档';

  @override
  String get svgEditorTitle => 'SVG编辑器';

  @override
  String get svgManagerTitle => 'SVG管理';

  @override
  String get svgUntitled => '未命名SVG';

  @override
  String get svgEditTab => '编辑';

  @override
  String get svgPreviewTab => '预览';

  @override
  String get svgMoreActions => '更多操作';

  @override
  String get svgImportFile => '导入SVG文件';

  @override
  String get svgSave => '保存';

  @override
  String get svgExportPng => '导出为PNG';

  @override
  String get svgSharePng => '分享为PNG';

  @override
  String get svgShareSvg => '分享SVG';

  @override
  String get svgRename => '重命名';

  @override
  String get svgSaveChanges => '保存更改';

  @override
  String get svgEnterFileName => '输入SVG文件名';

  @override
  String get svgFileName => '文件名';

  @override
  String get svgFileNameHint => '请输入SVG文件名';

  @override
  String get svgFileNameRequired => '文件名不能为空';

  @override
  String get svgProcessing => '处理中...';

  @override
  String get svgLoading => '加载中...';

  @override
  String get svgDocumentNotFound => '找不到文档';

  @override
  String svgLoadDocumentFailed(Object error) {
    return '加载文档失败: $error';
  }

  @override
  String svgCreateDocumentFailed(Object error) {
    return '创建文档失败: $error';
  }

  @override
  String svgSaveDocumentFailed(Object error) {
    return '保存文档失败: $error';
  }

  @override
  String get svgSaveSuccess => '保存成功';

  @override
  String svgImportFailed(Object error) {
    return '导入SVG文件失败: $error';
  }

  @override
  String svgExportPngSuccess(Object path) {
    return '导出PNG成功: $path';
  }

  @override
  String get svgExportPngFailed => '导出PNG失败';

  @override
  String svgShareSvgFailed(Object error) {
    return '分享SVG失败: $error';
  }

  @override
  String svgSharePngFailed(Object error) {
    return '分享PNG失败: $error';
  }

  @override
  String svgInvalidSvg(Object error) {
    return '无效的SVG: $error';
  }

  @override
  String get svgSaveFirst => '请先保存文档';

  @override
  String get svgEnterSvgCode => '输入SVG代码';

  @override
  String get svgNoContent => '无SVG内容';

  @override
  String get svgEnterCodeInEditor => '请在编辑标签页中输入SVG代码';

  @override
  String get svgCreateNew => '创建新SVG';

  @override
  String get svgImport => '导入SVG';

  @override
  String get svgImportSvgFile => '导入SVG文件';

  @override
  String get svgImportTooltip => '导入SVG';

  @override
  String get svgNewTooltip => '新建SVG';

  @override
  String get svgCreateNewTooltip => '创建新SVG';

  @override
  String get svgNoDocuments => '没有SVG文档';

  @override
  String get svgNoDocumentsDesc => '创建新的SVG文档或导入现有文件开始使用';

  @override
  String svgLoadFailed(Object error) {
    return '加载文档失败: $error';
  }

  @override
  String get svgDeleteConfirm => '确认删除';

  @override
  String svgDeleteConfirmMessage(Object title) {
    return '确定要删除\"$title\"吗？';
  }

  @override
  String svgDeleteFailed(Object error) {
    return '删除文档失败: $error';
  }

  @override
  String get svgEdit => '编辑';

  @override
  String get svgShare => '分享';

  @override
  String get svgShareAsPng => '分享为PNG';

  @override
  String get svgDelete => '删除';

  @override
  String get svgSavedToLibrary => 'SVG已保存到内容库';

  @override
  String svgCreatedAt(Object date) {
    return '创建于: $date';
  }

  @override
  String get pdfManagerTitle => 'PDF文档管理';

  @override
  String get pdfSearch => '搜索PDF文件...';

  @override
  String get pdfSearchHint => '搜索...';

  @override
  String get pdfNoDocuments => '没有找到匹配的PDF文档';

  @override
  String get pdfTryDifferentSearch => '尝试使用不同的搜索关键词';

  @override
  String get pdfConfirmDelete => '确认删除';

  @override
  String pdfDeleteConfirm(Object filename) {
    return '确定要删除 \"$filename\" 吗？此操作不可恢复。';
  }

  @override
  String pdfBatchDeleteConfirm(Object count) {
    return '确定要删除选中的 $count 个文件吗？此操作不可恢复。';
  }

  @override
  String get pdfCancel => '取消';

  @override
  String get pdfDelete => '删除';

  @override
  String get pdfImport => '导入PDF';

  @override
  String get pdfImportTooltip => '导入PDF';

  @override
  String get pdfSecuritySettings => '安全设置';

  @override
  String get pdfSelect => '选择';

  @override
  String get pdfMerge => '合并';

  @override
  String get pdfSelectAtLeastTwo => '请至少选择两个PDF文件进行合并';

  @override
  String get pdfMergeSuccess => 'PDF合并成功';

  @override
  String get pdfMergeFailed => 'PDF合并失败';

  @override
  String get pdfIntelligentCenter => 'PDF 智能管理中心';

  @override
  String get pdfCenterSubtitle => '集成阅读、编辑、安全、分享于一体的专业PDF工具';

  @override
  String get pdfVersion => 'v2.0 专业版';

  @override
  String get pdfCoreFeatures => '核心功能';

  @override
  String get pdfProfessional => '专业版';

  @override
  String get pdfSecurityEncryption => '安全加密';

  @override
  String get pdfPasswordProtection => '密码保护';

  @override
  String get pdfPermissionControl => '权限控制';

  @override
  String get pdfSmartAnnotations => '智能注释';

  @override
  String get pdfHighlight => '高亮标记';

  @override
  String get pdfTextAnnotations => '文字批注';

  @override
  String get pdfFastSearch => '快速搜索';

  @override
  String get pdfFullTextSearch => '全文检索';

  @override
  String get pdfPreciseLocation => '精确定位';

  @override
  String get pdfDocumentMerge => '文档合并';

  @override
  String get pdfMultiFileMerge => '多文件合并';

  @override
  String get pdfEasySharing => '便捷分享';

  @override
  String get pdfOneClickShare => '一键分享';

  @override
  String get pdfCloudSync => '云端同步';

  @override
  String get pdfAutoSync => '自动同步';

  @override
  String get pdfQuickStart => '快速开始';

  @override
  String get pdfImportDocument => '导入PDF文档';

  @override
  String get pdfViewDemo => '查看演示';

  @override
  String get pdfHelp => '使用帮助';

  @override
  String get pdfSupportInfo => '支持导入 .pdf 格式文件，最大支持 100MB';

  @override
  String pdfModified(Object date) {
    return '修改于: $date';
  }

  @override
  String get pdfJustNow => '刚刚';

  @override
  String pdfMinutesAgo(Object minutes) {
    return '$minutes分钟前';
  }

  @override
  String pdfHoursAgo(Object hours) {
    return '$hours小时前';
  }

  @override
  String pdfDaysAgo(Object days) {
    return '$days天前';
  }

  @override
  String pdfPages(Object count) {
    return '$count 页';
  }

  @override
  String get pdfSecurityStatus => '安全状态';

  @override
  String get pdfProtected => '已保护';

  @override
  String get pdfRestricted => '已限制';

  @override
  String get pdfUnprotected => '未保护';

  @override
  String get pdfUsageStats => '使用统计';

  @override
  String get pdfTotalDocuments => '文档总数';

  @override
  String get pdfTodayProcessed => '今日处理';

  @override
  String get pdfStorageSpace => '存储空间';

  @override
  String get pdfTips => '使用技巧';

  @override
  String get pdfLongPressSelect => '长按选择';

  @override
  String get pdfLongPressDesc => '长按文档卡片可进入多选模式，批量操作更高效';

  @override
  String get pdfSecurityTip => '安全加密';

  @override
  String get pdfSecurityTipDesc => '为重要文档设置密码保护，确保信息安全';

  @override
  String get pdfMergeTip => '文档合并';

  @override
  String get pdfMergeTipDesc => '选择多个PDF文档，一键合并成单个文件';

  @override
  String get pdfViewerTitle => 'PDF查看器';

  @override
  String get pdfSearchText => '搜索文本';

  @override
  String get pdfShowAnnotations => '显示注释';

  @override
  String get pdfHideAnnotations => '隐藏注释';

  @override
  String get pdfDocumentInfo => '文档信息';

  @override
  String get pdfAnnotationDetails => '注释详情';

  @override
  String pdfAuthor(Object author) {
    return '作者: $author';
  }

  @override
  String pdfCreatedAt(Object datetime, Object time) {
    return '创建时间: $datetime';
  }

  @override
  String pdfContent(Object content) {
    return '内容: $content';
  }

  @override
  String pdfHighlightedText(Object text) {
    return '高亮文本: $text';
  }

  @override
  String get pdfFileName => '文件名';

  @override
  String get pdfFileSize => '大小';

  @override
  String get pdfPageCount => '页数';

  @override
  String get pdfCreatedDate => '创建时间';

  @override
  String get pdfModifiedDate => '修改时间';

  @override
  String get pdfAnnotationCount => '注释数量';

  @override
  String get pdfClose => '关闭';

  @override
  String get pdfSecurityTitle => 'PDF安全设置';

  @override
  String get pdfDocumentInfoSection => '文档信息';

  @override
  String pdfStatus(Object status) {
    return '状态: $status';
  }

  @override
  String get pdfDecryptPdf => '解密PDF';

  @override
  String get pdfEncryptedDesc => '该PDF已加密，请输入密码进行解密';

  @override
  String get pdfCurrentPassword => '当前密码';

  @override
  String get pdfCurrentPasswordHint => '请输入当前密码';

  @override
  String get pdfDecryptButton => '解密';

  @override
  String get pdfEncryptionSettings => '加密设置';

  @override
  String get pdfUserPassword => '用户密码 *';

  @override
  String get pdfUserPasswordHint => '用于打开PDF的密码';

  @override
  String get pdfOwnerPassword => '所有者密码（可选）';

  @override
  String get pdfOwnerPasswordHint => '用于修改权限的密码';

  @override
  String get pdfPermissionSettings => '权限设置';

  @override
  String get pdfAllowPrint => '允许打印';

  @override
  String get pdfAllowPrintDesc => '允许用户打印PDF文档';

  @override
  String get pdfAllowCopy => '允许复制';

  @override
  String get pdfAllowCopyDesc => '允许用户复制PDF内容';

  @override
  String get pdfAllowEdit => '允许编辑';

  @override
  String get pdfAllowEditDesc => '允许用户编辑PDF文档';

  @override
  String get pdfAllowEditAnnotations => '允许编辑注释';

  @override
  String get pdfAllowEditAnnotationsDesc => '允许用户添加或编辑注释';

  @override
  String get pdfAllowFillForms => '允许填写表单';

  @override
  String get pdfAllowFillFormsDesc => '允许用户填写表单字段';

  @override
  String get pdfAllowExtractPages => '允许提取页面';

  @override
  String get pdfAllowExtractPagesDesc => '允许用户提取页面内容';

  @override
  String get pdfAllowAssembleDocument => '允许装配文档';

  @override
  String get pdfAllowAssembleDocumentDesc => '允许用户插入、删除、旋转页面';

  @override
  String get pdfAllowHighQualityPrint => '允许高质量打印';

  @override
  String get pdfAllowHighQualityPrintDesc => '允许用户高质量打印';

  @override
  String get pdfPresetPermissions => '预设权限';

  @override
  String get pdfAllPermissions => '全部权限';

  @override
  String get pdfBasicPermissions => '基本权限';

  @override
  String get pdfReadOnly => '只读';

  @override
  String get pdfSetPermissionsOnly => '仅设置权限';

  @override
  String get pdfEncryptAndSetPermissions => '加密并设置权限';

  @override
  String get pdfEnterUserPassword => '请输入用户密码';

  @override
  String get pdfEncryptSuccess => 'PDF加密成功';

  @override
  String get pdfEncryptFailed => 'PDF加密失败';

  @override
  String pdfEncryptionFailed(Object error) {
    return '加密失败: $error';
  }

  @override
  String get pdfEnterCurrentPassword => '请输入当前密码';

  @override
  String get pdfDecryptSuccess => 'PDF解密成功';

  @override
  String get pdfDecryptFailed => 'PDF解密失败，请检查密码';

  @override
  String pdfDecryptionFailed(Object error) {
    return '解密失败: $error';
  }

  @override
  String get pdfPermissionsSetSuccess => '权限设置成功';

  @override
  String get pdfPermissionsSetFailed => '权限设置失败';

  @override
  String pdfSetPermissionsFailed(Object error) {
    return '设置失败: $error';
  }

  @override
  String get htmlManagerTitle => 'HTML管理';

  @override
  String get htmlManagerDescription => '创建和编辑HTML文档，所有内容将自动保存至内容库进行统一管理';

  @override
  String get htmlCreateNew => '创建新HTML';

  @override
  String get htmlImportFile => '导入HTML文件';

  @override
  String get htmlImporting => '导入中...';

  @override
  String get htmlImportSuccess => '导入成功';

  @override
  String htmlImportSuccessMessage(Object count) {
    return '已成功导入 $count 个HTML文件到内容库';
  }

  @override
  String htmlImportFailed(Object error) {
    return '导入HTML文件失败: $error';
  }

  @override
  String htmlImportingProgress(Object imported, Object total) {
    return '正在导入HTML文件 ($imported/$total)';
  }

  @override
  String get htmlViewContentLibrary => '查看内容库';

  @override
  String get htmlEditorTitle => 'HTML编辑器';

  @override
  String get htmlNewDocument => '新HTML文档';

  @override
  String get htmlInputFilename => '输入HTML文件名';

  @override
  String get htmlFilename => '文件名';

  @override
  String get htmlFilenameHint => '请输入HTML文件名';

  @override
  String get htmlFilenameEmpty => '文件名不能为空';

  @override
  String get htmlCancel => '取消';

  @override
  String get htmlConfirm => '确定';

  @override
  String get htmlSaveSuccess => '保存成功';

  @override
  String get htmlDocumentNotFound => '找不到文档';

  @override
  String htmlLoadDocumentFailed(Object error) {
    return '加载文档失败: $error';
  }

  @override
  String htmlCreateDocumentFailed(Object error) {
    return '创建文档失败: $error';
  }

  @override
  String htmlSaveDocumentFailed(Object error) {
    return '保存文档失败: $error';
  }

  @override
  String htmlImportFileFailed(Object error) {
    return '导入HTML文件失败: $error';
  }

  @override
  String htmlExportImageFailed(Object error) {
    return '导出图片失败: $error';
  }

  @override
  String htmlShareHtmlFailed(Object error) {
    return '分享HTML失败: $error';
  }

  @override
  String htmlShareImageFailed(Object error) {
    return '分享图片失败: $error';
  }

  @override
  String htmlSaveToLibraryFailed(Object error) {
    return '保存到内容库失败: $error';
  }

  @override
  String get htmlPleaseSaveFirst => '请先保存文档';

  @override
  String get htmlSelectSaveLocation => '选择保存位置';

  @override
  String htmlExportImageSuccess(Object path) {
    return '导出图片成功: $path';
  }

  @override
  String htmlSavedToLibrary(Object title) {
    return '已保存到内容库: $title';
  }

  @override
  String get htmlProcessing => '处理中...';

  @override
  String get htmlProcessingLargeText => '处理大文本...';

  @override
  String get htmlInputHtmlCode => '输入HTML代码';

  @override
  String get htmlNoContent => '无HTML内容';

  @override
  String get htmlPleaseInputHtml => '请输入HTML代码';

  @override
  String get htmlEditMode => '编辑模式';

  @override
  String get htmlPreviewMode => '预览模式';

  @override
  String get htmlSingleScreenMode => '单屏模式';

  @override
  String get htmlSplitScreenMode => '分屏模式';

  @override
  String get htmlMoreActions => '更多操作';

  @override
  String get htmlImportHtmlFile => '导入HTML文件';

  @override
  String get htmlExportAsImage => '导出为图片';

  @override
  String get htmlShareAsImage => '分享为图片';

  @override
  String get htmlShareHtml => '分享HTML';

  @override
  String get htmlRename => '重命名';

  @override
  String get htmlSaveToLibrary => '保存到内容库';

  @override
  String get htmlSaveToLibraryTooltip => '保存到内容库';

  @override
  String get htmlSaveTooltip => '保存';

  @override
  String get htmlSaveToGallery => '保存到相册';

  @override
  String get htmlNewHtmlTooltip => '新建HTML';

  @override
  String get htmlImportHtmlTooltip => '导入HTML';

  @override
  String get settingsLanguagePreference => '选择您偏好的应用语言';

  @override
  String get settingsStorageManagement => '存储管理';

  @override
  String get settingsHelpCenter => '帮助中心';

  @override
  String get settingsFeedback => '意见反馈';

  @override
  String get settingsVersionInfo => '版本信息';

  @override
  String get settingsHelpAndFeedback => '帮助与反馈';

  @override
  String get settingsGetHelpOrProvideFeedback => '获取帮助或提供反馈';

  @override
  String get settingsHelpAndFeedbackContent => '如果您需要帮助或有建议，请通过反馈页面联系我们。';

  @override
  String get settingsOk => '确定';

  @override
  String get settingsSelectTheme => '选择主题';

  @override
  String get settingsSystemMode => '跟随系统';

  @override
  String get settingsLightMode => '浅色';

  @override
  String get settingsDarkMode => '深色';

  @override
  String get storageLoadingStorageInfo => '加载存储信息';

  @override
  String storageLoadStorageInfoFailed(Object error) {
    return '加载存储信息失败：$error';
  }

  @override
  String get storageTotalUsage => '总存储使用';

  @override
  String get storageDetails => '存储详情';

  @override
  String get storageAppData => '应用数据';

  @override
  String get storageCacheFiles => '缓存文件';

  @override
  String get storageContentData => '内容数据';

  @override
  String get storageVoiceFiles => '语音文件';

  @override
  String get storageImageFiles => '图片文件';

  @override
  String get storageSettingsData => '设置数据';

  @override
  String get storageCleanupOptions => '清理选项';

  @override
  String get storageClearCache => '清理缓存';

  @override
  String get storageClearCacheDesc => '删除临时文件和缓存数据';

  @override
  String get storageClearTempFiles => '清理临时文件';

  @override
  String get storageClearTempFilesDesc => '删除处理过程中产生的临时文件';

  @override
  String get storageDataManagement => '数据管理';

  @override
  String get storageExportData => '导出数据';

  @override
  String get storageExportDataDesc => '将应用数据导出到文件';

  @override
  String get storageImportData => '导入数据';

  @override
  String get storageImportDataDesc => '从文件导入应用数据';

  @override
  String get storageResetAppData => '重置应用数据';

  @override
  String get storageResetAppDataDesc => '清除所有数据并恢复默认设置';

  @override
  String get storageCacheCleared => '缓存清理完成';

  @override
  String storageClearCacheFailed(Object error) {
    return '清理缓存失败：$error';
  }

  @override
  String get storageTempFilesCleared => '临时文件清理完成';

  @override
  String storageClearTempFilesFailed(Object error) {
    return '清理临时文件失败：$error';
  }

  @override
  String get storageVoiceManagementInDevelopment => '语音文件管理功能开发中';

  @override
  String get storageImageManagementInDevelopment => '图片文件管理功能开发中';

  @override
  String get storageDataExportInDevelopment => '数据导出功能开发中';

  @override
  String get storageDataImportInDevelopment => '数据导入功能开发中';

  @override
  String get storageResetDataTitle => '重置应用数据';

  @override
  String get storageResetDataMessage => '此操作将删除所有数据并恢复默认设置，无法撤销。确定要继续吗？';

  @override
  String get storageCancel => '取消';

  @override
  String get storageConfirm => '确定';

  @override
  String get storageDataResetComplete => '应用数据重置完成';

  @override
  String storageDataResetFailed(Object error) {
    return '重置应用数据失败：$error';
  }

  @override
  String get iosSettingsGeneral => '通用';

  @override
  String get iosSettingsLanguageRegion => '语言与地区';

  @override
  String get iosSettingsDisplayBrightness => '显示与亮度';

  @override
  String get iosSettingsAppearance => '外观';

  @override
  String get iosSettingsPrivacySecurity => '隐私与安全';

  @override
  String get iosSettingsPrivacySettings => '隐私设置';

  @override
  String get iosSettingsStorage => '存储';

  @override
  String get iosSettingsStorageManagement => '存储管理';

  @override
  String get iosSettingsViewStorageUsage => '查看存储使用情况';

  @override
  String get iosSettingsDataImportExport => '数据导入导出';

  @override
  String get iosSettingsBackupRestoreData => '备份和恢复数据';

  @override
  String get iosSettingsSupport => '支持';

  @override
  String get iosSettingsHelpCenter => '帮助中心';

  @override
  String get iosSettingsFeedback => '意见反馈';

  @override
  String get iosSettingsRateApp => '评价应用';

  @override
  String get iosSettingsAbout => '关于';

  @override
  String get iosSettingsAboutApp => '关于应用';

  @override
  String get iosSettingsCurrentLanguage => '简体中文';

  @override
  String get iosSettingsLightTheme => '浅色';

  @override
  String get iosSettingsDarkTheme => '深色';

  @override
  String get iosSettingsSystemTheme => '跟随系统';

  @override
  String get iosSettingsSelectLanguage => '选择语言';

  @override
  String get iosSettingsSimplifiedChinese => '简体中文';

  @override
  String get iosSettingsTraditionalChinese => '繁體中文';

  @override
  String get iosSettingsEnglish => 'English';

  @override
  String iosSettingsLanguageSelected(Object language) {
    return '已选择语言：$language';
  }

  @override
  String get iosSettingsSelectAppearance => '选择外观';

  @override
  String get iosSettingsPrivacyContent => '我们重视您的隐私。所有数据处理都在本地进行，不会上传到服务器。';

  @override
  String get iosSettingsPrivacyManage => '您可以随时在设置中管理您的数据。';

  @override
  String get iosSettingsUnderstand => '了解';

  @override
  String get iosSettingsDataManagementTitle => '数据管理';

  @override
  String get iosSettingsSelectOperation => '选择要执行的操作';

  @override
  String get iosSettingsExportData => '导出数据';

  @override
  String get iosSettingsImportData => '导入数据';

  @override
  String get iosSettingsCreateBackup => '创建备份';

  @override
  String get iosSettingsRateAppTitle => '评价应用';

  @override
  String get iosSettingsRateAppMessage => '您喜欢这个应用吗？请在App Store中为我们评分！';

  @override
  String get iosSettingsLater => '稍后';

  @override
  String get iosSettingsRateNow => '去评价';

  @override
  String get iosSettingsCannotOpenAppStore => '无法打开App Store';

  @override
  String get iosSettingsAppStoreError => '打开App Store时出现错误';

  @override
  String iosSettingsVersion(Object version) {
    return '版本：$version';
  }

  @override
  String get iosSettingsAppDescription => '一款强大的内容管理工具，帮助您更高效地创建和管理各种格式的内容。';

  @override
  String get iosSettingsCopyright => '© 2023-2024 内容君团队';

  @override
  String get iosSettingsClose => '关闭';

  @override
  String get iosSettingsOK => '确定';

  @override
  String get iosSettingsError => '错误';

  @override
  String get iosSettingsDataExportInDevelopment => '数据导出功能开发中...';

  @override
  String get iosSettingsDataImportInDevelopment => '数据导入功能开发中...';

  @override
  String get iosSettingsBackupInDevelopment => '备份功能开发中...';

  @override
  String get helpCenterTitle => '帮助中心';

  @override
  String get helpCenterNeedHelp => '需要帮助？';

  @override
  String get helpCenterDescription => '查找常见问题的解答，或联系我们获取支持';

  @override
  String get helpCenterContactSupport => '联系支持';

  @override
  String get helpCenterUserManual => '用户手册';

  @override
  String get helpCenterGettingStarted => '如何开始使用';

  @override
  String get helpCenterGettingStartedContent =>
      '欢迎使用内容君！您可以从主页选择需要的功能模块，如文本卡片、Markdown编辑、PDF处理等。';

  @override
  String get helpCenterTextCards => '文本卡片功能';

  @override
  String get helpCenterTextCardsContent =>
      '文本卡片功能可以帮您将文本内容转换为精美的卡片图片，支持多种模板和样式自定义。';

  @override
  String get helpCenterMarkdownEditing => 'Markdown编辑';

  @override
  String get helpCenterMarkdownEditingContent =>
      'Markdown编辑器支持实时预览、多种主题、导出为HTML/PDF等功能，让您的文档编写更高效。';

  @override
  String get helpCenterTrafficGuideGeneration => '引流图片生成';

  @override
  String get helpCenterTrafficGuideGenerationContent =>
      '引流图片生成功能可以创建吸引眼球的营销图片，支持添加干扰、水印等防盗用功能。';

  @override
  String get helpCenterVoiceFeatures => '语音功能';

  @override
  String get helpCenterVoiceFeaturesContent =>
      '语音功能包括录音、转录、文字转语音等，支持多种语言和高质量的语音处理。';

  @override
  String get helpCenterPDFProcessing => 'PDF处理';

  @override
  String get helpCenterPDFProcessingContent =>
      'PDF处理功能支持查看、注释、安全设置等，让您更好地管理PDF文档。';

  @override
  String get helpCenterDataSyncBackup => '数据同步与备份';

  @override
  String get helpCenterDataSyncBackupContent =>
      '您的数据会自动保存在本地，建议定期使用导出功能备份重要内容。';

  @override
  String get helpCenterPrivacySecurity => '隐私与安全';

  @override
  String get helpCenterPrivacySecurityContent =>
      '我们重视您的隐私，所有数据处理都在本地进行，不会上传到服务器。';

  @override
  String get helpCenterSearchHelp => '搜索帮助';

  @override
  String get helpCenterSearchPlaceholder => '输入关键词搜索...';

  @override
  String get helpCenterSearchCancel => '取消';

  @override
  String get helpCenterSearch => '搜索';

  @override
  String get helpCenterSupportRequestSubject => '内容君 支持请求';

  @override
  String get helpCenterSupportRequestBody => '请描述您遇到的问题...';

  @override
  String get helpCenterCannotOpenEmailApp => '无法打开邮件应用';

  @override
  String get helpCenterEmailError => '发送邮件时出现错误';

  @override
  String get helpCenterCannotOpenManual => '无法打开用户手册';

  @override
  String get helpCenterManualError => '打开用户手册时出现错误';

  @override
  String get feedbackTitle => '意见反馈';

  @override
  String get feedbackSubtitle => '您的意见很重要';

  @override
  String get feedbackDescription => '告诉我们您的想法，帮助我们改进应用';

  @override
  String get feedbackType => '反馈类型';

  @override
  String get feedbackTitleLabel => '标题';

  @override
  String get feedbackDetailedDescription => '详细描述';

  @override
  String get feedbackContactEmail => '联系邮箱（可选）';

  @override
  String get feedbackIncludeSystemInfo => '包含系统信息';

  @override
  String get feedbackIncludeSystemInfoDesc => '帮助我们更好地诊断问题';

  @override
  String get feedbackSubmit => '提交反馈';

  @override
  String get feedbackBugReport => '错误报告';

  @override
  String get feedbackBugReportDesc => '报告应用中的错误或异常';

  @override
  String get feedbackFeatureSuggestion => '功能建议';

  @override
  String get feedbackFeatureSuggestionDesc => '建议新功能或改进现有功能';

  @override
  String get feedbackComplaint => '问题投诉';

  @override
  String get feedbackComplaintDesc => '对应用的不满或问题';

  @override
  String get feedbackPraise => '表扬赞美';

  @override
  String get feedbackPraiseDesc => '对应用的赞美或好评';

  @override
  String get feedbackTitleHint => '请简要描述您的反馈';

  @override
  String get feedbackTitleRequired => '请输入反馈标题';

  @override
  String get feedbackDescriptionHint => '请详细描述您的问题、建议或想法...';

  @override
  String get feedbackDescriptionRequired => '请输入详细描述';

  @override
  String get feedbackDescriptionTooShort => '描述内容至少需要10个字符';

  @override
  String get feedbackEmailHint => '<EMAIL>';

  @override
  String get feedbackEmailInvalid => '请输入有效的邮箱地址';

  @override
  String get feedbackSubmitting => '提交中...';

  @override
  String feedbackSubmissionError(Object error) {
    return '提交反馈时出现错误：$error';
  }

  @override
  String get feedbackSubmissionSuccessTitle => '反馈提交成功';

  @override
  String get feedbackSubmissionSuccessMessage => '感谢您的反馈！我们会认真考虑您的建议。';

  @override
  String get feedbackConfirm => '确定';

  @override
  String get feedbackSystemInfoFailed => '系统信息获取失败';

  @override
  String feedbackAppVersion(Object version) {
    return '应用版本：$version';
  }

  @override
  String feedbackBuildNumber(Object buildNumber) {
    return '构建号：$buildNumber';
  }

  @override
  String feedbackDevice(Object device) {
    return '设备：$device';
  }

  @override
  String feedbackSystemVersion(Object version) {
    return '系统版本：$version';
  }

  @override
  String feedbackDeviceModel(Object model) {
    return '设备型号：$model';
  }

  @override
  String feedbackManufacturer(Object manufacturer) {
    return '制造商：$manufacturer';
  }

  @override
  String feedbackEmailSubject(Object feedbackType) {
    return '内容君 - $feedbackType';
  }

  @override
  String get feedbackCannotOpenEmailApp => '无法打开邮件应用';

  @override
  String get newContentLibraryExperience => '全新内容库体验';

  @override
  String get supportMultipleContentTypes => '支持多种内容类型，优先显示渲染结果';

  @override
  String get contentLibraryDemoPage => '内容库演示';

  @override
  String contentServiceLoadItemFailed(Object error) {
    return '加载内容项失败: $error';
  }

  @override
  String get contentServiceMustBeTextType => '必须是文本类型内容';

  @override
  String get contentServiceMustBeImageType => '必须是图像类型内容';

  @override
  String get contentServiceItemNotFound => '内容项不存在';

  @override
  String get contentServiceNotInitialized => 'ContentService 未初始化';

  @override
  String contentServiceRenderFailed(Object error) {
    return '渲染失败: $error';
  }

  @override
  String permissionHelperRequestStorageFailed(Object error) {
    return '请求存储权限失败: $error';
  }

  @override
  String permissionHelperRequestCameraFailed(Object error) {
    return '请求相机权限失败: $error';
  }

  @override
  String permissionHelperRequestMultipleFailed(Object error) {
    return '请求多个权限失败: $error';
  }

  @override
  String permissionHelperIosPermissionCheckFailed(Object error) {
    return '启动时权限状态检查失败: $error';
  }

  @override
  String get chineseTraditionalColorTitle => '中国传统色';

  @override
  String get chineseTraditionalColorSubtitle => '选择您喜欢的传统色彩主题';

  @override
  String get chineseTraditionalColorSystemTheme => '跟随系统主题';

  @override
  String get chineseTraditionalColorSystemThemeDesc => '使用应用默认的主题色彩';

  @override
  String chineseTraditionalColorSwitchedToTheme(Object themeName) {
    return '已切换到「$themeName」主题';
  }

  @override
  String get chineseTraditionalColorSwitchedToSystem => '已切换到系统默认主题';

  @override
  String get subscriptionManagement => '订阅管理';

  @override
  String get upgradeSubscription => '升级订阅';

  @override
  String get upgradeSubscriptionDesc => '查看和购买更高级的订阅计划';

  @override
  String get restorePurchase => '恢复购买';

  @override
  String get restorePurchaseDesc => '恢复您之前的订阅购买';

  @override
  String get helpAndSupport => '帮助和支持';

  @override
  String get frequentlyAskedQuestions => '常见问题';

  @override
  String get frequentlyAskedQuestionsDesc => '查看关于订阅的常见问题解答';

  @override
  String get contactCustomerService => '联系客服';

  @override
  String get contactCustomerServiceDesc => '获取关于订阅的帮助';

  @override
  String get refundPolicy => '退款政策';

  @override
  String get refundPolicyDesc => '了解我们的退款和取消政策';

  @override
  String get restoringPurchase => '正在恢复购买';

  @override
  String get communicatingWithAppStore => '正在与应用商店通信，请稍候...';

  @override
  String get noRestorablePurchasesFound => '未找到可恢复的购买记录';

  @override
  String get currentSubscription => '当前订阅';

  @override
  String get freeVersion => '免费版';

  @override
  String get availableFeatures => '可用功能';

  @override
  String get basicProcessing => '基础处理';

  @override
  String get exportWithWatermark => '带水印导出';

  @override
  String get unlimitedExport => '无限导出';

  @override
  String get batchProcessing => '批量处理';

  @override
  String get advancedTools => '高级工具';

  @override
  String get markdownSavedMarkdown => '保存的 Markdown';

  @override
  String get textCardNoCardsYet => '还没有卡片';

  @override
  String get textCardTitle => '标题';

  @override
  String get textCardContent => '内容';

  @override
  String get textCardNeedPhotoPermission => '需要相册权限才能保存图片，请在设置中开启权限';

  @override
  String get textCardScreenshotFailed => '截图失败，请重试';

  @override
  String get textCardPermissionDenied => '权限不足，请在设置中开启相册权限';

  @override
  String get textCardExportCard => '导出卡片';

  @override
  String get textCardExportDocument => '导出文档';

  @override
  String get textCardPreview => '预览';

  @override
  String get textCardExporting => '导出中...';

  @override
  String get textCardStartExport => '开始导出';

  @override
  String get textCardExportSize => '导出尺寸';

  @override
  String get textCardTargetPlatform => '目标平台';

  @override
  String get textCardFileFormat => '文件格式';

  @override
  String get textCardWatermarkSettings => '水印设置';

  @override
  String get textCardAddWatermarkSubtitle => '在图片角落添加应用水印';

  @override
  String get textCardCustomWatermarkText => '自定义水印文字';

  @override
  String get textCardCustomWatermarkHint => '留空使用默认水印';

  @override
  String get textCardAdvancedOptions => '高级选项';

  @override
  String get textCardIncludeTitleSubtitle => '在导出的图片中显示标题';

  @override
  String get textCardIncludeTimestampSubtitle => '在图片中显示创建时间';

  @override
  String get textCardMore => '更多';

  @override
  String get textCardPart => '第';

  @override
  String get textCardSection => '部分';

  @override
  String get textCardSelectionInstructions => '选择下方文本的任意范围，即可对选中内容应用样式';

  @override
  String get textCardExportOptions => '导出选项';

  @override
  String get textCardSaveToGallery => '保存到相册';

  @override
  String get textCardExportSingleCard => '将导出 1 张卡片图片';

  @override
  String get textCardExportMultipleCards => '将导出';

  @override
  String get textCardImages => '张卡片图片';

  @override
  String get textCardCard => '第';

  @override
  String get textCardCardNumber => '张';

  @override
  String get textCardShareFromApp => '来自 内容君 的';

  @override
  String get textCardShareFailed => '分享失败';

  @override
  String get textCardSavedToGallery => '已保存';

  @override
  String get textCardCardsToGallery => '张卡片到相册';

  @override
  String get textCardCreateDocument => '创建文档';

  @override
  String get textCardTextEditMode => '文本编辑模式';

  @override
  String get textCardPreviewMode => '预览模式';

  @override
  String get textCardTotalCards => '共';

  @override
  String get textCardCards => '个卡片';

  @override
  String get textCardUseSeparator => '使用';

  @override
  String get textCardSeparatorHint => '分隔卡片';

  @override
  String get textCardDocumentTitle => '文档标题';

  @override
  String get textCardDocumentTitleHint => '为这组卡片起个标题...';

  @override
  String get textCardDocumentContentHint =>
      '输入或粘贴长文本...\n\n💡 使用技巧：\n• 在想要分割的地方点击\"插入分隔符\"按钮\n• 第一行如果是标题会自动识别\n• 点击右上角\"预览\"查看拆分效果';

  @override
  String get textCardGoBackToEditMode => '回到编辑模式添加分隔符来创建卡片';

  @override
  String get textCardListView => '列表视图';

  @override
  String get textCardGridView => '网格视图';

  @override
  String get textCardTemplate => '模板';

  @override
  String get textCardNoCardsInDocument => '文档中没有卡片';

  @override
  String get textCardEditDocumentToAddCards => '编辑文档来添加卡片';

  @override
  String get textCardDaysAgo => '天前';

  @override
  String get textCardHoursAgo => '小时前';

  @override
  String get textCardMinutesAgo => '分钟前';

  @override
  String get textCardJustNow => '刚刚';

  @override
  String get textCardPureTextCustomRendering => '纯文本定制渲染';

  @override
  String get textCardRenderContentToCards => '将您的内容渲染成精美的卡片';

  @override
  String get textCardDesignDescription =>
      '这是一个分离式的设计：简单的编辑器用于内容编辑和拆分，强大的可视化渲染器用于样式定制和最终展示。';

  @override
  String get textCardSimpleEdit => '简单编辑';

  @override
  String get textCardSimpleEditDesc => '专注于纯文本编辑和内容拆分，无复杂格式干扰';

  @override
  String get textCardVisualRendering => '可视化渲染';

  @override
  String get textCardVisualRenderingDesc => '所见即所得的样式定制，选中文本直接修改样式';

  @override
  String get textCardSmartRecognition => '智能识别';

  @override
  String get textCardSmartRecognitionDesc => '自动识别标题、列表、引用等内容类型并美化渲染';

  @override
  String get textCardExportShare => '导出分享';

  @override
  String get textCardExportShareDesc => '支持单卡片和批量导出，轻松分享精美内容';

  @override
  String get textCardCoreFeatures => '核心功能';

  @override
  String get textCardMarkdownTip => '提示：支持Markdown格式的文本输入，包括标题、列表、引用等';

  @override
  String get textCardStartCreating => '开始创建';

  @override
  String get textCardClickToEditContent => '点击编辑内容';

  @override
  String get textCardsLightCategory => '明亮';

  @override
  String get textCardsDarkCategory => '暗黑';

  @override
  String get textCardsNatureCategory => '自然';

  @override
  String get textCardsWarmCategory => '温暖';

  @override
  String get textCardsTechCategory => '科技';

  @override
  String get textCardsElegantCategory => '优雅';

  @override
  String get textCardsVintageCategory => '复古';

  @override
  String get textCardsPreviewEffect => '预览效果';

  @override
  String get textCardCreateBeautifulCard => '创建精美卡片';

  @override
  String get textCardsSelectTextToModifyStyle => '选中文本可以修改字体、颜色和大小';

  @override
  String get textCardsSelectTemplate => '选择模板';

  @override
  String get textCardsTemplateGallery => '模板画廊';

  @override
  String get blockMarkdown => '分块Markdown';

  @override
  String get cardCollection => '卡片合集';

  @override
  String get contentDefaultsTitle => '默认示例内容';

  @override
  String get markdownDefaultSampleTitle => 'Markdown 默认示例';

  @override
  String get markdownDefaultSampleDesc => '进入 Markdown 时自动填充示例内容';

  @override
  String get textCardsDefaultSampleTitle => '文本卡片 默认示例';

  @override
  String get textCardsDefaultSampleDesc => '进入文本卡片时自动填充示例内容';

  @override
  String get svgBuiltInPresetTitle => 'SVG 内置示例';

  @override
  String get svgBuiltInPresetDesc => '当没有文档时，显示内置 SVG 预设';

  @override
  String get htmlDefaultSampleTitle => 'HTML 默认示例';

  @override
  String get htmlDefaultSampleDesc => '当没有文档时，加载内置 HTML 示例';

  @override
  String get transformerExampleInputTitle => '文本转换 示例输入';

  @override
  String get transformerExampleInputDesc => '进入文本转换时自动填充示例输入';

  @override
  String get privacyPolicyTitle => '隐私政策';

  @override
  String get privacyPolicySubtitle => '查看我们的隐私政策';

  @override
  String get openSourceLicensesTitle => '开源许可';

  @override
  String get openSourceLicensesSubtitle => '查看第三方开源库许可';

  @override
  String get subscriptionUpgradeTitle => '升级到高级版';

  @override
  String get subscriptionUpgradeSubtitle => '解锁所有高级功能，提升您的AI体验';

  @override
  String get subscriptionChoosePlan => '选择您的订阅计划';

  @override
  String subscriptionDiscountSavePercent(Object percent) {
    return '省$percent%';
  }

  @override
  String get subscriptionLoadingPrice => '价格加载中…';

  @override
  String subscriptionEquivalentToPerMonth(Object price) {
    return '相当于 $price';
  }

  @override
  String get subscriptionIncludedFeatures => '包含的功能';

  @override
  String subscriptionSubscribeNowWithPrice(Object price) {
    return '立即订阅 $price';
  }

  @override
  String get subscriptionAgreementPrefix => '订阅即表示您同意我们的';

  @override
  String get termsOfService => '服务条款';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get subscriptionAgreementSuffix => '。订阅会自动续费，可随时取消。';

  @override
  String get subscriptionDevModeNotice =>
      '开发模式：订阅功能当前使用模拟数据，App Store Connect批准后将使用真实数据';

  @override
  String get diagnosticsTitle => '诊断结果';

  @override
  String get diagnosticsClose => '关闭';

  @override
  String get subscriptionDiagnosticsButton => '诊断购买服务';

  @override
  String get restoreCompletedTitle => '恢复完成';

  @override
  String get restoreFailedTitle => '恢复失败';

  @override
  String get restoreCompletedMessage => '您的购买已成功恢复。';

  @override
  String restoreFailedMessageWithError(Object error) {
    return '恢复购买时发生错误：$error';
  }

  @override
  String get andText => '和';

  @override
  String get subscriptionPlanMonthlyName => '内容君专业版（月度）';

  @override
  String get subscriptionPlanMonthlyDesc => '解锁所有高级内容处理功能';

  @override
  String get subscriptionPlanYearlyName => '内容君专业版（年度）';

  @override
  String get subscriptionPlanYearlyDesc => '年度订阅，更省哦';

  @override
  String get subscriptionPlanLifetimeName => '内容君专业版（永久）';

  @override
  String get subscriptionPlanLifetimeDesc => '一次性付费，终身使用';

  @override
  String get subscriptionPlanFreeName => '免费版';

  @override
  String get subscriptionPlanFreeDesc => '基础内容处理功能';

  @override
  String get subscriptionFeatureUnlimitedExportsName => '无限导出';

  @override
  String get subscriptionFeatureUnlimitedExportsDesc => '无限制导出处理后的内容';

  @override
  String get subscriptionFeatureBatchProcessingName => '批量处理';

  @override
  String get subscriptionFeatureBatchProcessingDesc => '支持批量处理多个文件';

  @override
  String get subscriptionFeatureAdvancedToolsName => '高级工具';

  @override
  String get subscriptionFeatureAdvancedToolsDesc => '访问所有高级编辑和处理工具';

  @override
  String get subscriptionFeatureNoWatermarkName => '无水印';

  @override
  String get subscriptionFeatureNoWatermarkDesc => '导出内容不含水印';

  @override
  String get subscriptionFeaturePrioritySupportName => '优先支持';

  @override
  String get subscriptionFeaturePrioritySupportDesc => '获得优先客户支持';

  @override
  String get subscriptionFeatureFutureUpdatesName => '未来更新';

  @override
  String get subscriptionFeatureFutureUpdatesDesc => '获得所有未来功能更新';

  @override
  String get subscriptionFeatureBasicProcessingName => '基础处理';

  @override
  String get subscriptionFeatureBasicProcessingDesc => '基础内容处理功能';

  @override
  String get subscriptionFeatureWatermarkedExportsName => '带水印导出';

  @override
  String get subscriptionFeatureWatermarkedExportsDesc => '导出内容带水印';

  @override
  String get subscriptionPeriodPerMonthSuffix => '/月';

  @override
  String get subscriptionPeriodPerYearSuffix => '/年';

  @override
  String get subscriptionPeriodLifetime => '终身';

  @override
  String get subscriptionPeriodFree => '免费';

  @override
  String get purchaseSuccessMessage => '购买成功，订阅已激活。感谢支持！';

  @override
  String get currentPlan => '当前计划';

  @override
  String get expired => '已过期';

  @override
  String daysUntilExpiry(int days) {
    return '$days天后到期';
  }

  @override
  String get subscriptionExpired => '已过期';

  @override
  String get textCardsBrowseTemplates => '浏览模板';

  @override
  String get textCardsBeautifulTemplates => '个精美模板，找到最适合的设计';

  @override
  String get textCardsAllCategories => '全部';

  @override
  String get textCardsBusinessCategory => '商务';

  @override
  String get textCardsAcademicCategory => '学术';

  @override
  String get textCardsCreativeCategory => '创意';

  @override
  String get textCardsMinimalCategory => '简约';

  @override
  String get textCardsModernCategory => '现代';

  @override
  String get textCardExportSettings => '导出设置';

  @override
  String get textCardImageSize => '图片尺寸';

  @override
  String get textCardImageRatio => '图片比例';

  @override
  String get smartTextSplitter => '智能文本拆分';

  @override
  String get smartTextSplitterSubtitle => '将长文本智能分割为多个卡片';

  @override
  String characterCount(int count) {
    return '字符数：$count';
  }

  @override
  String get splitConfig => '拆分配置';

  @override
  String get splitConfigDescription => '选择拆分模式和相关参数';

  @override
  String get splitMode => '拆分模式';

  @override
  String get customSeparator => '自定义分隔符';

  @override
  String get customSeparatorHint => '输入分隔符，如：---';

  @override
  String get advancedOptions => '高级选项';

  @override
  String get autoDetectTitles => '自动识别标题';

  @override
  String get autoDetectTitlesDescription => '智能识别文本中的标题内容';

  @override
  String get preserveFormatting => '保留格式';

  @override
  String get preserveFormattingDescription => '保留原文本的Markdown格式';

  @override
  String get smartMerge => '智能合并';

  @override
  String get smartMergeDescription => '自动合并过短的段落';

  @override
  String get maxLength => '最大长度';

  @override
  String get maxLengthDescription => '每个卡片的最大字符数';

  @override
  String get splitPreview => '拆分预览';

  @override
  String totalCards(int count) {
    return '共 $count 个卡片';
  }

  @override
  String get splitPreviewDescription => '预览拆分结果，可以编辑、合并或删除卡片';

  @override
  String get deselectAll => '取消选择';

  @override
  String get deleteSelected => '删除选中';

  @override
  String get noSplitResults => '没有拆分结果';

  @override
  String get noSplitResultsDescription => '请返回上一步检查输入文本和配置';

  @override
  String get previousStep => '上一步';

  @override
  String get nextStep => '下一步';

  @override
  String get startSplitting => '开始拆分';

  @override
  String get createCards => '创建卡片';

  @override
  String get inputTextHint => '在这里输入或粘贴文本内容...';

  @override
  String get titleOptional => '标题（可选）';

  @override
  String get exportSettings => '导出设置';

  @override
  String get confirmExport => '确定导出';

  @override
  String get previewInfo => '预览信息';

  @override
  String get dimensions => '尺寸';

  @override
  String get ratio => '比例';

  @override
  String get qualityPercent => '质量';

  @override
  String get watermarkStatus => '水印';

  @override
  String get include => '包含';

  @override
  String get notInclude => '不包含';

  @override
  String get pixels => '像素';

  @override
  String get pdfCreatedTime => 'Created';

  @override
  String get pdfModifiedTime => 'Modified';

  @override
  String get pdfPermissionsSuccess => 'Permissions set successfully';

  @override
  String get pdfPermissionsFailed => 'Permissions set failed';

  @override
  String pdfSettingsFailed(Object error) {
    return 'Settings failed: $error';
  }

  @override
  String get pdfDocumentInformation => 'Document Information';

  @override
  String get pdfEncryptedMessage =>
      'This PDF is encrypted, please enter password to decrypt';

  @override
  String get pdfSecurityEncrypted => 'Encrypted';

  @override
  String get pdfSecurityReadOnly => 'Read Only';

  @override
  String get pdfSecurityRestricted => 'Restricted';

  @override
  String get pdfSecurityOpen => 'Open';

  @override
  String get pdfAllowCopying => 'Allow Copying';

  @override
  String get pdfMultipleFormats => 'Multiple Formats';

  @override
  String get pdfMultiDeviceSync => 'Multi-device Sync';

  @override
  String get pdfProtectYourDocuments => 'Protect your important documents';

  @override
  String get pdfPasswordProtectionDesc =>
      'Set user and owner passwords for PDF documents to ensure document security';

  @override
  String get pdfPermissionControlDesc =>
      'Fine-grained control over document printing, copying, editing and other permissions';

  @override
  String get pdfEncryptionAlgorithm => 'Encryption Algorithm';

  @override
  String get pdfEncryptionAlgorithmDesc =>
      'Adopt industry-standard AES encryption algorithm to ensure document security';

  @override
  String get pdfImportToStart => 'Import PDF to Start';

  @override
  String get pdfUsageTips => 'Usage Tips';

  @override
  String get htmlUntitled => 'Untitled HTML';

  @override
  String get htmlNewDocumentTitle => 'New HTML Document';

  @override
  String get htmlCopy => 'Copy';

  @override
  String get htmlRenderError => 'Cannot render HTML content';

  @override
  String get voiceHomeTitle => 'Voice Assistant';

  @override
  String get voiceHomeSubtitle =>
      'Record ideas, convert to text, smart reading';

  @override
  String get voiceUsageStats => 'Usage Statistics';

  @override
  String get voiceRecordingCount => 'Recording Count';

  @override
  String get voiceRecordingsUnit => 'recordings';

  @override
  String get voiceTotalDuration => 'Total Duration';

  @override
  String get voiceCumulativeDuration => 'Cumulative Duration';

  @override
  String get voiceQuickActions => 'Quick Actions';

  @override
  String get voiceRecordNewVoice => 'Record new voice';

  @override
  String get voiceTextToSpeech => 'Text to Speech';

  @override
  String get voiceConvertTextToVoice => 'Convert text to voice';

  @override
  String get voicePowerfulFeatures => 'Powerful Features';

  @override
  String get voiceSmartTranscription => 'Smart Transcription';

  @override
  String get voiceSmartTranscriptionDesc =>
      'Automatically convert voice to text';

  @override
  String get voiceAudioAdjustment => 'Audio Adjustment';

  @override
  String get voiceAudioAdjustmentDesc => 'Adjust speed, pitch and volume';

  @override
  String get voicePlaylist => 'Playlist';

  @override
  String get voicePlaylistDesc => 'Manage and play multiple audio files';

  @override
  String get voiceCloudSync => 'Cloud Sync';

  @override
  String get voiceCloudSyncDesc => 'Sync your recordings across devices';

  @override
  String get voiceRecentRecordings => 'Recent Recordings';

  @override
  String get voiceViewAll => 'View All';

  @override
  String get voiceNoRecordingsYet => 'No recordings yet';

  @override
  String get voiceStartFirstRecording =>
      'Click the button below to start your first recording';

  @override
  String get voiceRecordDetailTitle => 'Voice Details';

  @override
  String get voiceSaveAllChanges => 'Save all changes';

  @override
  String get voiceRecordingFileMayBeCorrupted =>
      'Recording file may be corrupted, cannot play';

  @override
  String get voiceAudioFileNotExistOrCorrupted =>
      'Audio file does not exist or is corrupted';

  @override
  String voiceFilePath(Object path) {
    return 'File path: $path';
  }

  @override
  String get voiceRecheck => 'Recheck';

  @override
  String voiceFileStatusRechecked(Object corrupted, Object status) {
    return 'File status rechecked: $status$corrupted';
  }

  @override
  String get voiceFileExists => 'file exists';

  @override
  String get voiceFileNotExists => 'file does not exist';

  @override
  String get voiceFileButCorrupted => ', but file may be corrupted';

  @override
  String get voiceVoiceTranscription => 'Voice Transcription';

  @override
  String get voiceReadText => 'Read Text';

  @override
  String get voiceNoTranscriptionText => 'No transcription text yet';

  @override
  String get voiceSaveTranscriptionText => 'Save Transcription Text';

  @override
  String voiceCreateTime(Object time) {
    return 'Create Time: $time';
  }

  @override
  String get voicePlaying => 'Playing...';

  @override
  String get voicePaused => 'Paused';

  @override
  String get voiceAudioFileNotExist => 'Audio file does not exist, cannot play';

  @override
  String get voiceAudioDurationAbnormal =>
      'Audio duration abnormal, may not play properly';

  @override
  String voiceLoadAudioFailed(Object error) {
    return 'Failed to load audio: $error';
  }

  @override
  String voicePlayFailed(Object error) {
    return 'Play failed: $error';
  }

  @override
  String get voiceTitleSaved => 'Title saved';

  @override
  String get voiceTranscriptionTextSaved => 'Transcription text saved';

  @override
  String get voiceTtsPlayerTitle => 'Text to Speech';

  @override
  String get voiceInputTextToRead => 'Input text to read';

  @override
  String get voiceInputTextHint =>
      'Input text to read, click \"Add\" button to add to playlist';

  @override
  String get voiceAddToPlaylist => 'Add to playlist';

  @override
  String get voiceAddedToPlaylist => 'Added to playlist';

  @override
  String get voiceTtsSettings => 'TTS Settings';

  @override
  String get voiceSpeechRate => 'Speech Rate:';

  @override
  String get voicePitch => 'Pitch:';

  @override
  String get voiceVolume => 'Volume:';

  @override
  String get voiceLanguage => 'Language:';

  @override
  String get voicePlaylistTitle => 'Playlist';

  @override
  String get voiceStop => 'Stop';

  @override
  String get voicePlaylistEmpty => 'Playlist is empty';

  @override
  String get voiceTranscriptionTitle => 'Smart Transcription';

  @override
  String get voiceRealtimeTranscription => 'Realtime Transcription';

  @override
  String get voiceFileTranscription => 'File Transcription';

  @override
  String get voiceBatchTranscription => 'Batch Transcription';

  @override
  String get voiceSelectAudioFile => 'Select audio file for transcription';

  @override
  String get voiceFileSelected => 'File Selected';

  @override
  String get voiceSelectFile => 'Select File';

  @override
  String get voiceReselectFile => 'Reselect File';

  @override
  String get voiceTranscriptionResult => 'Transcription Result';

  @override
  String get voiceBatchTranscriptionFeature => 'Batch Transcription Feature';

  @override
  String get voiceSelectMultipleFiles =>
      'Select multiple files for batch transcription';

  @override
  String get voiceSelectMultipleFilesBtn => 'Select Multiple Files';

  @override
  String get voiceBatchProcessingProgress => 'Batch Processing Progress';

  @override
  String get voiceBatchTranscriptionInDev =>
      'Batch transcription feature in development...';

  @override
  String get voiceReadyToStart => 'Ready to start';

  @override
  String get voiceTranscribing => 'Transcribing...';

  @override
  String get voiceClickToStartRealtime =>
      'Click button below to start realtime transcription';

  @override
  String get voiceExport => 'Export';

  @override
  String get voiceShare => 'Share';

  @override
  String get voiceNoTranscriptionContent =>
      'No transcription content to export';

  @override
  String get voiceNoTranscriptionContentToShare =>
      'No transcription content to share';

  @override
  String get voiceExportFeatureInDev => 'Export feature in development...';

  @override
  String get voiceShareFeatureInDev => 'Share feature in development...';

  @override
  String get voiceTranscriptionSettings => 'Transcription Settings';

  @override
  String get voiceEnablePunctuation => 'Enable Punctuation';

  @override
  String get voiceAutoAddPunctuation => 'Automatically add punctuation';

  @override
  String get voiceSpeakerDetection => 'Speaker Detection';

  @override
  String get voiceDetectDifferentSpeakers => 'Detect different speakers';

  @override
  String get voiceConfidenceThreshold => 'Confidence Threshold';

  @override
  String get voicePermissionRequiredMessage =>
      'Voice transcription feature requires microphone permission. Please enable microphone access in settings.';

  @override
  String get voiceRecordingComplete => 'Recording complete';

  @override
  String get voiceRecordingFailedRetry => 'Recording failed, please retry';

  @override
  String voiceSelectFileFailed(Object error) {
    return 'Failed to select file: $error';
  }

  @override
  String voiceProcessAudioFileFailed(Object error) {
    return 'Failed to process audio file: $error';
  }

  @override
  String voiceSelectBatchFilesFailed(Object error) {
    return 'Failed to select batch files: $error';
  }

  @override
  String voiceFilesSelected(Object count) {
    return 'Selected $count files';
  }

  @override
  String get voiceNoTranscriptionContentToSave =>
      'No transcription content to save';

  @override
  String get voiceSaveTranscriptionResult => 'Save Transcription Result';

  @override
  String get voiceTranscriptionContentPreview =>
      'Transcription content preview:';

  @override
  String get voiceTranscriptionResultSaved => 'Transcription result saved';

  @override
  String voiceSaveFailed(Object error) {
    return 'Save failed: $error';
  }

  @override
  String get voiceTranscriptionFailedRetry =>
      'Transcription failed, please retry';

  @override
  String get voiceSmartTranscriptionPageTitle => 'Smart Transcription';

  @override
  String get voiceInitializationFailedCheckPermission =>
      'Initialization failed, please check microphone permission';

  @override
  String voiceInitializationException(Object error) {
    return 'Initialization exception: $error';
  }

  @override
  String get voiceServiceInitializationFailed =>
      'Service initialization failed';

  @override
  String get voiceStartTranscriptionFailed => 'Failed to start transcription';

  @override
  String get voiceTranscriptionIdle => 'Ready';

  @override
  String get voiceTranscriptionInProgress => 'Transcribing...';

  @override
  String get voiceTranscriptionCompleted => 'Transcription Completed';

  @override
  String get voiceTranscriptionError => 'Transcription Error';

  @override
  String get voiceLanguageSelector => 'Language:';

  @override
  String get voiceTranscriptionResultTitle => 'Transcription Result';

  @override
  String get voiceClickToStartTranscription =>
      'Click start button to begin transcription...';

  @override
  String get voiceStopTranscription => 'Stop Transcription';

  @override
  String get voiceClear => 'Clear';

  @override
  String get voiceIosPermissionTestTitle => 'iOS Permission Test';

  @override
  String get voiceDirectIosPermissionTest => 'Direct iOS Permission Test';

  @override
  String get voicePageLoaded => 'Page loaded';

  @override
  String voiceInitialMicPermissionStatus(Object status) {
    return 'Initial microphone permission status: $status';
  }

  @override
  String voiceInitialSpeechPermissionStatus(Object status) {
    return 'Initial speech recognition permission status: $status';
  }

  @override
  String get voiceNonIosPlatform =>
      'Non-iOS platform, not checking permissions';

  @override
  String get voiceRequestMicPermission => 'Request Microphone Permission';

  @override
  String voiceMicPermissionStatus(Object status) {
    return 'Microphone permission status: $status';
  }

  @override
  String get voiceRequestSpeechPermission =>
      'Request Speech Recognition Permission';

  @override
  String voiceSpeechPermissionStatus(Object status) {
    return 'Speech recognition permission status: $status';
  }

  @override
  String get voiceTestRecording => 'Test Recording';

  @override
  String get voiceTestRecordingFunction => 'Test recording function...';

  @override
  String get voiceRecorderInstanceCreated => 'Recorder instance created';

  @override
  String get voiceRecorderInitialized => 'Recorder initialized';

  @override
  String get voiceRecorderTestComplete => 'Recorder test complete';

  @override
  String get voiceRecorderClosed => 'Recorder closed';

  @override
  String voiceRecorderError(Object error) {
    return 'Recorder error: $error';
  }

  @override
  String get voiceTestSpeechRecognition => 'Test Speech Recognition';

  @override
  String get voiceTestSpeechRecognitionFunction => 'Test speech recognition...';

  @override
  String voiceSpeechRecognitionError(Object error) {
    return 'Speech recognition error: $error';
  }

  @override
  String voiceSpeechRecognitionStatus(Object status) {
    return 'Speech recognition status: $status';
  }

  @override
  String voiceSpeechRecognitionInit(Object success) {
    return 'Speech recognition init: $success';
  }

  @override
  String get voiceStartListening => 'Start listening...';

  @override
  String voiceRecognitionResult(Object result) {
    return 'Recognition result: $result';
  }

  @override
  String get voiceStopListening => 'Stop listening';

  @override
  String voiceSpeechRecognitionTestError(Object error) {
    return 'Speech recognition test error: $error';
  }

  @override
  String get voiceOpenAppSettings => 'Open App Settings';

  @override
  String get voiceOperationLog => 'Operation Log:';

  @override
  String get voiceIosPageLoaded => 'Page loaded';

  @override
  String get voiceIosInitialMicrophonePermission =>
      'Initial microphone permission status';

  @override
  String get voiceIosInitialSpeechPermission =>
      'Initial speech recognition permission status';

  @override
  String get voiceIosNonIosPlatform =>
      'Non-iOS platform, not checking permissions';

  @override
  String get voiceIosRequestMicrophonePermission =>
      'Requesting microphone permission...';

  @override
  String get voiceIosMicrophonePermissionStatus =>
      'Microphone permission status';

  @override
  String get voiceIosRequestSpeechPermission =>
      'Requesting speech recognition permission...';

  @override
  String get voiceIosSpeechPermissionStatus =>
      'Speech recognition permission status';

  @override
  String get voiceIosTestRecordingFunction => 'Testing recording function...';

  @override
  String get voiceIosRecorderInstanceCreated => 'Recorder instance created';

  @override
  String get voiceIosRecorderInitialized => 'Recorder initialized';

  @override
  String get voiceIosRecorderTestCompleted => 'Recorder test completed';

  @override
  String get voiceIosRecorderClosed => 'Recorder closed';

  @override
  String get voiceIosRecorderError => 'Recorder error';

  @override
  String get voiceIosTestSpeechRecognition => 'Testing speech recognition...';

  @override
  String get voiceIosSpeechRecognitionError => 'Speech recognition error';

  @override
  String get voiceIosSpeechRecognitionStatus => 'Speech recognition status';

  @override
  String get voiceIosSpeechRecognitionInitialization =>
      'Speech recognition initialization';

  @override
  String get voiceIosSuccess => 'Success';

  @override
  String get voiceIosFailed => 'Failed';

  @override
  String get voiceIosStartListening => 'Start listening...';

  @override
  String get voiceIosRecognitionResult => 'Recognition result';

  @override
  String get voiceIosStopListening => 'Stop listening';

  @override
  String get voiceIosSpeechRecognitionTestError =>
      'Speech recognition test error';

  @override
  String get voiceIosOpenAppSettings => 'Open app settings';

  @override
  String get voiceIosPermissionTest => 'iOS Permission Test';

  @override
  String get voiceIosDirectPermissionTest => 'Direct iOS Permission Test';

  @override
  String get voiceIosOperationLogs => 'Operation Logs';

  @override
  String get voiceOK => 'OK';

  @override
  String get voiceMicrophonePermissionRequired =>
      'Voice transcription requires microphone permission. Please allow microphone access in settings.';

  @override
  String get voiceLanguageChineseSimplified => '中文（简体）';

  @override
  String get voiceLanguageChineseTraditional => '中文（繁体）';

  @override
  String get voiceLanguageEnglish => 'English (US)';

  @override
  String get voiceLanguageJapanese => '日本語';

  @override
  String get voiceLanguageKorean => '한국어';

  @override
  String get trafficGuideWatermarkTitle => '文本水印处理';

  @override
  String get trafficGuideAddWatermarkModeSubtitle => '在文本中添加可见或隐形水印';

  @override
  String get trafficGuideRemoveWatermarkModeSubtitle => '从文本中移除已添加的水印';

  @override
  String get trafficGuideVisibleWatermark => '可见水印';

  @override
  String get trafficGuideWatermarkType => '水印类型';

  @override
  String trafficGuideProcessFailed(Object error) {
    return '处理失败: $error';
  }

  @override
  String get trafficGuideShowPreview => '显示预览';

  @override
  String get trafficGuideHidePreview => '隐藏预览';

  @override
  String get trafficGuideProcessSuccess => '处理成功';

  @override
  String get trafficGuideDetectedWatermark => '检测到水印';

  @override
  String get trafficGuideUnknownWatermark => '未知水印';

  @override
  String get trafficGuideNoWatermarkDetected => '未检测到水印';

  @override
  String get trafficGuideProcessedText => '处理后的文本';

  @override
  String get trafficGuideInvisibleWatermarkInfo =>
      '这将使用特殊的Unicode字符添加不可见水印，读者无法看到但可以被此工具检测到。';

  @override
  String get trafficGuideWatermarkRemovedSuccess => '水印移除成功！';

  @override
  String get trafficGuideWatermarkAddedSuccess => '水印添加成功！';

  @override
  String get exportSocialWeChatMoments => '微信朋友圈';

  @override
  String get exportSocialWeibo => '微博配图';

  @override
  String get exportSocialXiaohongshu => '小红书';

  @override
  String get exportSocialInstagram => 'Instagram';

  @override
  String get exportSocialTwitter => 'Twitter';

  @override
  String get exportWidthLabel => '宽度';

  @override
  String get exportHeightLabel => '高度';

  @override
  String get exportOptimizeForSocial => '社交媒体优化';

  @override
  String get exportOptimizeForSocialSubtitle => '针对社交平台优化尺寸';

  @override
  String get exportSocialPlatformSizes => '社交平台尺寸';

  @override
  String get exportSizeSmall => '小 (400×300)';

  @override
  String get exportSizeMedium => '中 (800×600)';

  @override
  String get exportSizeLarge => '大 (1200×900)';

  @override
  String get exportSizeCustom => '自定义';

  @override
  String get general => '通用';

  @override
  String get appearanceAndBrightness => '显示与亮度';

  @override
  String get privacyAndSecurity => '隐私与安全';

  @override
  String get storage => '存储';

  @override
  String get support => '支持';

  @override
  String get languageAndRegion => '语言与地区';

  @override
  String get privacySettings => '隐私设置';

  @override
  String get storageManagement => '存储管理';

  @override
  String get viewStorageUsage => '查看存储使用情况';

  @override
  String get dataImportExport => '数据导入导出';

  @override
  String get backupAndRestoreData => '备份和恢复数据';

  @override
  String get helpCenter => '帮助中心';

  @override
  String get feedback => '意见反馈';

  @override
  String get rateApp => '评价应用';

  @override
  String get aboutApp => '关于应用';

  @override
  String get dataManagement => '数据管理';

  @override
  String get selectOperation => '选择要执行的操作';

  @override
  String get exportData => '导出数据';

  @override
  String get importData => '导入数据';

  @override
  String get createBackup => '创建备份';

  @override
  String get dataExportInProgress => '数据导出功能开发中...';

  @override
  String get dataImportInProgress => '数据导入功能开发中...';

  @override
  String get backupInProgress => '备份功能开发中...';

  @override
  String get doYouLikeThisApp => '您喜欢这个应用吗？请在App Store中为我们评分！';

  @override
  String get later => '稍后';

  @override
  String get goToRate => '去评价';

  @override
  String get cannotOpenAppStore => '无法打开App Store';

  @override
  String get errorOpeningAppStore => '打开App Store时出现错误';

  @override
  String get understand => '了解';

  @override
  String get weValueYourPrivacy => '我们重视您的隐私。所有数据处理都在本地进行，不会上传到服务器。';

  @override
  String get manageDataAnytime => '您可以随时在设置中管理您的数据。';

  @override
  String languageSelected(Object language) {
    return '已选择语言：$language';
  }

  @override
  String get light => '浅色';

  @override
  String get dark => '深色';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get selectAppearance => '选择外观';

  @override
  String get traditionalChinese => '繁體中文';

  @override
  String get simplifiedChinese => '简体中文';

  @override
  String get contentSaveButtonFavorite => '收藏';

  @override
  String get textTransformerSelectMode => '选择转换模式';

  @override
  String get textTransformerInputText => '输入文本';

  @override
  String get textTransformerOutputResult => '转换结果';

  @override
  String get textTransformerHint => '在这里输入要转换的文本...';

  @override
  String get textTransformerOutputHint => '转换后的文本将显示在这里...';

  @override
  String get textTransformerCharacters => '字符';

  @override
  String get textTransformerTransform => '切换效果';

  @override
  String get textTransformerTransforming => '转换中...';

  @override
  String get textTransformerClearAll => '清空所有';

  @override
  String get textTransformerCopyResult => '复制结果';

  @override
  String get textTransformerCopied => '已复制到剪贴板';

  @override
  String get textTransformerTemplateEmojiName => '表情符号转换';

  @override
  String get textTransformerTemplateEmojiDesc => '将文字转换为特殊表情符号';

  @override
  String get textTransformerTemplateFancyName => '花体字母';

  @override
  String get textTransformerTemplateFancyDesc => '转换为优雅的花体字母';

  @override
  String get textTransformerTemplateBoldName => '粗体文字';

  @override
  String get textTransformerTemplateBoldDesc => '转换为粗体Unicode字符';

  @override
  String get textTransformerTemplateDecorativeName => '装饰文字';

  @override
  String get textTransformerTemplateDecorativeDesc => '添加装饰性符号';

  @override
  String get textTransformerTemplateMixedName => '混合效果';

  @override
  String get textTransformerTemplateMixedDesc => '随机组合多种转换效果';

  @override
  String get textTransformerTemplateInvisibleName => '隐形字符';

  @override
  String get textTransformerTemplateInvisibleDesc => '添加不可见字符绕过检测';

  @override
  String get textTransformerTemplateUnicodeName => 'Unicode变体';

  @override
  String get textTransformerTemplateUnicodeDesc => '使用Unicode变体字符';

  @override
  String get textTransformerEffectEmojiDesc => '数字和字母转换为特殊Unicode字符';

  @override
  String get textTransformerEffectFancyDesc => '转换为优雅的花体字母';

  @override
  String get textTransformerEffectBoldDesc => '转换为粗体Unicode字符';

  @override
  String get textTransformerEffectDecorativeDesc => '添加装饰性符号';

  @override
  String get textTransformerEffectMixedDesc => '随机组合多种转换效果';

  @override
  String get textTransformerEffectInvisibleDesc => '在字符间添加不可见字符，绕过检测';

  @override
  String get textTransformerEffectUnicodeDesc => '添加变音符号，改变字符外观';

  @override
  String get textTransformerSample => '测试文本';

  @override
  String get textTransformerSampleInvisible => '敏感内容检测绕过测试';

  @override
  String get textTransformerSampleUnicode => '特殊字符转换测试';
}
