import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ja'),
    Locale('zh'),
  ];

  /// The name of the application
  ///
  /// In en, this message translates to:
  /// **'ContentPal'**
  String get appName;

  /// The Chinese name of the application
  ///
  /// In en, this message translates to:
  /// **'内容君'**
  String get appNameChinese;

  /// Description of the application
  ///
  /// In en, this message translates to:
  /// **'Professional content processing tool that makes content creation easier'**
  String get appDescription;

  /// Home page title
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Settings page title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Language setting label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Theme setting label
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// Light theme option
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get lightTheme;

  /// Dark theme option
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get darkTheme;

  /// System theme option
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get systemTheme;

  /// Title for shared text content
  ///
  /// In en, this message translates to:
  /// **'Shared Text'**
  String get sharedText;

  /// Error message when initial intent cannot be retrieved
  ///
  /// In en, this message translates to:
  /// **'Failed to get initial intent: {error}'**
  String failedToGetInitialIntent(Object error);

  /// Error message when theme settings cannot be loaded
  ///
  /// In en, this message translates to:
  /// **'Failed to load theme settings: {error}'**
  String failedToLoadThemeSettings(Object error);

  /// Material You theme name
  ///
  /// In en, this message translates to:
  /// **'Material You'**
  String get themeMaterialYou;

  /// Morandi theme name
  ///
  /// In en, this message translates to:
  /// **'Morandi Style'**
  String get themeMorandi;

  /// Monochrome theme name
  ///
  /// In en, this message translates to:
  /// **'Minimalist Black & White'**
  String get themeMonochrome;

  /// Nature theme name
  ///
  /// In en, this message translates to:
  /// **'Natural Colors'**
  String get themeNature;

  /// Technology theme name
  ///
  /// In en, this message translates to:
  /// **'Tech Style'**
  String get themeTech;

  /// Chinese traditional colors theme name
  ///
  /// In en, this message translates to:
  /// **'Traditional Chinese Colors'**
  String get themeChinese;

  /// Material You theme description
  ///
  /// In en, this message translates to:
  /// **'Dynamic theme automatically extracted from wallpaper'**
  String get themeMaterialYouDesc;

  /// Morandi theme description
  ///
  /// In en, this message translates to:
  /// **'Soft and elegant Morandi color palette'**
  String get themeMorandiDesc;

  /// Monochrome theme description
  ///
  /// In en, this message translates to:
  /// **'Simple and pure black and white color scheme'**
  String get themeMonochromeDesc;

  /// Nature theme description
  ///
  /// In en, this message translates to:
  /// **'Comfortable and natural ecological color system'**
  String get themeNatureDesc;

  /// Technology theme description
  ///
  /// In en, this message translates to:
  /// **'Futuristic technology colors'**
  String get themeTechDesc;

  /// Chinese traditional colors theme description
  ///
  /// In en, this message translates to:
  /// **'Eastern aesthetics combining tradition and modernity'**
  String get themeChineseDesc;

  /// Markdown module name
  ///
  /// In en, this message translates to:
  /// **'Markdown'**
  String get markdown;

  /// Text Cards module name
  ///
  /// In en, this message translates to:
  /// **'Text Cards'**
  String get textCards;

  /// Color picker dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Color'**
  String get textCardSelectColor;

  /// No description provided for @textCardStyleApplied.
  ///
  /// In en, this message translates to:
  /// **'Style Applied'**
  String get textCardStyleApplied;

  /// Split failed error message
  ///
  /// In en, this message translates to:
  /// **'Split failed: {error}'**
  String textCardSplitFailed(Object error);

  /// Create failed error message
  ///
  /// In en, this message translates to:
  /// **'Create failed: {error}'**
  String textCardCreateFailed(Object error);

  /// Title for card editing dialog
  ///
  /// In en, this message translates to:
  /// **'Edit Card'**
  String get textCardEditCard;

  /// No description provided for @textCardPreviousStep.
  ///
  /// In en, this message translates to:
  /// **'Previous Step'**
  String get textCardPreviousStep;

  /// No description provided for @textCardSaveToContentLibrary.
  ///
  /// In en, this message translates to:
  /// **'Save to Content Library'**
  String get textCardSaveToContentLibrary;

  /// No description provided for @textCardStartExportingImage.
  ///
  /// In en, this message translates to:
  /// **'Starting image export...'**
  String get textCardStartExportingImage;

  /// Success message when image is saved
  ///
  /// In en, this message translates to:
  /// **'✅ Image successfully saved to photo album'**
  String get textCardImageSavedSuccess;

  /// No description provided for @textCardPleaseEnterContent.
  ///
  /// In en, this message translates to:
  /// **'Please enter card content'**
  String get textCardPleaseEnterContent;

  /// No description provided for @textCardDeleteCard.
  ///
  /// In en, this message translates to:
  /// **'Delete Card'**
  String get textCardDeleteCard;

  /// No description provided for @textCardDeleteConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this card? This action cannot be undone.'**
  String get textCardDeleteConfirm;

  /// No description provided for @textCardCategory.
  ///
  /// In en, this message translates to:
  /// **'Category: {category}'**
  String textCardCategory(Object category);

  /// No description provided for @textCardDescription.
  ///
  /// In en, this message translates to:
  /// **'Description: {description}'**
  String textCardDescription(Object description);

  /// No description provided for @textCardClose.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get textCardClose;

  /// No description provided for @textCardUseTemplate.
  ///
  /// In en, this message translates to:
  /// **'Use Template'**
  String get textCardUseTemplate;

  /// No description provided for @textCardConfirmExport.
  ///
  /// In en, this message translates to:
  /// **'Confirm Export'**
  String get textCardConfirmExport;

  /// Quality setting
  ///
  /// In en, this message translates to:
  /// **'Quality'**
  String get textCardQuality;

  /// Include watermark option
  ///
  /// In en, this message translates to:
  /// **'Include Watermark'**
  String get textCardIncludeWatermark;

  /// No description provided for @textCardPreviewInfo.
  ///
  /// In en, this message translates to:
  /// **'Preview Info'**
  String get textCardPreviewInfo;

  /// No description provided for @textCardRatio.
  ///
  /// In en, this message translates to:
  /// **'Ratio: {ratio}'**
  String textCardRatio(Object ratio);

  /// No description provided for @textCardQualityPercent.
  ///
  /// In en, this message translates to:
  /// **'Quality: {quality}%'**
  String textCardQualityPercent(Object quality);

  /// No description provided for @textCardWatermarkStatus.
  ///
  /// In en, this message translates to:
  /// **'Watermark: {status}'**
  String textCardWatermarkStatus(Object status);

  /// No description provided for @textCardDimensions.
  ///
  /// In en, this message translates to:
  /// **'Dimensions'**
  String get textCardDimensions;

  /// No description provided for @textCardInclude.
  ///
  /// In en, this message translates to:
  /// **'Include'**
  String get textCardInclude;

  /// No description provided for @textCardNotInclude.
  ///
  /// In en, this message translates to:
  /// **'Not Include'**
  String get textCardNotInclude;

  /// Button to add a new card
  ///
  /// In en, this message translates to:
  /// **'Add Card'**
  String get textCardAddCard;

  /// No description provided for @textCardAddNewCard.
  ///
  /// In en, this message translates to:
  /// **'Add New Card'**
  String get textCardAddNewCard;

  /// No description provided for @textCardEdit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get textCardEdit;

  /// Message shown during image export
  ///
  /// In en, this message translates to:
  /// **'Exporting image...'**
  String get textCardExportingImage;

  /// Success message for export
  ///
  /// In en, this message translates to:
  /// **'✅ Export successful! Saved to photo album'**
  String get textCardExportSuccess;

  /// Error message prefix for export failures
  ///
  /// In en, this message translates to:
  /// **'Export failed:'**
  String textCardExportFailed(Object error);

  /// Switch to add watermark
  ///
  /// In en, this message translates to:
  /// **'Add Watermark'**
  String get textCardAddWatermark;

  /// No description provided for @textCardAddWatermarkDesc.
  ///
  /// In en, this message translates to:
  /// **'Add app watermark to image corner'**
  String get textCardAddWatermarkDesc;

  /// Switch to include title
  ///
  /// In en, this message translates to:
  /// **'Include Title'**
  String get textCardIncludeTitle;

  /// No description provided for @textCardIncludeTitleDesc.
  ///
  /// In en, this message translates to:
  /// **'Show title in exported image'**
  String get textCardIncludeTitleDesc;

  /// Switch to include timestamp
  ///
  /// In en, this message translates to:
  /// **'Include Timestamp'**
  String get textCardIncludeTimestamp;

  /// No description provided for @textCardIncludeTimestampDesc.
  ///
  /// In en, this message translates to:
  /// **'Show creation time in image'**
  String get textCardIncludeTimestampDesc;

  /// No description provided for @textCardImageQuality.
  ///
  /// In en, this message translates to:
  /// **'Image Quality'**
  String get textCardImageQuality;

  /// Title for text style customization
  ///
  /// In en, this message translates to:
  /// **'Text Style Customization'**
  String textCardTextStyleCustomization(Object title);

  /// Button to export current card
  ///
  /// In en, this message translates to:
  /// **'Export Current Card'**
  String get textCardExportCurrentCard;

  /// Button to export multiple cards
  ///
  /// In en, this message translates to:
  /// **'Batch Export'**
  String get textCardBatchExport;

  /// Button to clear text selection
  ///
  /// In en, this message translates to:
  /// **'Clear Selection'**
  String get textCardClearSelection;

  /// Button to reset text style
  ///
  /// In en, this message translates to:
  /// **'Reset Style'**
  String get textCardResetStyle;

  /// Confirmation message for resetting styles
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to clear all text styles? This action cannot be undone.'**
  String get textCardResetStyleConfirm;

  /// No description provided for @textCardExportAsImage.
  ///
  /// In en, this message translates to:
  /// **'Export as Image'**
  String get textCardExportAsImage;

  /// No description provided for @textCardExportAsImageDesc.
  ///
  /// In en, this message translates to:
  /// **'Save this card as image'**
  String get textCardExportAsImageDesc;

  /// No description provided for @textCardEditCardDesc.
  ///
  /// In en, this message translates to:
  /// **'Modify title and content'**
  String get textCardEditCardDesc;

  /// No description provided for @textCardDeleteCardDesc.
  ///
  /// In en, this message translates to:
  /// **'Remove from document'**
  String get textCardDeleteCardDesc;

  /// No description provided for @textCardDeleteCardConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete card \"{title}\"? This action cannot be undone.'**
  String textCardDeleteCardConfirm(Object title);

  /// Edit document title
  ///
  /// In en, this message translates to:
  /// **'Edit Document'**
  String get textCardEditDocument;

  /// Uniform style button
  ///
  /// In en, this message translates to:
  /// **'Uniform Style'**
  String get textCardUniformStyle;

  /// No description provided for @textCardExportImage.
  ///
  /// In en, this message translates to:
  /// **'Export Image'**
  String get textCardExportImage;

  /// Insert separator button
  ///
  /// In en, this message translates to:
  /// **'Insert Separator'**
  String get textCardInsertSeparator;

  /// No description provided for @textCardPleaseEnterTitleAndCards.
  ///
  /// In en, this message translates to:
  /// **'Please enter title and ensure at least one card'**
  String get textCardPleaseEnterTitleAndCards;

  /// Save failed message
  ///
  /// In en, this message translates to:
  /// **'Save failed'**
  String textCardSaveFailed(Object error);

  /// No description provided for @textCardContentRendering.
  ///
  /// In en, this message translates to:
  /// **'Content Rendering'**
  String get textCardContentRendering;

  /// No description provided for @textCardExportWithTitle.
  ///
  /// In en, this message translates to:
  /// **'Export - {title}'**
  String textCardExportWithTitle(Object title);

  /// No description provided for @textCardShare.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get textCardShare;

  /// No description provided for @textCardSaveToAlbum.
  ///
  /// In en, this message translates to:
  /// **'Save to Album'**
  String get textCardSaveToAlbum;

  /// No description provided for @textCardUnderstood.
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get textCardUnderstood;

  /// No description provided for @textCardStartExperience.
  ///
  /// In en, this message translates to:
  /// **'Start Experience'**
  String get textCardStartExperience;

  /// No description provided for @textCardFeatureDemo.
  ///
  /// In en, this message translates to:
  /// **'Feature Demo'**
  String get textCardFeatureDemo;

  /// No description provided for @textCardGotIt.
  ///
  /// In en, this message translates to:
  /// **'Got it'**
  String get textCardGotIt;

  /// No description provided for @textCardStartUsing.
  ///
  /// In en, this message translates to:
  /// **'Start Using'**
  String get textCardStartUsing;

  /// No description provided for @textCardPreviewEffect.
  ///
  /// In en, this message translates to:
  /// **'Preview Effect'**
  String get textCardPreviewEffect;

  /// No description provided for @textCardExportInfo.
  ///
  /// In en, this message translates to:
  /// **'Export Info'**
  String get textCardExportInfo;

  /// No description provided for @textCardImageDimensions.
  ///
  /// In en, this message translates to:
  /// **'Image Dimensions'**
  String get textCardImageDimensions;

  /// No description provided for @textCardAspectRatio.
  ///
  /// In en, this message translates to:
  /// **'Aspect Ratio'**
  String get textCardAspectRatio;

  /// No description provided for @textCardFileSize.
  ///
  /// In en, this message translates to:
  /// **'File Size'**
  String get textCardFileSize;

  /// No description provided for @textCardUsageScenario.
  ///
  /// In en, this message translates to:
  /// **'Usage Scenario'**
  String get textCardUsageScenario;

  /// No description provided for @textCardBestQuality.
  ///
  /// In en, this message translates to:
  /// **'Best Quality (100%)'**
  String get textCardBestQuality;

  /// No description provided for @textCardWatermarkDescription.
  ///
  /// In en, this message translates to:
  /// **'Add app identifier at the bottom of the image'**
  String get textCardWatermarkDescription;

  /// PDF module name
  ///
  /// In en, this message translates to:
  /// **'PDF'**
  String get pdf;

  /// Voice module name
  ///
  /// In en, this message translates to:
  /// **'Voice'**
  String get voice;

  /// HTML module name
  ///
  /// In en, this message translates to:
  /// **'HTML'**
  String get html;

  /// SVG module name
  ///
  /// In en, this message translates to:
  /// **'SVG'**
  String get svg;

  /// Content field
  ///
  /// In en, this message translates to:
  /// **'Content'**
  String get content;

  /// Traffic Guide module name
  ///
  /// In en, this message translates to:
  /// **'Traffic Guide'**
  String get trafficGuide;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Confirm button
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get confirm;

  /// Yes button text
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No button text
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error message prefix
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// Success message prefix
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// Warning message prefix
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// Info message prefix
  ///
  /// In en, this message translates to:
  /// **'Info'**
  String get info;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Search input hint text
  ///
  /// In en, this message translates to:
  /// **'Enter search terms...'**
  String get searchHint;

  /// No search results message
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get noResults;

  /// Try again button text
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// Share button text
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// Export button text
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// Import button text
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get import;

  /// Copy button text
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get copy;

  /// Paste button text
  ///
  /// In en, this message translates to:
  /// **'Paste'**
  String get paste;

  /// Cut button text
  ///
  /// In en, this message translates to:
  /// **'Cut'**
  String get cut;

  /// Undo button text
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get undo;

  /// Redo button text
  ///
  /// In en, this message translates to:
  /// **'Redo'**
  String get redo;

  /// Select all button
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get selectAll;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// Previous button text
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// Done button text
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// Finish button text
  ///
  /// In en, this message translates to:
  /// **'Finish'**
  String get finish;

  /// Skip button text
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueAction;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Reset button text
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// Clear button text
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// Apply button text
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Preview button text
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get preview;

  /// Download button text
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// Upload button text
  ///
  /// In en, this message translates to:
  /// **'Upload'**
  String get upload;

  /// File label
  ///
  /// In en, this message translates to:
  /// **'File'**
  String get file;

  /// Folder label
  ///
  /// In en, this message translates to:
  /// **'Folder'**
  String get folder;

  /// Name field label
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Size field label
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get size;

  /// Date field label
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Time field label
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// Type field label
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// Status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Version field label
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// Author field label
  ///
  /// In en, this message translates to:
  /// **'Author'**
  String get author;

  /// Tags field label
  ///
  /// In en, this message translates to:
  /// **'Tags'**
  String get tags;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Priority field label
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// High priority
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// Medium priority
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// Low priority
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// Enabled status
  ///
  /// In en, this message translates to:
  /// **'Enabled'**
  String get enabled;

  /// Disabled status
  ///
  /// In en, this message translates to:
  /// **'Disabled'**
  String get disabled;

  /// Online status
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get online;

  /// Offline status
  ///
  /// In en, this message translates to:
  /// **'Offline'**
  String get offline;

  /// Connected status
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// Disconnected status
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get disconnected;

  /// Available status
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// Unavailable status
  ///
  /// In en, this message translates to:
  /// **'Unavailable'**
  String get unavailable;

  /// Status text for active subscription
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// Status text for inactive subscription
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// Public visibility
  ///
  /// In en, this message translates to:
  /// **'Public'**
  String get public;

  /// Private visibility
  ///
  /// In en, this message translates to:
  /// **'Private'**
  String get private;

  /// Draft status
  ///
  /// In en, this message translates to:
  /// **'Draft'**
  String get draft;

  /// Published status
  ///
  /// In en, this message translates to:
  /// **'Published'**
  String get published;

  /// Archived status
  ///
  /// In en, this message translates to:
  /// **'Archived'**
  String get archived;

  /// PDF module title
  ///
  /// In en, this message translates to:
  /// **'PDF Professional Tool'**
  String get pdfProfessionalTool;

  /// PDF module description
  ///
  /// In en, this message translates to:
  /// **'Powerful PDF processing capabilities that make document management easier'**
  String get pdfToolDescription;

  /// PDF security feature title
  ///
  /// In en, this message translates to:
  /// **'Security Encryption'**
  String get securityEncryption;

  /// PDF security feature description
  ///
  /// In en, this message translates to:
  /// **'Password Protection\nPermission Control'**
  String get passwordProtectionPermissionControl;

  /// PDF annotation feature title
  ///
  /// In en, this message translates to:
  /// **'Intelligent Annotation'**
  String get intelligentAnnotation;

  /// PDF annotation feature description
  ///
  /// In en, this message translates to:
  /// **'Highlight Marking\nText Annotation'**
  String get highlightMarkingTextAnnotation;

  /// PDF search feature title
  ///
  /// In en, this message translates to:
  /// **'Quick Search'**
  String get quickSearch;

  /// PDF search feature description
  ///
  /// In en, this message translates to:
  /// **'Full-text Search\nContent Location'**
  String get fullTextSearchContentLocation;

  /// PDF sharing feature title
  ///
  /// In en, this message translates to:
  /// **'Convenient Sharing'**
  String get convenientSharing;

  /// PDF sharing feature description
  ///
  /// In en, this message translates to:
  /// **'Multiple Formats\nOne-click Export'**
  String get multipleFormatsOneClickExport;

  /// Welcome message for PDF tool
  ///
  /// In en, this message translates to:
  /// **'Welcome to PDF Professional Tool!'**
  String get welcomeToPdfTool;

  /// Import PDF button text
  ///
  /// In en, this message translates to:
  /// **'Import First PDF Document'**
  String get importFirstPdfDocument;

  /// Appearance settings section title
  ///
  /// In en, this message translates to:
  /// **'Appearance'**
  String get appearance;

  /// Follow system theme option
  ///
  /// In en, this message translates to:
  /// **'Follow System'**
  String get followSystem;

  /// Language change notification message
  ///
  /// In en, this message translates to:
  /// **'Language changes will take effect immediately'**
  String get languageChangeEffect;

  /// Content library title
  ///
  /// In en, this message translates to:
  /// **'Content Library'**
  String get contentLibrary;

  /// Content library subtitle
  ///
  /// In en, this message translates to:
  /// **'Manage All Cards'**
  String get manageAllCards;

  /// Template library title
  ///
  /// In en, this message translates to:
  /// **'Template Library'**
  String get templateLibrary;

  /// Template library subtitle
  ///
  /// In en, this message translates to:
  /// **'Browse Beautiful Templates'**
  String get browseBeautifulTemplates;

  /// Input text to split title
  ///
  /// In en, this message translates to:
  /// **'Input Text to Split'**
  String get inputTextToSplit;

  /// Paste or input long text description
  ///
  /// In en, this message translates to:
  /// **'Paste or input long text content'**
  String get pasteOrInputLongText;

  /// Paste clipboard button
  ///
  /// In en, this message translates to:
  /// **'Paste Clipboard'**
  String get pasteClipboard;

  /// Clear content button
  ///
  /// In en, this message translates to:
  /// **'Clear Content'**
  String get clearContent;

  /// Default card title with number
  ///
  /// In en, this message translates to:
  /// **'Card {number}'**
  String cardNumber(int number);

  /// Loading demo data message
  ///
  /// In en, this message translates to:
  /// **'Loading demo data...'**
  String get loadingDemoData;

  /// Feature description for content library
  ///
  /// In en, this message translates to:
  /// **'✨ Modern UI Design\n🖼️ Render Result Preview\n📱 Block Mode Support\n⚡ High Performance Experience'**
  String get modernUIDesign;

  /// Edit function message
  ///
  /// In en, this message translates to:
  /// **'Edit function: {title}'**
  String editFunction(String title);

  /// Deleted item message
  ///
  /// In en, this message translates to:
  /// **'Deleted: {title}'**
  String deleted(String title);

  /// Share function message
  ///
  /// In en, this message translates to:
  /// **'Share function: {title}'**
  String shareFunction(String title);

  /// Create new content title
  ///
  /// In en, this message translates to:
  /// **'Create New Content'**
  String get createNewContent;

  /// Create content type selection description
  ///
  /// In en, this message translates to:
  /// **'Select the type of content you want to create'**
  String get selectContentType;

  /// Bold markdown syntax
  ///
  /// In en, this message translates to:
  /// **'**Bold**'**
  String get bold;

  /// Italic markdown syntax
  ///
  /// In en, this message translates to:
  /// **'*Italic*'**
  String get italic;

  /// Heading 1 markdown syntax
  ///
  /// In en, this message translates to:
  /// **'# Heading 1'**
  String get heading1;

  /// Heading 2 markdown syntax
  ///
  /// In en, this message translates to:
  /// **'## Heading 2'**
  String get heading2;

  /// Heading 3 markdown syntax
  ///
  /// In en, this message translates to:
  /// **'### Heading 3'**
  String get heading3;

  /// List markdown syntax
  ///
  /// In en, this message translates to:
  /// **'- List item\n- List item'**
  String get list;

  /// Link markdown syntax
  ///
  /// In en, this message translates to:
  /// **'[Link text](URL)'**
  String get link;

  /// Image markdown syntax
  ///
  /// In en, this message translates to:
  /// **'![Image description](Image URL)'**
  String get image;

  /// Inline code markdown syntax
  ///
  /// In en, this message translates to:
  /// **'`Code`'**
  String get code;

  /// Code block markdown syntax
  ///
  /// In en, this message translates to:
  /// **'```\nCode block\n```'**
  String get codeBlock;

  /// Quote markdown syntax
  ///
  /// In en, this message translates to:
  /// **'> Quote text'**
  String get quote;

  /// Table markdown syntax
  ///
  /// In en, this message translates to:
  /// **'| Column 1 | Column 2 |\n| --- | --- |\n| Content 1 | Content 2 |'**
  String get table;

  /// My content library title
  ///
  /// In en, this message translates to:
  /// **'My Content Library'**
  String get myContentLibrary;

  /// Content library description
  ///
  /// In en, this message translates to:
  /// **'Manage and browse all your content'**
  String get manageAndBrowseContent;

  /// Content tools section title
  ///
  /// In en, this message translates to:
  /// **'Content Tools'**
  String get contentTools;

  /// Recommended tools section title
  ///
  /// In en, this message translates to:
  /// **'Recommended Tools'**
  String get recommendedTools;

  /// Markdown tool title
  ///
  /// In en, this message translates to:
  /// **'Markdown'**
  String get markdownTitle;

  /// Markdown tool description
  ///
  /// In en, this message translates to:
  /// **'Document editing and rendering'**
  String get markdownDescription;

  /// Text cards tool title
  ///
  /// In en, this message translates to:
  /// **'Text Cards'**
  String get textCardsTitle;

  /// Text cards tool description
  ///
  /// In en, this message translates to:
  /// **'Knowledge card customization and rendering'**
  String get textCardsDescription;

  /// Traffic guide module title
  ///
  /// In en, this message translates to:
  /// **'Traffic Guide'**
  String get trafficGuideTitle;

  /// Traffic guide tool description
  ///
  /// In en, this message translates to:
  /// **'Traffic image and text processing'**
  String get trafficGuideDescription;

  /// File tools section title
  ///
  /// In en, this message translates to:
  /// **'File Tools'**
  String get fileTools;

  /// SVG tool title
  ///
  /// In en, this message translates to:
  /// **'SVG'**
  String get svgTitle;

  /// SVG tool description
  ///
  /// In en, this message translates to:
  /// **'Vector graphics processing'**
  String get svgDescription;

  /// HTML tool title
  ///
  /// In en, this message translates to:
  /// **'HTML'**
  String get htmlTitle;

  /// HTML tool description
  ///
  /// In en, this message translates to:
  /// **'Web content editing'**
  String get htmlDescription;

  /// Loading content message
  ///
  /// In en, this message translates to:
  /// **'Loading content...'**
  String get loadingContent;

  /// Language change success message
  ///
  /// In en, this message translates to:
  /// **'Language changed to {language}'**
  String languageChangedTo(String language);

  /// Developer settings section title
  ///
  /// In en, this message translates to:
  /// **'Developer'**
  String get developer;

  /// Content library demo title
  ///
  /// In en, this message translates to:
  /// **'Content Library Demo'**
  String get contentLibraryDemo;

  /// Content library demo description
  ///
  /// In en, this message translates to:
  /// **'View new content library features'**
  String get viewNewContentLibraryFeatures;

  /// I18n demo title
  ///
  /// In en, this message translates to:
  /// **'Internationalization Demo'**
  String get i18nDemo;

  /// I18n demo description
  ///
  /// In en, this message translates to:
  /// **'View multi-language support effects'**
  String get viewMultiLanguageSupport;

  /// About settings section title
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// Version info title
  ///
  /// In en, this message translates to:
  /// **'Version Info'**
  String get versionInfo;

  /// Help and feedback title
  ///
  /// In en, this message translates to:
  /// **'Help & Feedback'**
  String get helpAndFeedback;

  /// Help and feedback description
  ///
  /// In en, this message translates to:
  /// **'Get help or provide feedback'**
  String get getHelpOrProvideFeedback;

  /// Help and feedback dialog content
  ///
  /// In en, this message translates to:
  /// **'If you have any questions or suggestions, please contact us through the following methods:\n\nEmail: <EMAIL>'**
  String get helpAndFeedbackContent;

  /// Select theme dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Theme'**
  String get selectTheme;

  /// Light theme mode
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// Dark theme mode
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// System theme mode
  ///
  /// In en, this message translates to:
  /// **'Follow System'**
  String get systemMode;

  /// Settings page subtitle
  ///
  /// In en, this message translates to:
  /// **'Personalize your app experience'**
  String get personalizeYourAppExperience;

  /// Setting to use default initial text
  ///
  /// In en, this message translates to:
  /// **'Use Default Initial Text'**
  String get useDefaultInitialText;

  /// Description for default initial text setting
  ///
  /// In en, this message translates to:
  /// **'Auto-fill default example content when entering modules'**
  String get useDefaultInitialTextDescription;

  /// Content settings section title
  ///
  /// In en, this message translates to:
  /// **'Content Settings'**
  String get contentSettings;

  /// Image generator tool title
  ///
  /// In en, this message translates to:
  /// **'Image Generator'**
  String get trafficGuideImageGenerator;

  /// Subtitle for image generator
  ///
  /// In en, this message translates to:
  /// **'Generate images for traffic across platforms'**
  String get trafficGuideImageGeneratorSubtitle;

  /// Text tab label
  ///
  /// In en, this message translates to:
  /// **'Text'**
  String get trafficGuideTabText;

  /// Template tab label
  ///
  /// In en, this message translates to:
  /// **'Template'**
  String get trafficGuideTabTemplate;

  /// Effects tab label
  ///
  /// In en, this message translates to:
  /// **'Effects'**
  String get trafficGuideTabEffects;

  /// Text content field label
  ///
  /// In en, this message translates to:
  /// **'Text Content'**
  String get trafficGuideTextContent;

  /// Text content field hint
  ///
  /// In en, this message translates to:
  /// **'Enter text to display...'**
  String get trafficGuideTextHint;

  /// Font settings section title
  ///
  /// In en, this message translates to:
  /// **'Font Settings'**
  String get trafficGuideFontSettings;

  /// Font size setting
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get trafficGuideFontSize;

  /// Color settings section title
  ///
  /// In en, this message translates to:
  /// **'Color Settings'**
  String get trafficGuideColorSettings;

  /// Text color field label
  ///
  /// In en, this message translates to:
  /// **'Text Color'**
  String get trafficGuideTextColor;

  /// Background color field label
  ///
  /// In en, this message translates to:
  /// **'Background Color'**
  String get trafficGuideBackgroundColor;

  /// Visual effects section title
  ///
  /// In en, this message translates to:
  /// **'Visual Effects'**
  String get trafficGuideVisualEffects;

  /// Noise level slider label
  ///
  /// In en, this message translates to:
  /// **'Noise Level'**
  String get trafficGuideNoiseLevel;

  /// Distortion level slider label
  ///
  /// In en, this message translates to:
  /// **'Distortion Level'**
  String get trafficGuideDistortionLevel;

  /// Add watermark checkbox label
  ///
  /// In en, this message translates to:
  /// **'Add Watermark'**
  String get trafficGuideAddWatermark;

  /// Watermark text field label
  ///
  /// In en, this message translates to:
  /// **'Watermark Text'**
  String get trafficGuideWatermarkText;

  /// Watermark hint text
  ///
  /// In en, this message translates to:
  /// **'Enter watermark text...'**
  String get trafficGuideWatermarkHint;

  /// Export button text
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get trafficGuideExport;

  /// Template selection error
  ///
  /// In en, this message translates to:
  /// **'Please select a template first'**
  String get trafficGuideSelectTemplateFirst;

  /// Image save success message
  ///
  /// In en, this message translates to:
  /// **'Image saved successfully'**
  String get trafficGuideImageSavedSuccess;

  /// Save error message
  ///
  /// In en, this message translates to:
  /// **'Save failed: {error}'**
  String trafficGuideSaveFailed(Object error);

  /// Permission permanently denied error
  ///
  /// In en, this message translates to:
  /// **'Permission permanently denied'**
  String get trafficGuidePermissionPermanentlyDenied;

  /// Permission required error
  ///
  /// In en, this message translates to:
  /// **'Permission required'**
  String get trafficGuidePermissionRequired;

  /// Save failed with message error
  ///
  /// In en, this message translates to:
  /// **'Save failed: {message}'**
  String trafficGuideSaveFailedWithMessage(Object message);

  /// Save failed empty result error
  ///
  /// In en, this message translates to:
  /// **'Save failed: empty result'**
  String get trafficGuideSaveFailedEmptyResult;

  /// Preview tab label
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get markdownPreview;

  /// Markdown content label
  ///
  /// In en, this message translates to:
  /// **'Markdown Content'**
  String get markdownContentLabel;

  /// Render mode label
  ///
  /// In en, this message translates to:
  /// **'Render Mode:'**
  String get markdownRenderModeLabel;

  /// Normal render mode
  ///
  /// In en, this message translates to:
  /// **'Normal Mode'**
  String get markdownNormalMode;

  /// Block render mode
  ///
  /// In en, this message translates to:
  /// **'Block Mode'**
  String get markdownBlockMode;

  /// Configuration tab label
  ///
  /// In en, this message translates to:
  /// **'Config'**
  String get markdownConfigTab;

  /// Management tab label
  ///
  /// In en, this message translates to:
  /// **'Manage'**
  String get markdownManageTab;

  /// Preview tab label
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get markdownPreviewTab;

  /// Block information section title
  ///
  /// In en, this message translates to:
  /// **'Block Information'**
  String get markdownBlockInfo;

  /// Total blocks count label
  ///
  /// In en, this message translates to:
  /// **'Total Blocks'**
  String get markdownTotalBlocks;

  /// Visible blocks count label
  ///
  /// In en, this message translates to:
  /// **'Visible Blocks'**
  String get markdownVisibleBlocks;

  /// Markdown editor title
  ///
  /// In en, this message translates to:
  /// **'Markdown Editor'**
  String get markdownEditorTitle;

  /// Markdown preview title
  ///
  /// In en, this message translates to:
  /// **'Markdown Preview'**
  String get markdownPreviewTitle;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get markdownTitleLabel;

  /// Subtitle field label
  ///
  /// In en, this message translates to:
  /// **'Subtitle (Optional)'**
  String get markdownSubtitleLabel;

  /// Default document title
  ///
  /// In en, this message translates to:
  /// **'Untitled Document'**
  String get markdownUntitledDocument;

  /// Default section title
  ///
  /// In en, this message translates to:
  /// **'Untitled Section'**
  String get markdownUntitledSection;

  /// Split sections button text
  ///
  /// In en, this message translates to:
  /// **'Split Sections'**
  String get markdownSplitSections;

  /// Save document button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save Document'**
  String get markdownSaveDocument;

  /// Action options title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Action Options'**
  String get markdownActionOptions;

  /// Share image button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Share Image'**
  String get markdownShareImage;

  /// Copy content option
  ///
  /// In en, this message translates to:
  /// **'Copy Content'**
  String get markdownCopyContent;

  /// Save to album button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save to Album'**
  String get markdownSaveToAlbum;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get commonCancel;

  /// Reset button text
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get commonReset;

  /// Select all button text
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get commonSelectAll;

  /// Deselect all button text
  ///
  /// In en, this message translates to:
  /// **'Deselect All'**
  String get commonDeselectAll;

  /// Show selected blocks button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Show Selected'**
  String get markdownShowSelected;

  /// Hide selected blocks button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Hide Selected'**
  String get markdownHideSelected;

  /// Export selected blocks button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export Selected'**
  String get markdownExportSelected;

  /// Hide block action text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Hide Block'**
  String get markdownHideBlock;

  /// Show block action text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Show Block'**
  String get markdownShowBlock;

  /// Export as image option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export as Image'**
  String get markdownExportAsImage;

  /// Export as markdown option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export as Markdown'**
  String get markdownExportAsMarkdown;

  /// Got it button text
  ///
  /// In en, this message translates to:
  /// **'Got it'**
  String get commonGotIt;

  /// Block render settings title
  ///
  /// In en, this message translates to:
  /// **'Block Render Settings'**
  String get markdownBlockRenderSettings;

  /// Basic settings section title
  ///
  /// In en, this message translates to:
  /// **'Basic Settings'**
  String get markdownBasicSettings;

  /// Enable block rendering option
  ///
  /// In en, this message translates to:
  /// **'Enable Block Rendering'**
  String get markdownEnableBlockRender;

  /// Separator settings section title
  ///
  /// In en, this message translates to:
  /// **'Separator Settings'**
  String get markdownSeparatorSettings;

  /// Split by H1 headers option
  ///
  /// In en, this message translates to:
  /// **'Split by H1 Headers'**
  String get markdownSplitByH1;

  /// Split by H2 headers option
  ///
  /// In en, this message translates to:
  /// **'Split by H2 Headers'**
  String get markdownSplitByH2;

  /// Custom separator pattern label
  ///
  /// In en, this message translates to:
  /// **'Custom Separator Pattern (Regex)'**
  String get markdownCustomSeparatorPattern;

  /// Appearance settings section title
  ///
  /// In en, this message translates to:
  /// **'Appearance Settings'**
  String get markdownAppearanceSettings;

  /// Block spacing setting
  ///
  /// In en, this message translates to:
  /// **'Block Spacing'**
  String get markdownBlockSpacing;

  /// Section split settings title
  ///
  /// In en, this message translates to:
  /// **'Section Split Settings'**
  String get markdownSectionSplitSettings;

  /// Split by horizontal rule option
  ///
  /// In en, this message translates to:
  /// **'Split by Horizontal Rule'**
  String get markdownSplitByHorizontalRule;

  /// Maximum section length setting
  ///
  /// In en, this message translates to:
  /// **'Maximum Section Length'**
  String get markdownMaxSectionLength;

  /// Unlimited option
  ///
  /// In en, this message translates to:
  /// **'Unlimited'**
  String get commonUnlimited;

  /// Set maximum section length dialog title
  ///
  /// In en, this message translates to:
  /// **'Set Maximum Section Length'**
  String get markdownSetMaxSectionLength;

  /// Maximum characters field label
  ///
  /// In en, this message translates to:
  /// **'Maximum Characters'**
  String get markdownMaxCharacters;

  /// Helper text for unlimited characters
  ///
  /// In en, this message translates to:
  /// **'Leave empty for unlimited'**
  String get markdownLeaveEmptyUnlimited;

  /// Simple template name
  ///
  /// In en, this message translates to:
  /// **'Simple'**
  String get templateSimpleName;

  /// Simple template description
  ///
  /// In en, this message translates to:
  /// **'Clean and minimalist design style'**
  String get templateSimpleDescription;

  /// Modern template name
  ///
  /// In en, this message translates to:
  /// **'Modern'**
  String get templateModernName;

  /// Modern template description
  ///
  /// In en, this message translates to:
  /// **'Modern design style with shadow effects'**
  String get templateModernDescription;

  /// Elegant template name
  ///
  /// In en, this message translates to:
  /// **'Elegant'**
  String get templateElegantName;

  /// Elegant template description
  ///
  /// In en, this message translates to:
  /// **'Elegant design style with thin borders'**
  String get templateElegantDescription;

  /// Code template name
  ///
  /// In en, this message translates to:
  /// **'Code'**
  String get templateCodeName;

  /// Code template description
  ///
  /// In en, this message translates to:
  /// **'Dark theme suitable for code display'**
  String get templateCodeDescription;

  /// Card template name
  ///
  /// In en, this message translates to:
  /// **'Card'**
  String get templateCardName;

  /// Card template description
  ///
  /// In en, this message translates to:
  /// **'Social media card style design'**
  String get templateCardDescription;

  /// Morandi template name
  ///
  /// In en, this message translates to:
  /// **'Morandi'**
  String get templateMorandiName;

  /// Morandi template description
  ///
  /// In en, this message translates to:
  /// **'Premium Morandi color palette, soft and elegant'**
  String get templateMorandiDescription;

  /// Chinese blue template name
  ///
  /// In en, this message translates to:
  /// **'Chinese Blue'**
  String get templateChineseBlueName;

  /// Chinese blue template description
  ///
  /// In en, this message translates to:
  /// **'Traditional Chinese porcelain color and pattern design'**
  String get templateChineseBlueDescription;

  /// Chinese vermilion template name
  ///
  /// In en, this message translates to:
  /// **'Chinese Vermilion'**
  String get templateChineseVermilionName;

  /// Chinese vermilion template description
  ///
  /// In en, this message translates to:
  /// **'Traditional Chinese vermilion color, elegant and solemn'**
  String get templateChineseVermilionDescription;

  /// Gradient purple template name
  ///
  /// In en, this message translates to:
  /// **'Gradient Purple'**
  String get templateGradientPurpleName;

  /// Gradient purple template description
  ///
  /// In en, this message translates to:
  /// **'Modern purple-blue gradient background, stylish and elegant'**
  String get templateGradientPurpleDescription;

  /// Festive red template name
  ///
  /// In en, this message translates to:
  /// **'Festive Red'**
  String get templateFestiveRedName;

  /// Festive red template description
  ///
  /// In en, this message translates to:
  /// **'Festive theme, suitable for occasions like Spring Festival'**
  String get templateFestiveRedDescription;

  /// Bamboo slip template name
  ///
  /// In en, this message translates to:
  /// **'Bamboo Slip'**
  String get templateBambooSlipName;

  /// Bamboo slip template description
  ///
  /// In en, this message translates to:
  /// **'Traditional bamboo slip style, rich in ancient charm'**
  String get templateBambooSlipDescription;

  /// Watermark position top left
  ///
  /// In en, this message translates to:
  /// **'Top Left'**
  String get watermarkPositionTopLeft;

  /// Watermark position top center
  ///
  /// In en, this message translates to:
  /// **'Top Center'**
  String get watermarkPositionTopCenter;

  /// Watermark position top right
  ///
  /// In en, this message translates to:
  /// **'Top Right'**
  String get watermarkPositionTopRight;

  /// Watermark position bottom left
  ///
  /// In en, this message translates to:
  /// **'Bottom Left'**
  String get watermarkPositionBottomLeft;

  /// Watermark position bottom center
  ///
  /// In en, this message translates to:
  /// **'Bottom Center'**
  String get watermarkPositionBottomCenter;

  /// Watermark position bottom right
  ///
  /// In en, this message translates to:
  /// **'Bottom Right'**
  String get watermarkPositionBottomRight;

  /// Watermark position tiled
  ///
  /// In en, this message translates to:
  /// **'Tiled'**
  String get watermarkPositionTiled;

  /// Error message for empty content
  ///
  /// In en, this message translates to:
  /// **'Please enter Markdown content first'**
  String get markdownEnterContentFirst;

  /// Success message for splitting sections
  ///
  /// In en, this message translates to:
  /// **'Successfully split into {count} sections'**
  String markdownSplitSuccess(int count);

  /// Error message for splitting sections
  ///
  /// In en, this message translates to:
  /// **'Error splitting sections: {error}'**
  String markdownSplitError(String error);

  /// Error message for no sections
  ///
  /// In en, this message translates to:
  /// **'No sections to preview'**
  String get markdownNoSectionsToPreview;

  /// Error message for unsplitted content
  ///
  /// In en, this message translates to:
  /// **'Please split Markdown content first'**
  String get markdownSplitContentFirst;

  /// Success message for saving document
  ///
  /// In en, this message translates to:
  /// **'Document saved successfully'**
  String get markdownDocumentSaveSuccess;

  /// Error message for saving document
  ///
  /// In en, this message translates to:
  /// **'Failed to save document: {error}'**
  String markdownDocumentSaveError(String error);

  /// Storage permission error message
  ///
  /// In en, this message translates to:
  /// **'Storage permission required to save images'**
  String get errorStoragePermissionRequired;

  /// No content to preview message
  ///
  /// In en, this message translates to:
  /// **'No content to preview'**
  String get markdownNoContentToPreview;

  /// Image generation failed message
  ///
  /// In en, this message translates to:
  /// **'Image generation failed, unable to share'**
  String get markdownImageGenerationFailed;

  /// Share error message
  ///
  /// In en, this message translates to:
  /// **'Share failed: {error}'**
  String markdownShareError(String error);

  /// Error message for disabled block mode
  ///
  /// In en, this message translates to:
  /// **'Please enable block mode first'**
  String get markdownEnableBlockModeFirst;

  /// No blocks message
  ///
  /// In en, this message translates to:
  /// **'No blocks available'**
  String get markdownNoBlocks;

  /// Helper text for enabling block rendering
  ///
  /// In en, this message translates to:
  /// **'Enable block rendering to view block list'**
  String get markdownEnableBlockRenderToList;

  /// Content copied message
  ///
  /// In en, this message translates to:
  /// **'Content copied to clipboard'**
  String get commonContentCopied;

  /// Reset demo button text
  ///
  /// In en, this message translates to:
  /// **'Reset Demo'**
  String get markdownResetDemo;

  /// Help button text
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get commonHelp;

  /// Block render help title
  ///
  /// In en, this message translates to:
  /// **'Block Render Help'**
  String get markdownBlockRenderHelp;

  /// Feature description label
  ///
  /// In en, this message translates to:
  /// **'Feature Description:'**
  String get markdownFeatureDescription;

  /// Operation method label
  ///
  /// In en, this message translates to:
  /// **'Operation Method:'**
  String get markdownOperationMethod;

  /// Tips label
  ///
  /// In en, this message translates to:
  /// **'Tips:'**
  String get commonTips;

  /// Section settings tooltip
  ///
  /// In en, this message translates to:
  /// **'Section Settings'**
  String get markdownSectionSettings;

  /// Select template tooltip
  ///
  /// In en, this message translates to:
  /// **'Select Template'**
  String get markdownSelectTemplate;

  /// Select HTML template title
  ///
  /// In en, this message translates to:
  /// **'Select HTML Template'**
  String get markdownSelectHtmlTemplate;

  /// Preview button text
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get commonPreview;

  /// Save image button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save Image'**
  String get markdownSaveImage;

  /// Share button text
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get commonShare;

  /// Show all filter option
  ///
  /// In en, this message translates to:
  /// **'Show All'**
  String get commonShowAll;

  /// Show visible only filter option
  ///
  /// In en, this message translates to:
  /// **'Show Visible Only'**
  String get commonShowVisibleOnly;

  /// Sort by index option
  ///
  /// In en, this message translates to:
  /// **'Sort by Index'**
  String get commonSortByIndex;

  /// Sort by title option
  ///
  /// In en, this message translates to:
  /// **'Sort by Title'**
  String get commonSortByTitle;

  /// Sort by type option
  ///
  /// In en, this message translates to:
  /// **'Sort by Type'**
  String get commonSortByType;

  /// Sort by length option
  ///
  /// In en, this message translates to:
  /// **'Sort by Length'**
  String get commonSortByLength;

  /// Block count text
  ///
  /// In en, this message translates to:
  /// **'{count} blocks'**
  String markdownBlockCount(int count);

  /// Character count text
  ///
  /// In en, this message translates to:
  /// **'{count} characters'**
  String commonCharacterCount(int count);

  /// Selected block count text
  ///
  /// In en, this message translates to:
  /// **'{count} blocks selected'**
  String markdownSelectedBlockCount(int count);

  /// Total label
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get commonTotal;

  /// Visible label
  ///
  /// In en, this message translates to:
  /// **'Visible'**
  String get commonVisible;

  /// Hidden label
  ///
  /// In en, this message translates to:
  /// **'Hidden'**
  String get commonHidden;

  /// Markdown content placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter Markdown content here...'**
  String get markdownContentPlaceholder;

  /// Helper text for splitting content
  ///
  /// In en, this message translates to:
  /// **'Click split button to split Markdown into sections'**
  String get markdownClickSplitButton;

  /// Horizontal rule helper text
  ///
  /// In en, this message translates to:
  /// **'Split when encountering three or more -, *, or _ symbols'**
  String get markdownHorizontalRuleHelper;

  /// H1 split helper text
  ///
  /// In en, this message translates to:
  /// **'Split when encountering # H1 headers'**
  String get markdownH1SplitHelper;

  /// Character count text
  ///
  /// In en, this message translates to:
  /// **'{count} characters'**
  String markdownCharacterCount(int count);

  /// Auto split helper text
  ///
  /// In en, this message translates to:
  /// **'Automatically split long sections into multiple sections'**
  String get markdownAutoSplitHelper;

  /// Separator example text
  ///
  /// In en, this message translates to:
  /// **'Example: Three or more consecutive hyphens'**
  String get markdownSeparatorExample;

  /// H1 separator helper text
  ///
  /// In en, this message translates to:
  /// **'Use # H1 headers as block separators'**
  String get markdownH1SeparatorHelper;

  /// H2 separator helper text
  ///
  /// In en, this message translates to:
  /// **'Use ## H2 headers as block separators'**
  String get markdownH2SeparatorHelper;

  /// Block render helper text
  ///
  /// In en, this message translates to:
  /// **'When enabled, Markdown content will be displayed in blocks according to set rules'**
  String get markdownBlockRenderHelper;

  /// Export blocks option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export Blocks'**
  String get markdownExportBlocks;

  /// Generate summary report option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Generate Summary Report'**
  String get markdownGenerateSummary;

  /// Image export success message
  ///
  /// In en, this message translates to:
  /// **'Image exported: {filePath}'**
  String markdownImageExportSuccess(String filePath);

  /// Export error message
  ///
  /// In en, this message translates to:
  /// **'Export failed: {error}'**
  String markdownExportError(String error);

  /// Markdown export success message
  ///
  /// In en, this message translates to:
  /// **'Markdown file exported: {filePath}'**
  String markdownMarkdownExportSuccess(String filePath);

  /// Summary generated message
  ///
  /// In en, this message translates to:
  /// **'Summary report generated: {filePath}'**
  String markdownSummaryGenerated(String filePath);

  /// Summary generation error message
  ///
  /// In en, this message translates to:
  /// **'Report generation failed: {error}'**
  String markdownSummaryError(String error);

  /// Generating image progress message
  ///
  /// In en, this message translates to:
  /// **'Generating image ({current}/{total})'**
  String markdownGeneratingImage(int current, int total);

  /// Images saved success message
  ///
  /// In en, this message translates to:
  /// **'Successfully saved {count} images to album'**
  String markdownImagesSavedSuccess(int count);

  /// Chinese blue template watermark
  ///
  /// In en, this message translates to:
  /// **'青花'**
  String get templateChineseBlueWatermark;

  /// Chinese vermilion template watermark
  ///
  /// In en, this message translates to:
  /// **'赤'**
  String get templateChineseVermilionWatermark;

  /// Festive red template watermark
  ///
  /// In en, this message translates to:
  /// **'福'**
  String get templateFestiveRedWatermark;

  /// Bamboo slip template watermark
  ///
  /// In en, this message translates to:
  /// **'竹'**
  String get templateBambooSlipWatermark;

  /// Block management title
  ///
  /// In en, this message translates to:
  /// **'Block Management'**
  String get markdownBlockManagement;

  /// Export options title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export Options'**
  String get markdownExportOptions;

  /// Deselect button text
  ///
  /// In en, this message translates to:
  /// **'Deselect'**
  String get commonDeselect;

  /// Select button text
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get commonSelect;

  /// Loading text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get commonLoading;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get commonConfirm;

  /// Edit button text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get commonEdit;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get commonDelete;

  /// Add button text
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get commonAdd;

  /// Remove button text
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get commonRemove;

  /// Apply button text
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get commonApply;

  /// Close button text
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get commonClose;

  /// Open button text
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get commonOpen;

  /// View button text
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get commonView;

  /// Browse button text
  ///
  /// In en, this message translates to:
  /// **'Browse'**
  String get commonBrowse;

  /// Search button text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get commonSearch;

  /// Filter button text
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get commonFilter;

  /// Sort button text
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get commonSort;

  /// Refresh button text
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get commonRefresh;

  /// Reload button text
  ///
  /// In en, this message translates to:
  /// **'Reload'**
  String get commonReload;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get commonRetry;

  /// Continue button text
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get commonContinue;

  /// Finish button text
  ///
  /// In en, this message translates to:
  /// **'Finish'**
  String get commonFinish;

  /// Skip button text
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get commonSkip;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get commonBack;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get commonNext;

  /// Previous button text
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get commonPrevious;

  /// Done button text
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get commonDone;

  /// Start button text
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get commonStart;

  /// Stop button text
  ///
  /// In en, this message translates to:
  /// **'Stop'**
  String get commonStop;

  /// Pause button text
  ///
  /// In en, this message translates to:
  /// **'Pause'**
  String get commonPause;

  /// Resume button text
  ///
  /// In en, this message translates to:
  /// **'Resume'**
  String get commonResume;

  /// Play button text
  ///
  /// In en, this message translates to:
  /// **'Play'**
  String get commonPlay;

  /// Mute button text
  ///
  /// In en, this message translates to:
  /// **'Mute'**
  String get commonMute;

  /// Unmute button text
  ///
  /// In en, this message translates to:
  /// **'Unmute'**
  String get commonUnmute;

  /// Volume up button text
  ///
  /// In en, this message translates to:
  /// **'Volume Up'**
  String get commonVolumeUp;

  /// Volume down button text
  ///
  /// In en, this message translates to:
  /// **'Volume Down'**
  String get commonVolumeDown;

  /// Fullscreen button text
  ///
  /// In en, this message translates to:
  /// **'Fullscreen'**
  String get commonFullscreen;

  /// Exit fullscreen button text
  ///
  /// In en, this message translates to:
  /// **'Exit Fullscreen'**
  String get commonExitFullscreen;

  /// Zoom in button text
  ///
  /// In en, this message translates to:
  /// **'Zoom In'**
  String get commonZoomIn;

  /// Zoom out button text
  ///
  /// In en, this message translates to:
  /// **'Zoom Out'**
  String get commonZoomOut;

  /// Zoom reset button text
  ///
  /// In en, this message translates to:
  /// **'Zoom Reset'**
  String get commonZoomReset;

  /// Rotate left button text
  ///
  /// In en, this message translates to:
  /// **'Rotate Left'**
  String get commonRotateLeft;

  /// Rotate right button text
  ///
  /// In en, this message translates to:
  /// **'Rotate Right'**
  String get commonRotateRight;

  /// Flip horizontal button text
  ///
  /// In en, this message translates to:
  /// **'Flip Horizontal'**
  String get commonFlipHorizontal;

  /// Flip vertical button text
  ///
  /// In en, this message translates to:
  /// **'Flip Vertical'**
  String get commonFlipVertical;

  /// Crop button text
  ///
  /// In en, this message translates to:
  /// **'Crop'**
  String get commonCrop;

  /// Resize button text
  ///
  /// In en, this message translates to:
  /// **'Resize'**
  String get commonResize;

  /// Rotate button text
  ///
  /// In en, this message translates to:
  /// **'Rotate'**
  String get commonRotate;

  /// Flip button text
  ///
  /// In en, this message translates to:
  /// **'Flip'**
  String get commonFlip;

  /// Mirror button text
  ///
  /// In en, this message translates to:
  /// **'Mirror'**
  String get commonMirror;

  /// Skew button text
  ///
  /// In en, this message translates to:
  /// **'Skew'**
  String get commonSkew;

  /// Distort button text
  ///
  /// In en, this message translates to:
  /// **'Distort'**
  String get commonDistort;

  /// Blur button text
  ///
  /// In en, this message translates to:
  /// **'Blur'**
  String get commonBlur;

  /// Sharpen button text
  ///
  /// In en, this message translates to:
  /// **'Sharpen'**
  String get commonSharpen;

  /// Brightness button text
  ///
  /// In en, this message translates to:
  /// **'Brightness'**
  String get commonBrightness;

  /// Contrast button text
  ///
  /// In en, this message translates to:
  /// **'Contrast'**
  String get commonContrast;

  /// Saturation button text
  ///
  /// In en, this message translates to:
  /// **'Saturation'**
  String get commonSaturation;

  /// Hue button text
  ///
  /// In en, this message translates to:
  /// **'Hue'**
  String get commonHue;

  /// Gamma button text
  ///
  /// In en, this message translates to:
  /// **'Gamma'**
  String get commonGamma;

  /// Exposure button text
  ///
  /// In en, this message translates to:
  /// **'Exposure'**
  String get commonExposure;

  /// Vignette button text
  ///
  /// In en, this message translates to:
  /// **'Vignette'**
  String get commonVignette;

  /// Grain button text
  ///
  /// In en, this message translates to:
  /// **'Grain'**
  String get commonGrain;

  /// Noise button text
  ///
  /// In en, this message translates to:
  /// **'Noise'**
  String get commonNoise;

  /// Pixelate button text
  ///
  /// In en, this message translates to:
  /// **'Pixelate'**
  String get commonPixelate;

  /// Posterize button text
  ///
  /// In en, this message translates to:
  /// **'Posterize'**
  String get commonPosterize;

  /// Dither button text
  ///
  /// In en, this message translates to:
  /// **'Dither'**
  String get commonDither;

  /// Threshold button text
  ///
  /// In en, this message translates to:
  /// **'Threshold'**
  String get commonThreshold;

  /// Quantize button text
  ///
  /// In en, this message translates to:
  /// **'Quantize'**
  String get commonQuantize;

  /// Desaturate button text
  ///
  /// In en, this message translates to:
  /// **'Desaturate'**
  String get commonDesaturate;

  /// Saturate button text
  ///
  /// In en, this message translates to:
  /// **'Saturate'**
  String get commonSaturate;

  /// Invert button text
  ///
  /// In en, this message translates to:
  /// **'Invert'**
  String get commonInvert;

  /// Grayscale button text
  ///
  /// In en, this message translates to:
  /// **'Grayscale'**
  String get commonGrayscale;

  /// Sepia button text
  ///
  /// In en, this message translates to:
  /// **'Sepia'**
  String get commonSepia;

  /// Vintage button text
  ///
  /// In en, this message translates to:
  /// **'Vintage'**
  String get commonVintage;

  /// Retro button text
  ///
  /// In en, this message translates to:
  /// **'Retro'**
  String get commonRetro;

  /// Black and white button text
  ///
  /// In en, this message translates to:
  /// **'Black and White'**
  String get commonBlackAndWhite;

  /// Cool button text
  ///
  /// In en, this message translates to:
  /// **'Cool'**
  String get commonCool;

  /// Warm button text
  ///
  /// In en, this message translates to:
  /// **'Warm'**
  String get commonWarm;

  /// Fade button text
  ///
  /// In en, this message translates to:
  /// **'Fade'**
  String get commonFade;

  /// Duotone button text
  ///
  /// In en, this message translates to:
  /// **'Duotone'**
  String get commonDuotone;

  /// Tricolor button text
  ///
  /// In en, this message translates to:
  /// **'Tricolor'**
  String get commonTricolor;

  /// Monochrome button text
  ///
  /// In en, this message translates to:
  /// **'Monochrome'**
  String get commonMonochrome;

  /// Polychrome button text
  ///
  /// In en, this message translates to:
  /// **'Polychrome'**
  String get commonPolychrome;

  /// Rainbow button text
  ///
  /// In en, this message translates to:
  /// **'Rainbow'**
  String get commonRainbow;

  /// Gradient button text
  ///
  /// In en, this message translates to:
  /// **'Gradient'**
  String get commonGradient;

  /// Pattern button text
  ///
  /// In en, this message translates to:
  /// **'Pattern'**
  String get commonPattern;

  /// Texture button text
  ///
  /// In en, this message translates to:
  /// **'Texture'**
  String get commonTexture;

  /// Border button text
  ///
  /// In en, this message translates to:
  /// **'Border'**
  String get commonBorder;

  /// Frame button text
  ///
  /// In en, this message translates to:
  /// **'Frame'**
  String get commonFrame;

  /// Shadow button text
  ///
  /// In en, this message translates to:
  /// **'Shadow'**
  String get commonShadow;

  /// Glow button text
  ///
  /// In en, this message translates to:
  /// **'Glow'**
  String get commonGlow;

  /// Neon button text
  ///
  /// In en, this message translates to:
  /// **'Neon'**
  String get commonNeon;

  /// Light button text
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get commonLight;

  /// Dark button text
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get commonDark;

  /// Bright button text
  ///
  /// In en, this message translates to:
  /// **'Bright'**
  String get commonBright;

  /// Dim button text
  ///
  /// In en, this message translates to:
  /// **'Dim'**
  String get commonDim;

  /// Clear button text
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get commonClear;

  /// Cloudy button text
  ///
  /// In en, this message translates to:
  /// **'Cloudy'**
  String get commonCloudy;

  /// Foggy button text
  ///
  /// In en, this message translates to:
  /// **'Foggy'**
  String get commonFoggy;

  /// Hazy button text
  ///
  /// In en, this message translates to:
  /// **'Hazy'**
  String get commonHazy;

  /// Smoky button text
  ///
  /// In en, this message translates to:
  /// **'Smoky'**
  String get commonSmoky;

  /// Dusty button text
  ///
  /// In en, this message translates to:
  /// **'Dusty'**
  String get commonDusty;

  /// Misty button text
  ///
  /// In en, this message translates to:
  /// **'Misty'**
  String get commonMisty;

  /// Frosty button text
  ///
  /// In en, this message translates to:
  /// **'Frosty'**
  String get commonFrosty;

  /// Icy button text
  ///
  /// In en, this message translates to:
  /// **'Icy'**
  String get commonIcy;

  /// Snowy button text
  ///
  /// In en, this message translates to:
  /// **'Snowy'**
  String get commonSnowy;

  /// Rainy button text
  ///
  /// In en, this message translates to:
  /// **'Rainy'**
  String get commonRainy;

  /// Stormy button text
  ///
  /// In en, this message translates to:
  /// **'Stormy'**
  String get commonStormy;

  /// Windy button text
  ///
  /// In en, this message translates to:
  /// **'Windy'**
  String get commonWindy;

  /// Breezy button text
  ///
  /// In en, this message translates to:
  /// **'Breezy'**
  String get commonBreezy;

  /// Calm button text
  ///
  /// In en, this message translates to:
  /// **'Calm'**
  String get commonCalm;

  /// Still button text
  ///
  /// In en, this message translates to:
  /// **'Still'**
  String get commonStill;

  /// Quiet button text
  ///
  /// In en, this message translates to:
  /// **'Quiet'**
  String get commonQuiet;

  /// Silent button text
  ///
  /// In en, this message translates to:
  /// **'Silent'**
  String get commonSilent;

  /// Peaceful button text
  ///
  /// In en, this message translates to:
  /// **'Peaceful'**
  String get commonPeaceful;

  /// Serene button text
  ///
  /// In en, this message translates to:
  /// **'Serene'**
  String get commonSerene;

  /// Tranquil button text
  ///
  /// In en, this message translates to:
  /// **'Tranquil'**
  String get commonTranquil;

  /// Placid button text
  ///
  /// In en, this message translates to:
  /// **'Placid'**
  String get commonPlacid;

  /// Smooth button text
  ///
  /// In en, this message translates to:
  /// **'Smooth'**
  String get commonSmooth;

  /// Rough button text
  ///
  /// In en, this message translates to:
  /// **'Rough'**
  String get commonRough;

  /// Coarse button text
  ///
  /// In en, this message translates to:
  /// **'Coarse'**
  String get commonCoarse;

  /// Fine button text
  ///
  /// In en, this message translates to:
  /// **'Fine'**
  String get commonFine;

  /// Soft button text
  ///
  /// In en, this message translates to:
  /// **'Soft'**
  String get commonSoft;

  /// Hard button text
  ///
  /// In en, this message translates to:
  /// **'Hard'**
  String get commonHard;

  /// Tough button text
  ///
  /// In en, this message translates to:
  /// **'Tough'**
  String get commonTough;

  /// Strong button text
  ///
  /// In en, this message translates to:
  /// **'Strong'**
  String get commonStrong;

  /// Weak button text
  ///
  /// In en, this message translates to:
  /// **'Weak'**
  String get commonWeak;

  /// Gentle button text
  ///
  /// In en, this message translates to:
  /// **'Gentle'**
  String get commonGentle;

  /// Mild button text
  ///
  /// In en, this message translates to:
  /// **'Mild'**
  String get commonMild;

  /// Harsh button text
  ///
  /// In en, this message translates to:
  /// **'Harsh'**
  String get commonHarsh;

  /// Severe button text
  ///
  /// In en, this message translates to:
  /// **'Severe'**
  String get commonSevere;

  /// Extreme button text
  ///
  /// In en, this message translates to:
  /// **'Extreme'**
  String get commonExtreme;

  /// Intense button text
  ///
  /// In en, this message translates to:
  /// **'Intense'**
  String get commonIntense;

  /// Moderate button text
  ///
  /// In en, this message translates to:
  /// **'Moderate'**
  String get commonModerate;

  /// Average button text
  ///
  /// In en, this message translates to:
  /// **'Average'**
  String get commonAverage;

  /// Normal button text
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get commonNormal;

  /// Standard button text
  ///
  /// In en, this message translates to:
  /// **'Standard'**
  String get commonStandard;

  /// Regular button text
  ///
  /// In en, this message translates to:
  /// **'Regular'**
  String get commonRegular;

  /// Typical button text
  ///
  /// In en, this message translates to:
  /// **'Typical'**
  String get commonTypical;

  /// Usual button text
  ///
  /// In en, this message translates to:
  /// **'Usual'**
  String get commonUsual;

  /// Common button text
  ///
  /// In en, this message translates to:
  /// **'Common'**
  String get commonCommon;

  /// Ordinary button text
  ///
  /// In en, this message translates to:
  /// **'Ordinary'**
  String get commonOrdinary;

  /// General button text
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get commonGeneral;

  /// Basic button text
  ///
  /// In en, this message translates to:
  /// **'Basic'**
  String get commonBasic;

  /// Simple button text
  ///
  /// In en, this message translates to:
  /// **'Simple'**
  String get commonSimple;

  /// Easy button text
  ///
  /// In en, this message translates to:
  /// **'Easy'**
  String get commonEasy;

  /// Difficult button text
  ///
  /// In en, this message translates to:
  /// **'Difficult'**
  String get commonDifficult;

  /// Complex button text
  ///
  /// In en, this message translates to:
  /// **'Complex'**
  String get commonComplex;

  /// Complicated button text
  ///
  /// In en, this message translates to:
  /// **'Complicated'**
  String get commonComplicated;

  /// Advanced button text
  ///
  /// In en, this message translates to:
  /// **'Advanced'**
  String get commonAdvanced;

  /// Expert button text
  ///
  /// In en, this message translates to:
  /// **'Expert'**
  String get commonExpert;

  /// Professional button text
  ///
  /// In en, this message translates to:
  /// **'Professional'**
  String get commonProfessional;

  /// Specialized button text
  ///
  /// In en, this message translates to:
  /// **'Specialized'**
  String get commonSpecialized;

  /// Technical button text
  ///
  /// In en, this message translates to:
  /// **'Technical'**
  String get commonTechnical;

  /// Scientific button text
  ///
  /// In en, this message translates to:
  /// **'Scientific'**
  String get commonScientific;

  /// Academic button text
  ///
  /// In en, this message translates to:
  /// **'Academic'**
  String get commonAcademic;

  /// Educational button text
  ///
  /// In en, this message translates to:
  /// **'Educational'**
  String get commonEducational;

  /// Instructional button text
  ///
  /// In en, this message translates to:
  /// **'Instructional'**
  String get commonInstructional;

  /// Tutorial button text
  ///
  /// In en, this message translates to:
  /// **'Tutorial'**
  String get commonTutorial;

  /// Guide button text
  ///
  /// In en, this message translates to:
  /// **'Guide'**
  String get commonGuide;

  /// Manual button text
  ///
  /// In en, this message translates to:
  /// **'Manual'**
  String get commonManual;

  /// Handbook button text
  ///
  /// In en, this message translates to:
  /// **'Handbook'**
  String get commonHandbook;

  /// Reference button text
  ///
  /// In en, this message translates to:
  /// **'Reference'**
  String get commonReference;

  /// Documentation button text
  ///
  /// In en, this message translates to:
  /// **'Documentation'**
  String get commonDocumentation;

  /// Support button text
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get commonSupport;

  /// Assistance button text
  ///
  /// In en, this message translates to:
  /// **'Assistance'**
  String get commonAssistance;

  /// Aid button text
  ///
  /// In en, this message translates to:
  /// **'Aid'**
  String get commonAid;

  /// Service button text
  ///
  /// In en, this message translates to:
  /// **'Service'**
  String get commonService;

  /// Maintenance button text
  ///
  /// In en, this message translates to:
  /// **'Maintenance'**
  String get commonMaintenance;

  /// Repair button text
  ///
  /// In en, this message translates to:
  /// **'Repair'**
  String get commonRepair;

  /// Fix button text
  ///
  /// In en, this message translates to:
  /// **'Fix'**
  String get commonFix;

  /// Solve button text
  ///
  /// In en, this message translates to:
  /// **'Solve'**
  String get commonSolve;

  /// Resolve button text
  ///
  /// In en, this message translates to:
  /// **'Resolve'**
  String get commonResolve;

  /// Address button text
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get commonAddress;

  /// Handle button text
  ///
  /// In en, this message translates to:
  /// **'Handle'**
  String get commonHandle;

  /// Manage button text
  ///
  /// In en, this message translates to:
  /// **'Manage'**
  String get commonManage;

  /// Control button text
  ///
  /// In en, this message translates to:
  /// **'Control'**
  String get commonControl;

  /// Direct button text
  ///
  /// In en, this message translates to:
  /// **'Direct'**
  String get commonDirect;

  /// Lead button text
  ///
  /// In en, this message translates to:
  /// **'Lead'**
  String get commonLead;

  /// Conduct button text
  ///
  /// In en, this message translates to:
  /// **'Conduct'**
  String get commonConduct;

  /// Operate button text
  ///
  /// In en, this message translates to:
  /// **'Operate'**
  String get commonOperate;

  /// Run button text
  ///
  /// In en, this message translates to:
  /// **'Run'**
  String get commonRun;

  /// Execute button text
  ///
  /// In en, this message translates to:
  /// **'Execute'**
  String get commonExecute;

  /// Perform button text
  ///
  /// In en, this message translates to:
  /// **'Perform'**
  String get commonPerform;

  /// Implement button text
  ///
  /// In en, this message translates to:
  /// **'Implement'**
  String get commonImplement;

  /// Carry out button text
  ///
  /// In en, this message translates to:
  /// **'Carry Out'**
  String get commonCarryOut;

  /// Accomplish button text
  ///
  /// In en, this message translates to:
  /// **'Accomplish'**
  String get commonAccomplish;

  /// Achieve button text
  ///
  /// In en, this message translates to:
  /// **'Achieve'**
  String get commonAchieve;

  /// Attain button text
  ///
  /// In en, this message translates to:
  /// **'Attain'**
  String get commonAttain;

  /// Reach button text
  ///
  /// In en, this message translates to:
  /// **'Reach'**
  String get commonReach;

  /// Obtain button text
  ///
  /// In en, this message translates to:
  /// **'Obtain'**
  String get commonObtain;

  /// Get button text
  ///
  /// In en, this message translates to:
  /// **'Get'**
  String get commonGet;

  /// Acquire button text
  ///
  /// In en, this message translates to:
  /// **'Acquire'**
  String get commonAcquire;

  /// Gain button text
  ///
  /// In en, this message translates to:
  /// **'Gain'**
  String get commonGain;

  /// Receive button text
  ///
  /// In en, this message translates to:
  /// **'Receive'**
  String get commonReceive;

  /// Collect button text
  ///
  /// In en, this message translates to:
  /// **'Collect'**
  String get commonCollect;

  /// Gather button text
  ///
  /// In en, this message translates to:
  /// **'Gather'**
  String get commonGather;

  /// Assemble button text
  ///
  /// In en, this message translates to:
  /// **'Assemble'**
  String get commonAssemble;

  /// Compile button text
  ///
  /// In en, this message translates to:
  /// **'Compile'**
  String get commonCompile;

  /// Combine button text
  ///
  /// In en, this message translates to:
  /// **'Combine'**
  String get commonCombine;

  /// Merge button text
  ///
  /// In en, this message translates to:
  /// **'Merge'**
  String get commonMerge;

  /// Join button text
  ///
  /// In en, this message translates to:
  /// **'Join'**
  String get commonJoin;

  /// Unite button text
  ///
  /// In en, this message translates to:
  /// **'Unite'**
  String get commonUnite;

  /// Connect button text
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get commonConnect;

  /// Link button text
  ///
  /// In en, this message translates to:
  /// **'Link'**
  String get commonLink;

  /// Attach button text
  ///
  /// In en, this message translates to:
  /// **'Attach'**
  String get commonAttach;

  /// Fasten button text
  ///
  /// In en, this message translates to:
  /// **'Fasten'**
  String get commonFasten;

  /// Secure button text
  ///
  /// In en, this message translates to:
  /// **'Secure'**
  String get commonSecure;

  /// Tie button text
  ///
  /// In en, this message translates to:
  /// **'Tie'**
  String get commonTie;

  /// Bind button text
  ///
  /// In en, this message translates to:
  /// **'Bind'**
  String get commonBind;

  /// Wrap button text
  ///
  /// In en, this message translates to:
  /// **'Wrap'**
  String get commonWrap;

  /// Cover button text
  ///
  /// In en, this message translates to:
  /// **'Cover'**
  String get commonCover;

  /// Enclose button text
  ///
  /// In en, this message translates to:
  /// **'Enclose'**
  String get commonEnclose;

  /// Surround button text
  ///
  /// In en, this message translates to:
  /// **'Surround'**
  String get commonSurround;

  /// Envelop button text
  ///
  /// In en, this message translates to:
  /// **'Envelop'**
  String get commonEnvelop;

  /// Contain button text
  ///
  /// In en, this message translates to:
  /// **'Contain'**
  String get commonContain;

  /// Include button text
  ///
  /// In en, this message translates to:
  /// **'Include'**
  String get commonInclude;

  /// Involve button text
  ///
  /// In en, this message translates to:
  /// **'Involve'**
  String get commonInvolve;

  /// Embrace button text
  ///
  /// In en, this message translates to:
  /// **'Embrace'**
  String get commonEmbrace;

  /// Encompass button text
  ///
  /// In en, this message translates to:
  /// **'Encompass'**
  String get commonEncompass;

  /// Span button text
  ///
  /// In en, this message translates to:
  /// **'Span'**
  String get commonSpan;

  /// Extend button text
  ///
  /// In en, this message translates to:
  /// **'Extend'**
  String get commonExtend;

  /// Stretch button text
  ///
  /// In en, this message translates to:
  /// **'Stretch'**
  String get commonStretch;

  /// Expand button text
  ///
  /// In en, this message translates to:
  /// **'Expand'**
  String get commonExpand;

  /// Grow button text
  ///
  /// In en, this message translates to:
  /// **'Grow'**
  String get commonGrow;

  /// Increase button text
  ///
  /// In en, this message translates to:
  /// **'Increase'**
  String get commonIncrease;

  /// Enlarge button text
  ///
  /// In en, this message translates to:
  /// **'Enlarge'**
  String get commonEnlarge;

  /// Magnify button text
  ///
  /// In en, this message translates to:
  /// **'Magnify'**
  String get commonMagnify;

  /// Amplify button text
  ///
  /// In en, this message translates to:
  /// **'Amplify'**
  String get commonAmplify;

  /// Boost button text
  ///
  /// In en, this message translates to:
  /// **'Boost'**
  String get commonBoost;

  /// Enhance button text
  ///
  /// In en, this message translates to:
  /// **'Enhance'**
  String get commonEnhance;

  /// Improve button text
  ///
  /// In en, this message translates to:
  /// **'Improve'**
  String get commonImprove;

  /// Better button text
  ///
  /// In en, this message translates to:
  /// **'Better'**
  String get commonBetter;

  /// Upgrade button text
  ///
  /// In en, this message translates to:
  /// **'Upgrade'**
  String get commonUpgrade;

  /// Advance button text
  ///
  /// In en, this message translates to:
  /// **'Advance'**
  String get commonAdvance;

  /// Progress button text
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get commonProgress;

  /// Develop button text
  ///
  /// In en, this message translates to:
  /// **'Develop'**
  String get commonDevelop;

  /// Evolve button text
  ///
  /// In en, this message translates to:
  /// **'Evolve'**
  String get commonEvolve;

  /// Mature button text
  ///
  /// In en, this message translates to:
  /// **'Mature'**
  String get commonMature;

  /// Ripe button text
  ///
  /// In en, this message translates to:
  /// **'Ripe'**
  String get commonRipe;

  /// Perfect button text
  ///
  /// In en, this message translates to:
  /// **'Perfect'**
  String get commonPerfect;

  /// Complete button text
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get complete;

  /// Complete button text
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get commonComplete;

  /// End button text
  ///
  /// In en, this message translates to:
  /// **'End'**
  String get commonEnd;

  /// Terminate button text
  ///
  /// In en, this message translates to:
  /// **'Terminate'**
  String get commonTerminate;

  /// Conclude button text
  ///
  /// In en, this message translates to:
  /// **'Conclude'**
  String get commonConclude;

  /// Finalize button text
  ///
  /// In en, this message translates to:
  /// **'Finalize'**
  String get commonFinalize;

  /// Shut button text
  ///
  /// In en, this message translates to:
  /// **'Shut'**
  String get commonShut;

  /// Seal button text
  ///
  /// In en, this message translates to:
  /// **'Seal'**
  String get commonSeal;

  /// Lock button text
  ///
  /// In en, this message translates to:
  /// **'Lock'**
  String get commonLock;

  /// Tighten button text
  ///
  /// In en, this message translates to:
  /// **'Tighten'**
  String get commonTighten;

  /// Organize button text
  ///
  /// In en, this message translates to:
  /// **'Organize'**
  String get commonOrganize;

  /// Arrange button text
  ///
  /// In en, this message translates to:
  /// **'Arrange'**
  String get commonArrange;

  /// Order button text
  ///
  /// In en, this message translates to:
  /// **'Order'**
  String get commonOrder;

  /// Classify button text
  ///
  /// In en, this message translates to:
  /// **'Classify'**
  String get commonClassify;

  /// Categorize button text
  ///
  /// In en, this message translates to:
  /// **'Categorize'**
  String get commonCategorize;

  /// Group button text
  ///
  /// In en, this message translates to:
  /// **'Group'**
  String get commonGroup;

  /// Cluster button text
  ///
  /// In en, this message translates to:
  /// **'Cluster'**
  String get commonCluster;

  /// Bunch button text
  ///
  /// In en, this message translates to:
  /// **'Bunch'**
  String get commonBunch;

  /// Bundle button text
  ///
  /// In en, this message translates to:
  /// **'Bundle'**
  String get commonBundle;

  /// Pack button text
  ///
  /// In en, this message translates to:
  /// **'Pack'**
  String get commonPack;

  /// Package button text
  ///
  /// In en, this message translates to:
  /// **'Package'**
  String get commonPackage;

  /// Hold button text
  ///
  /// In en, this message translates to:
  /// **'Hold'**
  String get commonHold;

  /// Carry button text
  ///
  /// In en, this message translates to:
  /// **'Carry'**
  String get commonCarry;

  /// Bear button text
  ///
  /// In en, this message translates to:
  /// **'Bear'**
  String get commonBear;

  /// Sustain button text
  ///
  /// In en, this message translates to:
  /// **'Sustain'**
  String get commonSustain;

  /// Maintain button text
  ///
  /// In en, this message translates to:
  /// **'Maintain'**
  String get commonMaintain;

  /// Keep button text
  ///
  /// In en, this message translates to:
  /// **'Keep'**
  String get commonKeep;

  /// Retain button text
  ///
  /// In en, this message translates to:
  /// **'Retain'**
  String get commonRetain;

  /// Preserve button text
  ///
  /// In en, this message translates to:
  /// **'Preserve'**
  String get commonPreserve;

  /// Conserve button text
  ///
  /// In en, this message translates to:
  /// **'Conserve'**
  String get commonConserve;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get commonSave;

  /// Store button text
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get commonStore;

  /// Reserve button text
  ///
  /// In en, this message translates to:
  /// **'Reserve'**
  String get commonReserve;

  /// Set aside button text
  ///
  /// In en, this message translates to:
  /// **'Set Aside'**
  String get commonSetAside;

  /// Put away button text
  ///
  /// In en, this message translates to:
  /// **'Put Away'**
  String get commonPutAway;

  /// Place button text
  ///
  /// In en, this message translates to:
  /// **'Place'**
  String get commonPlace;

  /// Position button text
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get commonPosition;

  /// Locate button text
  ///
  /// In en, this message translates to:
  /// **'Locate'**
  String get commonLocate;

  /// Situate button text
  ///
  /// In en, this message translates to:
  /// **'Situate'**
  String get commonSituate;

  /// Install button text
  ///
  /// In en, this message translates to:
  /// **'Install'**
  String get commonInstall;

  /// Set button text
  ///
  /// In en, this message translates to:
  /// **'Set'**
  String get commonSet;

  /// Establish button text
  ///
  /// In en, this message translates to:
  /// **'Establish'**
  String get commonEstablish;

  /// Found button text
  ///
  /// In en, this message translates to:
  /// **'Found'**
  String get commonFound;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get commonCreate;

  /// Make button text
  ///
  /// In en, this message translates to:
  /// **'Make'**
  String get commonMake;

  /// Build button text
  ///
  /// In en, this message translates to:
  /// **'Build'**
  String get commonBuild;

  /// Construct button text
  ///
  /// In en, this message translates to:
  /// **'Construct'**
  String get commonConstruct;

  /// Form button text
  ///
  /// In en, this message translates to:
  /// **'Form'**
  String get commonForm;

  /// Shape button text
  ///
  /// In en, this message translates to:
  /// **'Shape'**
  String get commonShape;

  /// Mold button text
  ///
  /// In en, this message translates to:
  /// **'Mold'**
  String get commonMold;

  /// Fashion button text
  ///
  /// In en, this message translates to:
  /// **'Fashion'**
  String get commonFashion;

  /// Design button text
  ///
  /// In en, this message translates to:
  /// **'Design'**
  String get commonDesign;

  /// Plan button text
  ///
  /// In en, this message translates to:
  /// **'Plan'**
  String get commonPlan;

  /// Devise button text
  ///
  /// In en, this message translates to:
  /// **'Devise'**
  String get commonDevise;

  /// Conceive button text
  ///
  /// In en, this message translates to:
  /// **'Conceive'**
  String get commonConceive;

  /// Imagine button text
  ///
  /// In en, this message translates to:
  /// **'Imagine'**
  String get commonImagine;

  /// Envision button text
  ///
  /// In en, this message translates to:
  /// **'Envision'**
  String get commonEnvision;

  /// Visualize button text
  ///
  /// In en, this message translates to:
  /// **'Visualize'**
  String get commonVisualize;

  /// Dream button text
  ///
  /// In en, this message translates to:
  /// **'Dream'**
  String get commonDream;

  /// Think button text
  ///
  /// In en, this message translates to:
  /// **'Think'**
  String get commonThink;

  /// Consider button text
  ///
  /// In en, this message translates to:
  /// **'Consider'**
  String get commonConsider;

  /// Ponder button text
  ///
  /// In en, this message translates to:
  /// **'Ponder'**
  String get commonPonder;

  /// Reflect button text
  ///
  /// In en, this message translates to:
  /// **'Reflect'**
  String get commonReflect;

  /// Meditate button text
  ///
  /// In en, this message translates to:
  /// **'Meditate'**
  String get commonMeditate;

  /// Contemplate button text
  ///
  /// In en, this message translates to:
  /// **'Contemplate'**
  String get commonContemplate;

  /// Study button text
  ///
  /// In en, this message translates to:
  /// **'Study'**
  String get commonStudy;

  /// Learn button text
  ///
  /// In en, this message translates to:
  /// **'Learn'**
  String get commonLearn;

  /// Discover button text
  ///
  /// In en, this message translates to:
  /// **'Discover'**
  String get commonDiscover;

  /// Find button text
  ///
  /// In en, this message translates to:
  /// **'Find'**
  String get commonFind;

  /// Uncover button text
  ///
  /// In en, this message translates to:
  /// **'Uncover'**
  String get commonUncover;

  /// Reveal button text
  ///
  /// In en, this message translates to:
  /// **'Reveal'**
  String get commonReveal;

  /// Expose button text
  ///
  /// In en, this message translates to:
  /// **'Expose'**
  String get commonExpose;

  /// Disclose button text
  ///
  /// In en, this message translates to:
  /// **'Disclose'**
  String get commonDisclose;

  /// Divulge button text
  ///
  /// In en, this message translates to:
  /// **'Divulge'**
  String get commonDivulge;

  /// Tell button text
  ///
  /// In en, this message translates to:
  /// **'Tell'**
  String get commonTell;

  /// Inform button text
  ///
  /// In en, this message translates to:
  /// **'Inform'**
  String get commonInform;

  /// Notify button text
  ///
  /// In en, this message translates to:
  /// **'Notify'**
  String get commonNotify;

  /// Announce button text
  ///
  /// In en, this message translates to:
  /// **'Announce'**
  String get commonAnnounce;

  /// Declare button text
  ///
  /// In en, this message translates to:
  /// **'Declare'**
  String get commonDeclare;

  /// Proclaim button text
  ///
  /// In en, this message translates to:
  /// **'Proclaim'**
  String get commonProclaim;

  /// Pronounce button text
  ///
  /// In en, this message translates to:
  /// **'Pronounce'**
  String get commonPronounce;

  /// State button text
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get commonState;

  /// Express button text
  ///
  /// In en, this message translates to:
  /// **'Express'**
  String get commonExpress;

  /// Voice button text
  ///
  /// In en, this message translates to:
  /// **'Voice'**
  String get commonVoice;

  /// Articulate button text
  ///
  /// In en, this message translates to:
  /// **'Articulate'**
  String get commonArticulate;

  /// Utter button text
  ///
  /// In en, this message translates to:
  /// **'Utter'**
  String get commonUtter;

  /// Say button text
  ///
  /// In en, this message translates to:
  /// **'Say'**
  String get commonSay;

  /// Speak button text
  ///
  /// In en, this message translates to:
  /// **'Speak'**
  String get commonSpeak;

  /// Talk button text
  ///
  /// In en, this message translates to:
  /// **'Talk'**
  String get commonTalk;

  /// Converse button text
  ///
  /// In en, this message translates to:
  /// **'Converse'**
  String get commonConverse;

  /// Communicate button text
  ///
  /// In en, this message translates to:
  /// **'Communicate'**
  String get commonCommunicate;

  /// Correspond button text
  ///
  /// In en, this message translates to:
  /// **'Correspond'**
  String get commonCorrespond;

  /// Contact button text
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get commonContact;

  /// Approach button text
  ///
  /// In en, this message translates to:
  /// **'Approach'**
  String get commonApproach;

  /// Accost button text
  ///
  /// In en, this message translates to:
  /// **'Accost'**
  String get commonAccost;

  /// Greet button text
  ///
  /// In en, this message translates to:
  /// **'Greet'**
  String get commonGreet;

  /// Welcome button text
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get commonWelcome;

  /// Accept button text
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get commonAccept;

  /// Take button text
  ///
  /// In en, this message translates to:
  /// **'Take'**
  String get commonTake;

  /// Procure button text
  ///
  /// In en, this message translates to:
  /// **'Procure'**
  String get commonProcure;

  /// Purchase button text
  ///
  /// In en, this message translates to:
  /// **'Purchase'**
  String get commonPurchase;

  /// Buy button text
  ///
  /// In en, this message translates to:
  /// **'Buy'**
  String get commonBuy;

  /// Shop button text
  ///
  /// In en, this message translates to:
  /// **'Shop'**
  String get commonShop;

  /// Trade button text
  ///
  /// In en, this message translates to:
  /// **'Trade'**
  String get commonTrade;

  /// Exchange button text
  ///
  /// In en, this message translates to:
  /// **'Exchange'**
  String get commonExchange;

  /// Swap button text
  ///
  /// In en, this message translates to:
  /// **'Swap'**
  String get commonSwap;

  /// Switch button text
  ///
  /// In en, this message translates to:
  /// **'Switch'**
  String get commonSwitch;

  /// Change button text
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get commonChange;

  /// Alter button text
  ///
  /// In en, this message translates to:
  /// **'Alter'**
  String get commonAlter;

  /// Modify button text
  ///
  /// In en, this message translates to:
  /// **'Modify'**
  String get commonModify;

  /// Adjust button text
  ///
  /// In en, this message translates to:
  /// **'Adjust'**
  String get commonAdjust;

  /// Tweak button text
  ///
  /// In en, this message translates to:
  /// **'Tweak'**
  String get commonTweak;

  /// Fine tune button text
  ///
  /// In en, this message translates to:
  /// **'Fine Tune'**
  String get commonFineTune;

  /// Optimize button text
  ///
  /// In en, this message translates to:
  /// **'Optimize'**
  String get commonOptimize;

  /// Refine button text
  ///
  /// In en, this message translates to:
  /// **'Refine'**
  String get commonRefine;

  /// Polish button text
  ///
  /// In en, this message translates to:
  /// **'Polish'**
  String get commonPolish;

  /// Cease button text
  ///
  /// In en, this message translates to:
  /// **'Cease'**
  String get commonCease;

  /// Halt button text
  ///
  /// In en, this message translates to:
  /// **'Halt'**
  String get commonHalt;

  /// Break button text
  ///
  /// In en, this message translates to:
  /// **'Break'**
  String get commonBreak;

  /// Interrupt button text
  ///
  /// In en, this message translates to:
  /// **'Interrupt'**
  String get commonInterrupt;

  /// Suspend button text
  ///
  /// In en, this message translates to:
  /// **'Suspend'**
  String get commonSuspend;

  /// Delay button text
  ///
  /// In en, this message translates to:
  /// **'Delay'**
  String get commonDelay;

  /// Postpone button text
  ///
  /// In en, this message translates to:
  /// **'Postpone'**
  String get commonPostpone;

  /// Defer button text
  ///
  /// In en, this message translates to:
  /// **'Defer'**
  String get commonDefer;

  /// Wait button text
  ///
  /// In en, this message translates to:
  /// **'Wait'**
  String get commonWait;

  /// Remain button text
  ///
  /// In en, this message translates to:
  /// **'Remain'**
  String get commonRemain;

  /// Stay button text
  ///
  /// In en, this message translates to:
  /// **'Stay'**
  String get commonStay;

  /// Proceed button text
  ///
  /// In en, this message translates to:
  /// **'Proceed'**
  String get commonProceed;

  /// Move button text
  ///
  /// In en, this message translates to:
  /// **'Move'**
  String get commonMove;

  /// Go button text
  ///
  /// In en, this message translates to:
  /// **'Go'**
  String get commonGo;

  /// Travel button text
  ///
  /// In en, this message translates to:
  /// **'Travel'**
  String get commonTravel;

  /// Journey button text
  ///
  /// In en, this message translates to:
  /// **'Journey'**
  String get commonJourney;

  /// Pass button text
  ///
  /// In en, this message translates to:
  /// **'Pass'**
  String get commonPass;

  /// Cross button text
  ///
  /// In en, this message translates to:
  /// **'Cross'**
  String get commonCross;

  /// Transit button text
  ///
  /// In en, this message translates to:
  /// **'Transit'**
  String get commonTransit;

  /// Transfer button text
  ///
  /// In en, this message translates to:
  /// **'Transfer'**
  String get commonTransfer;

  /// Convey button text
  ///
  /// In en, this message translates to:
  /// **'Convey'**
  String get commonConvey;

  /// Transport button text
  ///
  /// In en, this message translates to:
  /// **'Transport'**
  String get commonTransport;

  /// Bring button text
  ///
  /// In en, this message translates to:
  /// **'Bring'**
  String get commonBring;

  /// Fetch button text
  ///
  /// In en, this message translates to:
  /// **'Fetch'**
  String get commonFetch;

  /// Salute button text
  ///
  /// In en, this message translates to:
  /// **'Salute'**
  String get commonSalute;

  /// Hail button text
  ///
  /// In en, this message translates to:
  /// **'Hail'**
  String get commonHail;

  /// Nigh button text
  ///
  /// In en, this message translates to:
  /// **'Nigh'**
  String get commonNigh;

  /// Draw near button text
  ///
  /// In en, this message translates to:
  /// **'Draw Near'**
  String get commonDrawNear;

  /// Come button text
  ///
  /// In en, this message translates to:
  /// **'Come'**
  String get commonCome;

  /// Arrive button text
  ///
  /// In en, this message translates to:
  /// **'Arrive'**
  String get commonArrive;

  /// Land button text
  ///
  /// In en, this message translates to:
  /// **'Land'**
  String get commonLand;

  /// Enter button text
  ///
  /// In en, this message translates to:
  /// **'Enter'**
  String get commonEnter;

  /// Access button text
  ///
  /// In en, this message translates to:
  /// **'Access'**
  String get commonAccess;

  /// Go on button text
  ///
  /// In en, this message translates to:
  /// **'Go On'**
  String get commonGoOn;

  /// Keep on button text
  ///
  /// In en, this message translates to:
  /// **'Keep On'**
  String get commonKeepOn;

  /// Carry on button text
  ///
  /// In en, this message translates to:
  /// **'Carry On'**
  String get commonCarryOn;

  /// Persist button text
  ///
  /// In en, this message translates to:
  /// **'Persist'**
  String get commonPersist;

  /// Persevere button text
  ///
  /// In en, this message translates to:
  /// **'Persevere'**
  String get commonPersevere;

  /// Endure button text
  ///
  /// In en, this message translates to:
  /// **'Endure'**
  String get commonEndure;

  /// Last button text
  ///
  /// In en, this message translates to:
  /// **'Last'**
  String get commonLast;

  /// Save button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get markdownSaveButton;

  /// Cancel button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get markdownCancelButton;

  /// Confirm button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get markdownConfirmButton;

  /// Reset button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get markdownResetButton;

  /// Apply button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get markdownApplyButton;

  /// Close button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get markdownCloseButton;

  /// Select button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get markdownSelectButton;

  /// Browse button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Browse'**
  String get markdownBrowseButton;

  /// Search button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get markdownSearchButton;

  /// Clear button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get markdownClearButton;

  /// Delete button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get markdownDeleteButton;

  /// Edit button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get markdownEditButton;

  /// Export button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get markdownExportButton;

  /// Import button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get markdownImportButton;

  /// Share button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get markdownShareButton;

  /// Copy button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get markdownCopyButton;

  /// Paste button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Paste'**
  String get markdownPasteButton;

  /// Cut button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Cut'**
  String get markdownCutButton;

  /// Undo button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get markdownUndoButton;

  /// Redo button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Redo'**
  String get markdownRedoButton;

  /// Edit tab label in markdown editor
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get markdownEditTab;

  /// Template tab label in markdown editor
  ///
  /// In en, this message translates to:
  /// **'Template'**
  String get markdownTemplateTab;

  /// Style tab label in markdown editor
  ///
  /// In en, this message translates to:
  /// **'Style'**
  String get markdownStyleTab;

  /// Watermark tab label in markdown editor
  ///
  /// In en, this message translates to:
  /// **'Watermark'**
  String get markdownWatermarkTab;

  /// Block tab label in markdown editor
  ///
  /// In en, this message translates to:
  /// **'Block'**
  String get markdownBlockTab;

  /// Template selector title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Template Selector'**
  String get markdownTemplateSelector;

  /// Style selector title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Style Selector'**
  String get markdownStyleSelector;

  /// Watermark settings title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Watermark Settings'**
  String get markdownWatermarkSettings;

  /// Block settings title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block Settings'**
  String get markdownBlockSettings;

  /// Block configuration panel title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block Configuration Panel'**
  String get markdownBlockConfigPanel;

  /// Block management panel title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block Management Panel'**
  String get markdownBlockManagerPanel;

  /// Text field label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Text'**
  String get markdownTextLabel;

  /// Markdown content field label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Markdown Content'**
  String get markdownMarkdownContent;

  /// Watermark text field label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Watermark Text'**
  String get markdownWatermarkText;

  /// Watermark text input hint in markdown module
  ///
  /// In en, this message translates to:
  /// **'Enter watermark text'**
  String get markdownEnterWatermarkText;

  /// Markdown content input hint in markdown module
  ///
  /// In en, this message translates to:
  /// **'Enter Markdown content...'**
  String get markdownEnterMarkdownContent;

  /// Font settings section title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Font Settings'**
  String get markdownFontSettings;

  /// Font size label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get markdownFontSize;

  /// Font family label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Font Family'**
  String get markdownFontFamily;

  /// Code font label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Code Font'**
  String get markdownCodeFont;

  /// Color settings section title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Color Settings'**
  String get markdownColorSettings;

  /// Text color label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Text Color'**
  String get markdownTextColor;

  /// Background color label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Background Color'**
  String get markdownBackgroundColor;

  /// Border color label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Border Color'**
  String get markdownBorderColor;

  /// Border width label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Border Width'**
  String get markdownBorderWidth;

  /// Shadow settings section title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Shadow Settings'**
  String get markdownShadowSettings;

  /// Shadow color label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Shadow Color'**
  String get markdownShadowColor;

  /// Border radius label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Border Radius'**
  String get markdownBorderRadius;

  /// Padding label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Padding'**
  String get markdownPadding;

  /// Margin label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Margin'**
  String get markdownMargin;

  /// Watermark content section title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Watermark Content'**
  String get markdownWatermarkContent;

  /// Watermark text style label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Text Style'**
  String get markdownWatermarkTextStyle;

  /// Normal watermark style option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Normal'**
  String get markdownWatermarkNormal;

  /// Bold watermark style option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Bold'**
  String get markdownWatermarkBold;

  /// Italic watermark style option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Italic'**
  String get markdownWatermarkItalic;

  /// Watermark position label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Display Position'**
  String get markdownWatermarkPosition;

  /// Text color label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Text Color'**
  String get markdownWatermarkTextColor;

  /// Opacity slider label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Opacity'**
  String get markdownWatermarkOpacity;

  /// Font size slider label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get markdownWatermarkFontSize;

  /// Rotation slider label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Rotation'**
  String get markdownWatermarkRotation;

  /// Tile settings section title in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Tile Settings'**
  String get markdownWatermarkTileSettings;

  /// Watermark horizontal spacing label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Horizontal Spacing'**
  String get markdownWatermarkHorizontalSpacing;

  /// Watermark vertical spacing label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Vertical Spacing'**
  String get markdownWatermarkVerticalSpacing;

  /// Select watermark color button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Select Watermark Color'**
  String get markdownSelectWatermarkColor;

  /// Reset to app name button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Reset to App Name'**
  String get markdownResetToAppName;

  /// Show block title option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Show Block Title'**
  String get markdownShowBlockTitle;

  /// Show block border option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Show Block Border'**
  String get markdownShowBlockBorder;

  /// Sort by index option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Sort by Index'**
  String get markdownSortByIndex;

  /// Sort by title option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Sort by Title'**
  String get markdownSortByTitle;

  /// Sort by type option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Sort by Type'**
  String get markdownSortByType;

  /// Sort by length option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Sort by Length'**
  String get markdownSortByLength;

  /// Share result button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Share Result'**
  String get markdownShareResult;

  /// Export result button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Export Result'**
  String get markdownExportResult;

  /// Save success message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save Success'**
  String get markdownSaveSuccess;

  /// Save failed message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save Failed'**
  String get markdownSaveFailed;

  /// Template description label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Template Description'**
  String get markdownTemplateDescription;

  /// Template features label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Template Features'**
  String get markdownTemplateFeatures;

  /// Border style label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Border Style'**
  String get markdownBorderStyle;

  /// Shadow effect label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Shadow Effect'**
  String get markdownShadowEffect;

  /// Show header option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Show Header'**
  String get markdownShowHeader;

  /// Inner shadow option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Inner Shadow'**
  String get markdownInnerShadow;

  /// Heading alignment label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Heading Alignment'**
  String get markdownHeadingAlignment;

  /// Left align option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Left Align'**
  String get markdownLeftAlign;

  /// Center align option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Center Align'**
  String get markdownCenterAlign;

  /// Right align option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Right Align'**
  String get markdownRightAlign;

  /// Gradient background option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Gradient Background'**
  String get markdownGradientBackground;

  /// Background pattern option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Background Pattern'**
  String get markdownBackgroundPattern;

  /// List item style label in markdown module
  ///
  /// In en, this message translates to:
  /// **'List Item Style'**
  String get markdownListItemStyle;

  /// Checkbox style label in markdown module
  ///
  /// In en, this message translates to:
  /// **'Checkbox Style'**
  String get markdownCheckboxStyle;

  /// More actions button tooltip in markdown editor
  ///
  /// In en, this message translates to:
  /// **'More Actions'**
  String get markdownMoreActions;

  /// Share image subtitle in markdown module
  ///
  /// In en, this message translates to:
  /// **'Share rendered result with others'**
  String get markdownShareImageSubtitle;

  /// Copy content subtitle in markdown module
  ///
  /// In en, this message translates to:
  /// **'Copy Markdown text to clipboard'**
  String get markdownCopyContentSubtitle;

  /// Save to album subtitle in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save image to local album'**
  String get markdownSaveToAlbumSubtitle;

  /// Operation options title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Operation Options'**
  String get markdownOperationOptions;

  /// Select color button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Select Color'**
  String get markdownSelectColor;

  /// Choose color button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Choose Color'**
  String get markdownChooseColor;

  /// Color picker title in markdown module
  ///
  /// In en, this message translates to:
  /// **'Color Picker'**
  String get markdownColorPicker;

  /// Reset settings button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Reset Settings'**
  String get markdownResetSettings;

  /// Apply settings button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Apply Settings'**
  String get markdownApplySettings;

  /// Loading message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get markdownLoading;

  /// Generating message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Generating...'**
  String get markdownGenerating;

  /// Processing message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get markdownProcessing;

  /// Saving message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get markdownSaving;

  /// Exporting message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Exporting...'**
  String get markdownExporting;

  /// Sharing message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Sharing...'**
  String get markdownSharing;

  /// Copying message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Copying...'**
  String get markdownCopying;

  /// Success message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get markdownSuccess;

  /// Error message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get markdownError;

  /// Warning message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get markdownWarning;

  /// Info message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Info'**
  String get markdownInfo;

  /// Complete message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get markdownComplete;

  /// Failed message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get markdownFailed;

  /// Cancelled message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get markdownCancelled;

  /// Content saved message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Content saved to content library'**
  String get markdownContentSaved;

  /// Template selected message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Template \"{name}\" selected'**
  String markdownTemplateSelected(String name);

  /// Save error message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Save failed: {error}'**
  String markdownSaveError(String error);

  /// Load error message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Load error: {error}'**
  String markdownLoadError(String error);

  /// Process error message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Process error: {error}'**
  String markdownProcessError(String error);

  /// Block mode enabled message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block mode enabled'**
  String get markdownBlockModeEnabled;

  /// Block mode disabled message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block mode disabled'**
  String get markdownBlockModeDisabled;

  /// Block added message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block added'**
  String get markdownBlockAdded;

  /// Block removed message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block removed'**
  String get markdownBlockRemoved;

  /// Block updated message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block updated'**
  String get markdownBlockUpdated;

  /// Block hidden message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block hidden'**
  String get markdownBlockHidden;

  /// Block shown message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block shown'**
  String get markdownBlockShown;

  /// Block selected message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block selected'**
  String get markdownBlockSelected;

  /// Block deselected message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block deselected'**
  String get markdownBlockDeselected;

  /// Block moved message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block moved'**
  String get markdownBlockMoved;

  /// Block resized message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block resized'**
  String get markdownBlockResized;

  /// Block reordered message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block reordered'**
  String get markdownBlockReordered;

  /// Block exported message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block exported'**
  String get markdownBlockExported;

  /// Block imported message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Block imported'**
  String get markdownBlockImported;

  /// Block render feature description 1 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Block rendering can split long documents into multiple independent blocks'**
  String get markdownBlockRenderFeature1;

  /// Block render feature description 2 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Each block can be individually displayed or hidden'**
  String get markdownBlockRenderFeature2;

  /// Block render feature description 3 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Support multiple separation methods: headers, custom separators, manual separation'**
  String get markdownBlockRenderFeature3;

  /// Block render feature description 4 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Adjust block settings in the left configuration panel'**
  String get markdownBlockRenderFeature4;

  /// Block render feature description 5 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Click in the preview area to add new separator bars'**
  String get markdownBlockRenderFeature5;

  /// Block render feature description 6 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Drag separator bars to readjust block positions'**
  String get markdownBlockRenderFeature6;

  /// Block render feature description 7 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Click the eye icon on block title bars to hide/show blocks'**
  String get markdownBlockRenderFeature7;

  /// Block render feature description 8 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Different types of blocks are distinguished by different colored borders'**
  String get markdownBlockRenderFeature8;

  /// Block render feature description 9 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Blue: H1 header blocks'**
  String get markdownBlockRenderFeature9;

  /// Block render feature description 10 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Green: H2 header blocks'**
  String get markdownBlockRenderFeature10;

  /// Block render feature description 11 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Orange: Custom separator blocks'**
  String get markdownBlockRenderFeature11;

  /// Block render feature description 12 in markdown module
  ///
  /// In en, this message translates to:
  /// **'• Gray: Manual separator blocks'**
  String get markdownBlockRenderFeature12;

  /// Got it button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Got It'**
  String get markdownGotIt;

  /// I know button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'I Know'**
  String get markdownIKnow;

  /// Understood button text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get markdownUnderstood;

  /// Left align option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Left'**
  String get markdownAlignLeft;

  /// Center align option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Center'**
  String get markdownAlignCenter;

  /// Right align option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Right'**
  String get markdownAlignRight;

  /// Top left position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Top Left'**
  String get markdownPositionTopLeft;

  /// Top center position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Top Center'**
  String get markdownPositionTopCenter;

  /// Top right position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Top Right'**
  String get markdownPositionTopRight;

  /// Bottom left position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Bottom Left'**
  String get markdownPositionBottomLeft;

  /// Bottom center position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Bottom Center'**
  String get markdownPositionBottomCenter;

  /// Bottom right position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Bottom Right'**
  String get markdownPositionBottomRight;

  /// Tiled position option in markdown module
  ///
  /// In en, this message translates to:
  /// **'Tiled'**
  String get markdownPositionTiled;

  /// Exporting blocks message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Exporting blocks...'**
  String get markdownExportingBlocks;

  /// Generating report message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Generating report...'**
  String get markdownGeneratingReport;

  /// Processing complete message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Processing complete'**
  String get markdownProcessingComplete;

  /// Operation successful message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Operation successful'**
  String get markdownOperationSuccessful;

  /// Operation failed message in markdown module
  ///
  /// In en, this message translates to:
  /// **'Operation failed'**
  String get markdownOperationFailed;

  /// Show watermark text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Show Watermark'**
  String get markdownWatermarkVisible;

  /// Hide watermark text in markdown module
  ///
  /// In en, this message translates to:
  /// **'Hide Watermark'**
  String get markdownWatermarkHidden;

  /// Position and appearance section title in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Position & Appearance'**
  String get markdownWatermarkPositionAppearance;

  /// Display position dropdown label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Display Position'**
  String get markdownWatermarkDisplayPosition;

  /// Horizontal gap slider label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Horizontal Gap'**
  String get markdownWatermarkHorizontalGap;

  /// Vertical gap slider label in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Vertical Gap'**
  String get markdownWatermarkVerticalGap;

  /// Color picker dialog title in watermark settings
  ///
  /// In en, this message translates to:
  /// **'Select Watermark Color'**
  String get markdownWatermarkSelectColor;

  /// Cancel button text in watermark color picker
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get markdownWatermarkCancel;

  /// Confirm button text in watermark color picker
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get markdownWatermarkConfirm;

  /// Initialization failed message
  ///
  /// In en, this message translates to:
  /// **'Initialization failed: {error}'**
  String voiceInitializationFailed(Object error);

  /// Message when playing all voice recordings
  ///
  /// In en, this message translates to:
  /// **'Playing all recordings'**
  String get voicePlayingAllRecordings;

  /// Voice recordings list page title
  ///
  /// In en, this message translates to:
  /// **'My Recordings'**
  String get voiceMyRecordings;

  /// Play all button
  ///
  /// In en, this message translates to:
  /// **'Play All'**
  String get voicePlayAll;

  /// Empty state message when no recordings exist
  ///
  /// In en, this message translates to:
  /// **'No voice recordings'**
  String get voiceNoRecordings;

  /// Start recording button
  ///
  /// In en, this message translates to:
  /// **'Start Recording'**
  String get voiceStartRecording;

  /// Instruction text for empty state
  ///
  /// In en, this message translates to:
  /// **'Tap the button below to record your first voice'**
  String get voiceTapToStartRecording;

  /// Delete confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get voiceConfirmDelete;

  /// Delete confirmation dialog message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this voice recording?'**
  String get voiceDeleteConfirmation;

  /// Cancel button text
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get voiceCancel;

  /// Delete button text
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get voiceDelete;

  /// Message when recording is deleted
  ///
  /// In en, this message translates to:
  /// **'Recording deleted'**
  String get voiceRecordingDeleted;

  /// Transcription content label
  ///
  /// In en, this message translates to:
  /// **'Transcription:'**
  String get voiceTranscriptionContent;

  /// Today prefix for date formatting
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get voiceToday;

  /// Yesterday prefix for date formatting
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get voiceYesterday;

  /// Recording text
  ///
  /// In en, this message translates to:
  /// **'Recording'**
  String get voiceRecording;

  /// Voice recording page title
  ///
  /// In en, this message translates to:
  /// **'Voice Recording'**
  String get voiceRecordingPageTitle;

  /// Request permission button text
  ///
  /// In en, this message translates to:
  /// **'Request Permission'**
  String get voiceRequestPermission;

  /// Open settings message
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get voiceOpenSettings;

  /// Recording in progress status
  ///
  /// In en, this message translates to:
  /// **'Recording in progress...'**
  String get voiceRecordingInProgress;

  /// Ready to record status
  ///
  /// In en, this message translates to:
  /// **'Ready to start recording'**
  String get voiceReadyToRecord;

  /// Prompt text to start speaking
  ///
  /// In en, this message translates to:
  /// **'Please start speaking...'**
  String get voiceStartSpeaking;

  /// Click to start instruction
  ///
  /// In en, this message translates to:
  /// **'Click to start'**
  String get voiceClickToStart;

  /// Click to stop instruction
  ///
  /// In en, this message translates to:
  /// **'Click to stop'**
  String get voiceClickToStop;

  /// Message when recording is stopped
  ///
  /// In en, this message translates to:
  /// **'Recording stopped'**
  String get voiceRecordingStopped;

  /// Message when recording fails
  ///
  /// In en, this message translates to:
  /// **'Recording failed'**
  String get voiceRecordingFailed;

  /// Stop recording failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to stop recording: {error}'**
  String voiceStopRecordingFailed(Object error);

  /// Invalid recording file message
  ///
  /// In en, this message translates to:
  /// **'Recording file is invalid, please try again'**
  String get voiceRecordingFileInvalid;

  /// Message when recording file is not found
  ///
  /// In en, this message translates to:
  /// **'Recording file not found, recording may have failed'**
  String get voiceRecordingFileNotFound;

  /// Save recording dialog title
  ///
  /// In en, this message translates to:
  /// **'Save Recording'**
  String get voiceSaveRecording;

  /// Title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get voiceTitle;

  /// Duration label
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get voiceDuration;

  /// Transcription label
  ///
  /// In en, this message translates to:
  /// **'Transcription'**
  String get voiceTranscription;

  /// Recording and transcription saved message
  ///
  /// In en, this message translates to:
  /// **'Recording and transcription text saved'**
  String get voiceRecordingAndTranscriptionSaved;

  /// Recording saved message
  ///
  /// In en, this message translates to:
  /// **'Recording saved'**
  String get voiceRecordingSaved;

  /// Save recording failure message
  ///
  /// In en, this message translates to:
  /// **'Failed to save recording: {error}'**
  String voiceSaveRecordingFailed(Object error);

  /// No microphone permission message
  ///
  /// In en, this message translates to:
  /// **'No microphone permission, cannot record'**
  String get voiceNoMicrophonePermission;

  /// Permission required dialog title
  ///
  /// In en, this message translates to:
  /// **'Permission Required'**
  String get voicePermissionRequired;

  /// Permission instruction text
  ///
  /// In en, this message translates to:
  /// **'Please follow these steps to enable permissions:'**
  String get voicePermissionInstructions;

  /// Permission setup step 1
  ///
  /// In en, this message translates to:
  /// **'1. Click \"Go to Settings\" button'**
  String get voicePermissionStep1;

  /// Permission setup step 2
  ///
  /// In en, this message translates to:
  /// **'2. Click \"Privacy & Security\" in Settings'**
  String get voicePermissionStep2;

  /// Permission setup step 3
  ///
  /// In en, this message translates to:
  /// **'3. Click \"Microphone\" and \"Speech Recognition\" respectively'**
  String get voicePermissionStep3;

  /// Permission setup step 4
  ///
  /// In en, this message translates to:
  /// **'4. Find \"内容君\" in the list and enable permissions'**
  String get voicePermissionStep4;

  /// Permission setup note
  ///
  /// In en, this message translates to:
  /// **'Note: If you don\'t see the app, please return to the app and click \"Request Permission\" again, then check settings again'**
  String get voicePermissionNote;

  /// Go to settings button text
  ///
  /// In en, this message translates to:
  /// **'Go to Settings'**
  String get voiceGoToSettings;

  /// Permission enable instruction
  ///
  /// In en, this message translates to:
  /// **'Please enable microphone permission in settings to record'**
  String get voiceEnableMicrophonePermission;

  /// Initialization error dialog title
  ///
  /// In en, this message translates to:
  /// **'Initialization Error'**
  String get voiceInitializationError;

  /// Request permission again button text
  ///
  /// In en, this message translates to:
  /// **'Request Permission Again'**
  String get voiceRequestPermissionAgain;

  /// Start recording failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to start recording: {error}'**
  String voiceStartRecordingFailed(Object error);

  /// Permission requirement message
  ///
  /// In en, this message translates to:
  /// **'Need microphone permission to use recording feature'**
  String get voiceNeedMicrophonePermission;

  /// Permission requirement message for recording
  ///
  /// In en, this message translates to:
  /// **'Need microphone permission to record'**
  String get voiceNeedMicrophonePermissionForRecording;

  /// Speech recognition initialization failure message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition initialization failed, please ensure microphone permission is granted'**
  String get voiceSpeechRecognitionInitFailed;

  /// Voice recording page title
  ///
  /// In en, this message translates to:
  /// **'Voice Recording'**
  String get voiceRecordingTitle;

  /// Permission guide instruction
  ///
  /// In en, this message translates to:
  /// **'Please follow these steps to enable permissions:'**
  String get voicePermissionGuide;

  /// Permission guide step 1
  ///
  /// In en, this message translates to:
  /// **'1. Click \"Go to Settings\" button'**
  String get voicePermissionGuideStep1;

  /// Permission guide step 2
  ///
  /// In en, this message translates to:
  /// **'2. Click \"Privacy & Security\" in settings'**
  String get voicePermissionGuideStep2;

  /// Permission guide step 3
  ///
  /// In en, this message translates to:
  /// **'3. Click \"Microphone\" and \"Speech Recognition\" respectively'**
  String get voicePermissionGuideStep3;

  /// Permission guide step 4
  ///
  /// In en, this message translates to:
  /// **'4. Find \"内容君\" in the list and enable permissions'**
  String get voicePermissionGuideStep4;

  /// Permission guide note
  ///
  /// In en, this message translates to:
  /// **'Note: If you cannot find the app, please return to the app and click \"Request Permission\" again, then check settings again'**
  String get voicePermissionGuideNote;

  /// Recording title field label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get voiceRecordingTitleLabel;

  /// Recording duration label
  ///
  /// In en, this message translates to:
  /// **'Duration: {duration}'**
  String voiceRecordingDuration(Object duration);

  /// Recording transcription label
  ///
  /// In en, this message translates to:
  /// **'Transcription: {transcription}'**
  String voiceRecordingTranscription(Object transcription);

  /// Recording file not found message
  ///
  /// In en, this message translates to:
  /// **'Recording file does not exist, recording may have failed'**
  String get voiceRecordingFileNotExist;

  /// Please start speaking prompt
  ///
  /// In en, this message translates to:
  /// **'Please start speaking...'**
  String get voicePleaseStartSpeaking;

  /// Start recording instruction
  ///
  /// In en, this message translates to:
  /// **'Click button below to start recording\nVoice will be converted to text in real-time'**
  String get voiceClickToStartRecording;

  /// Speech recognition in progress message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition in progress, real-time transcription displayed above'**
  String get voiceSpeechRecognitionInProgress;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get voiceSave;

  /// Content tools section title
  ///
  /// In en, this message translates to:
  /// **'Content Tools'**
  String get trafficGuideContentTools;

  /// Description for content tools section
  ///
  /// In en, this message translates to:
  /// **'Choose the right tool to create traffic content'**
  String get trafficGuideToolsDescription;

  /// Text transformer tool title
  ///
  /// In en, this message translates to:
  /// **'Text Transformer'**
  String get trafficGuideTextTransformer;

  /// Subtitle for text transformer
  ///
  /// In en, this message translates to:
  /// **'Emoji conversion and character obfuscation'**
  String get trafficGuideTextTransformerSubtitle;

  /// Watermark processor tool title
  ///
  /// In en, this message translates to:
  /// **'Watermark Processor'**
  String get trafficGuideWatermarkProcessor;

  /// Subtitle for watermark processor
  ///
  /// In en, this message translates to:
  /// **'Add and remove invisible watermarks'**
  String get trafficGuideWatermarkProcessorSubtitle;

  /// New project button title
  ///
  /// In en, this message translates to:
  /// **'New Project'**
  String get trafficGuideNewProject;

  /// Subtitle for new project
  ///
  /// In en, this message translates to:
  /// **'Create traffic project configuration'**
  String get trafficGuideNewProjectSubtitle;

  /// My projects section title
  ///
  /// In en, this message translates to:
  /// **'My Projects'**
  String get trafficGuideMyProjects;

  /// Description for projects section
  ///
  /// In en, this message translates to:
  /// **'Manage your traffic project configurations'**
  String get trafficGuideProjectsDescription;

  /// No projects message
  ///
  /// In en, this message translates to:
  /// **'No Projects'**
  String get trafficGuideNoProjects;

  /// Description for no projects state
  ///
  /// In en, this message translates to:
  /// **'Click \'New Project\' to create your first traffic project'**
  String get trafficGuideNoProjectsDescription;

  /// Refresh tooltip
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get trafficGuideRefresh;

  /// Loading message
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get trafficGuideLoading;

  /// Last updated date format
  ///
  /// In en, this message translates to:
  /// **'Updated: {date}'**
  String trafficGuideLastUpdated(Object date);

  /// Confirm delete dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get trafficGuideConfirmDelete;

  /// Delete confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete project \"{name}\"?'**
  String trafficGuideDeleteConfirmation(Object name);

  /// Project deleted success message
  ///
  /// In en, this message translates to:
  /// **'Project \"{name}\" deleted'**
  String trafficGuideProjectDeleted(Object name);

  /// Edit action
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get trafficGuideEdit;

  /// Delete action
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get trafficGuideDelete;

  /// Project editor page title
  ///
  /// In en, this message translates to:
  /// **'Project Editor'**
  String get trafficGuideProjectEditor;

  /// Basic information section title
  ///
  /// In en, this message translates to:
  /// **'Basic Information'**
  String get trafficGuideBasicInfo;

  /// Project name field label
  ///
  /// In en, this message translates to:
  /// **'Project Name'**
  String get trafficGuideProjectName;

  /// Project name field hint
  ///
  /// In en, this message translates to:
  /// **'Enter project name'**
  String get trafficGuideProjectNameHint;

  /// Project description field label
  ///
  /// In en, this message translates to:
  /// **'Project Description'**
  String get trafficGuideProjectDescription;

  /// Project description field hint
  ///
  /// In en, this message translates to:
  /// **'Enter project description'**
  String get trafficGuideProjectDescriptionHint;

  /// Project name validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter project name'**
  String get trafficGuideProjectNameRequired;

  /// Image configuration section title
  ///
  /// In en, this message translates to:
  /// **'Image Configuration'**
  String get trafficGuideImageConfig;

  /// Default text field label
  ///
  /// In en, this message translates to:
  /// **'Default Text'**
  String get trafficGuideDefaultText;

  /// Default text field hint
  ///
  /// In en, this message translates to:
  /// **'Enter default display text'**
  String get trafficGuideDefaultTextHint;

  /// Font family field label
  ///
  /// In en, this message translates to:
  /// **'Font Family'**
  String get trafficGuideFontFamily;

  /// Text transform configuration section title
  ///
  /// In en, this message translates to:
  /// **'Text Transform Configuration'**
  String get trafficGuideTextTransformConfig;

  /// Emoji conversion toggle title
  ///
  /// In en, this message translates to:
  /// **'Emoji Conversion'**
  String get trafficGuideEmojiConversion;

  /// Emoji conversion subtitle
  ///
  /// In en, this message translates to:
  /// **'Convert numbers and letters to special Unicode characters'**
  String get trafficGuideEmojiConversionSubtitle;

  /// Unicode variation toggle title
  ///
  /// In en, this message translates to:
  /// **'Unicode Variation'**
  String get trafficGuideUnicodeVariation;

  /// Unicode variation subtitle
  ///
  /// In en, this message translates to:
  /// **'Add diacritical characters and special Unicode'**
  String get trafficGuideUnicodeVariationSubtitle;

  /// Invisible characters toggle title
  ///
  /// In en, this message translates to:
  /// **'Invisible Characters'**
  String get trafficGuideInvisibleChars;

  /// Invisible characters subtitle
  ///
  /// In en, this message translates to:
  /// **'Insert invisible characters in text'**
  String get trafficGuideInvisibleCharsSubtitle;

  /// Sensitive word masking toggle title
  ///
  /// In en, this message translates to:
  /// **'Sensitive Word Masking'**
  String get trafficGuideSensitiveWordMasking;

  /// Sensitive word masking subtitle
  ///
  /// In en, this message translates to:
  /// **'Apply character obfuscation to sensitive words'**
  String get trafficGuideSensitiveWordMaskingSubtitle;

  /// Sensitive words field label
  ///
  /// In en, this message translates to:
  /// **'Sensitive Words'**
  String get trafficGuideSensitiveWords;

  /// Sensitive words field hint
  ///
  /// In en, this message translates to:
  /// **'Enter sensitive words, separated by commas'**
  String get trafficGuideSensitiveWordsHint;

  /// Watermark configuration section title
  ///
  /// In en, this message translates to:
  /// **'Watermark Configuration'**
  String get trafficGuideWatermarkConfig;

  /// Watermark text field hint
  ///
  /// In en, this message translates to:
  /// **'Enter watermark content'**
  String get trafficGuideWatermarkTextHint;

  /// Invisible watermark option
  ///
  /// In en, this message translates to:
  /// **'Invisible Watermark'**
  String get trafficGuideInvisibleWatermark;

  /// Opacity setting
  ///
  /// In en, this message translates to:
  /// **'Opacity'**
  String get trafficGuideOpacity;

  /// Watermark font size field label
  ///
  /// In en, this message translates to:
  /// **'Font Size'**
  String get trafficGuideWatermarkFontSize;

  /// Project save success message
  ///
  /// In en, this message translates to:
  /// **'Project saved successfully'**
  String get trafficGuideProjectSaved;

  /// Save project button text
  ///
  /// In en, this message translates to:
  /// **'Save Project'**
  String get trafficGuideSaveProject;

  /// Saving progress text
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get trafficGuideSaving;

  /// Default new project name
  ///
  /// In en, this message translates to:
  /// **'New Project'**
  String get trafficGuideNewProjectName;

  /// Default new project description
  ///
  /// In en, this message translates to:
  /// **'Traffic project configuration'**
  String get trafficGuideNewProjectDescription;

  /// Image generator screen title
  ///
  /// In en, this message translates to:
  /// **'Traffic Image Generator'**
  String get trafficGuideImageGeneratorTitle;

  /// Image configuration section title
  ///
  /// In en, this message translates to:
  /// **'Image Configuration'**
  String get trafficGuideImageConfiguration;

  /// Text content validation error
  ///
  /// In en, this message translates to:
  /// **'Please enter text content'**
  String get trafficGuideTextRequired;

  /// Interference settings section title
  ///
  /// In en, this message translates to:
  /// **'Interference Settings'**
  String get trafficGuideInterferenceSettings;

  /// Interference level slider label
  ///
  /// In en, this message translates to:
  /// **'Interference Level'**
  String get trafficGuideInterferenceLevel;

  /// Watermark settings section
  ///
  /// In en, this message translates to:
  /// **'Watermark Settings'**
  String get trafficGuideWatermarkSettings;

  /// Watermark content label
  ///
  /// In en, this message translates to:
  /// **'Watermark Content'**
  String get trafficGuideWatermarkContent;

  /// Hint for watermark content input
  ///
  /// In en, this message translates to:
  /// **'Enter watermark content...'**
  String get trafficGuideWatermarkContentHint;

  /// Preview button text
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get trafficGuidePreview;

  /// Save to album tooltip
  ///
  /// In en, this message translates to:
  /// **'Save to Album'**
  String get trafficGuideSaveToAlbum;

  /// Share tooltip
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get trafficGuideShare;

  /// Select color dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Color'**
  String get trafficGuideSelectColor;

  /// Black color name
  ///
  /// In en, this message translates to:
  /// **'Black'**
  String get trafficGuideBlack;

  /// White color name
  ///
  /// In en, this message translates to:
  /// **'White'**
  String get trafficGuideWhite;

  /// Red color name
  ///
  /// In en, this message translates to:
  /// **'Red'**
  String get trafficGuideRed;

  /// Green color name
  ///
  /// In en, this message translates to:
  /// **'Green'**
  String get trafficGuideGreen;

  /// Blue color name
  ///
  /// In en, this message translates to:
  /// **'Blue'**
  String get trafficGuideBlue;

  /// Yellow color name
  ///
  /// In en, this message translates to:
  /// **'Yellow'**
  String get trafficGuideYellow;

  /// Purple color name
  ///
  /// In en, this message translates to:
  /// **'Purple'**
  String get trafficGuidePurple;

  /// Cyan color name
  ///
  /// In en, this message translates to:
  /// **'Cyan'**
  String get trafficGuideCyan;

  /// Generate image button text
  ///
  /// In en, this message translates to:
  /// **'Generate Image'**
  String get trafficGuideGenerateImage;

  /// Generating progress text
  ///
  /// In en, this message translates to:
  /// **'Generating...'**
  String get trafficGuideGenerating;

  /// Image generation error message
  ///
  /// In en, this message translates to:
  /// **'Image generation failed: {error}'**
  String trafficGuideImageGenerationFailed(Object error);

  /// Long press to save hint
  ///
  /// In en, this message translates to:
  /// **'Long press image to save to album'**
  String get trafficGuideLongPressToSave;

  /// Share feature in development message
  ///
  /// In en, this message translates to:
  /// **'Share feature in development...'**
  String get trafficGuideShareFeatureInProgress;

  /// Text transformer screen title
  ///
  /// In en, this message translates to:
  /// **'Text Transformer'**
  String get trafficGuideTextTransformerTitle;

  /// Transform settings section title
  ///
  /// In en, this message translates to:
  /// **'Transform Settings'**
  String get trafficGuideTransformSettings;

  /// Transform text button text
  ///
  /// In en, this message translates to:
  /// **'Transform Text'**
  String get trafficGuideTransformText;

  /// Transforming progress text
  ///
  /// In en, this message translates to:
  /// **'Transforming...'**
  String get trafficGuideTransforming;

  /// Input text section title
  ///
  /// In en, this message translates to:
  /// **'Input Text'**
  String get trafficGuideInputText;

  /// Characters count label
  ///
  /// In en, this message translates to:
  /// **'characters'**
  String get trafficGuideCharacters;

  /// Input text field hint
  ///
  /// In en, this message translates to:
  /// **'Enter text to transform...'**
  String get trafficGuideInputHint;

  /// Transform result section title
  ///
  /// In en, this message translates to:
  /// **'Transform Result'**
  String get trafficGuideTransformResult;

  /// Transform result field hint
  ///
  /// In en, this message translates to:
  /// **'Transformed text will appear here...'**
  String get trafficGuideResultHint;

  /// Transform error message
  ///
  /// In en, this message translates to:
  /// **'Transform failed: {error}'**
  String trafficGuideTransformFailed(Object error);

  /// Copy result button text
  ///
  /// In en, this message translates to:
  /// **'Copy Result'**
  String get trafficGuideCopyResult;

  /// Clear button text
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get trafficGuideClear;

  /// Settings tooltip
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get trafficGuideSettings;

  /// Advanced settings section
  ///
  /// In en, this message translates to:
  /// **'Advanced Settings'**
  String get trafficGuideAdvancedSettings;

  /// Copied to clipboard message
  ///
  /// In en, this message translates to:
  /// **'Copied to clipboard'**
  String get trafficGuideCopiedToClipboard;

  /// Custom character mapping section title
  ///
  /// In en, this message translates to:
  /// **'Custom Character Mapping'**
  String get trafficGuideCustomCharacterMapping;

  /// Mapping format hint
  ///
  /// In en, this message translates to:
  /// **'Format: original=target (one per line)'**
  String get trafficGuideMappingFormat;

  /// Mapping example
  ///
  /// In en, this message translates to:
  /// **'Example:\na=ᴀ\nb=ʙ'**
  String get trafficGuideMappingExample;

  /// Confirm button text
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get trafficGuideConfirm;

  /// Processing mode selection
  ///
  /// In en, this message translates to:
  /// **'Processing Mode'**
  String get trafficGuideProcessingMode;

  /// Add watermark mode option
  ///
  /// In en, this message translates to:
  /// **'Add Watermark'**
  String get trafficGuideAddWatermarkMode;

  /// Remove watermark mode option
  ///
  /// In en, this message translates to:
  /// **'Remove Watermark'**
  String get trafficGuideRemoveWatermarkMode;

  /// Process text button text
  ///
  /// In en, this message translates to:
  /// **'Process Text'**
  String get trafficGuideProcessText;

  /// Processing status message
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get trafficGuideProcessing;

  /// Original text section label
  ///
  /// In en, this message translates to:
  /// **'Original Text'**
  String get trafficGuideOriginalText;

  /// Watermarked text section label
  ///
  /// In en, this message translates to:
  /// **'Watermarked Text'**
  String get trafficGuideWatermarkedText;

  /// Hint for process result display area
  ///
  /// In en, this message translates to:
  /// **'Processing result will be displayed here'**
  String get trafficGuideProcessHint;

  /// Watermark identifier label
  ///
  /// In en, this message translates to:
  /// **'Watermark Identifier'**
  String get trafficGuideWatermarkIdentifier;

  /// Hint for watermark identifier input
  ///
  /// In en, this message translates to:
  /// **'Enter watermark identifier to remove...'**
  String get trafficGuideWatermarkIdentifierHint;

  /// Rotation angle setting
  ///
  /// In en, this message translates to:
  /// **'Rotation Angle'**
  String get trafficGuideRotationAngle;

  /// Validation message for empty text input
  ///
  /// In en, this message translates to:
  /// **'Please enter text to process'**
  String get trafficGuideEnterTextToProcess;

  /// Validation message for empty watermark content
  ///
  /// In en, this message translates to:
  /// **'Please enter watermark content'**
  String get trafficGuideEnterWatermarkContent;

  /// Sensitive words list label
  ///
  /// In en, this message translates to:
  /// **'Sensitive Words List'**
  String get trafficGuideSensitiveWordsList;

  /// Sensitive words list hint text
  ///
  /// In en, this message translates to:
  /// **'Enter sensitive words, separated by commas'**
  String get trafficGuideSensitiveWordsListHint;

  /// Hint for text input when adding watermark
  ///
  /// In en, this message translates to:
  /// **'Enter text content that needs watermark added'**
  String get trafficGuideWatermarkAddHint;

  /// Hint for text input when removing watermark
  ///
  /// In en, this message translates to:
  /// **'Enter text content that needs watermark removed'**
  String get trafficGuideWatermarkRemoveHint;

  /// Text Cards home page title
  ///
  /// In en, this message translates to:
  /// **'Text Cards'**
  String get textCardsHomePageTitle;

  /// Text Cards home page subtitle
  ///
  /// In en, this message translates to:
  /// **'Modern Style • Inline Editing • HD Export'**
  String get textCardsHomePageSubtitle;

  /// Start creating button text
  ///
  /// In en, this message translates to:
  /// **'Start Creating'**
  String get textCardsStartCreating;

  /// Quick actions section title
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get textCardsQuickActions;

  /// Template library action title
  ///
  /// In en, this message translates to:
  /// **'Template Library'**
  String get textCardsTemplateLibrary;

  /// Template library action subtitle
  ///
  /// In en, this message translates to:
  /// **'16+ Beautiful Templates'**
  String get textCardsTemplateLibrarySubtitle;

  /// Smart split action title
  ///
  /// In en, this message translates to:
  /// **'Smart Split'**
  String get textCardsSmartSplit;

  /// Smart split action subtitle
  ///
  /// In en, this message translates to:
  /// **'Long Text Segmentation'**
  String get textCardsSmartSplitSubtitle;

  /// Content library action title
  ///
  /// In en, this message translates to:
  /// **'Content Library'**
  String get textCardsContentLibrary;

  /// Content library action subtitle
  ///
  /// In en, this message translates to:
  /// **'Manage All Cards'**
  String get textCardsContentLibrarySubtitle;

  /// Share action title
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get textCardsShare;

  /// Share action subtitle
  ///
  /// In en, this message translates to:
  /// **'Export HD Images'**
  String get textCardsShareSubtitle;

  /// Features section title
  ///
  /// In en, this message translates to:
  /// **'Features'**
  String get textCardsFeatures;

  /// Modern templates feature title
  ///
  /// In en, this message translates to:
  /// **'Modern Style Templates'**
  String get textCardsModernTemplates;

  /// Modern templates feature description
  ///
  /// In en, this message translates to:
  /// **'Carefully designed modern social and reading style templates to make your content more attractive'**
  String get textCardsModernTemplatesDesc;

  /// Inline editing feature title
  ///
  /// In en, this message translates to:
  /// **'Inline Text Editing'**
  String get textCardsInlineEditing;

  /// Inline editing feature description
  ///
  /// In en, this message translates to:
  /// **'Select any text fragment, adjust font, color, size in real-time, what you see is what you get'**
  String get textCardsInlineEditingDesc;

  /// HD export feature title
  ///
  /// In en, this message translates to:
  /// **'HD Image Export'**
  String get textCardsHDExport;

  /// HD export feature description
  ///
  /// In en, this message translates to:
  /// **'Support multiple resolutions and aspect ratios, save to album with one click, perfect for all platforms'**
  String get textCardsHDExportDesc;

  /// View content library button text
  ///
  /// In en, this message translates to:
  /// **'View My Content Library'**
  String get textCardsViewContentLibrary;

  /// Content library button subtitle
  ///
  /// In en, this message translates to:
  /// **'Manage and browse all created cards'**
  String get textCardsManageCards;

  /// Create button text
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get textCardsCreate;

  /// Message when user tries to share without creating a card
  ///
  /// In en, this message translates to:
  /// **'Please create a card first'**
  String get textCardsPleaseCreateCardFirst;

  /// Success message when card is created
  ///
  /// In en, this message translates to:
  /// **'Card created successfully! Saved to content library'**
  String get textCardsCardCreatedSuccess;

  /// Success message for batch creation
  ///
  /// In en, this message translates to:
  /// **'Batch creation successful! Created {count} cards'**
  String textCardsBatchCreateSuccess(Object count);

  /// Partial success message for batch creation
  ///
  /// In en, this message translates to:
  /// **'Batch creation completed! Successfully created {success}/{total} cards'**
  String textCardsBatchCreatePartial(Object success, Object total);

  /// Error message when card creation fails
  ///
  /// In en, this message translates to:
  /// **'Creation failed: {error}'**
  String textCardsCreateFailed(Object error);

  /// Error message when batch creation fails
  ///
  /// In en, this message translates to:
  /// **'Batch creation failed: {error}'**
  String textCardsBatchCreateFailed(Object error);

  /// Card editor page title
  ///
  /// In en, this message translates to:
  /// **'Card Editor'**
  String get textCardsEditorTitle;

  /// Create card title
  ///
  /// In en, this message translates to:
  /// **'Create Card'**
  String get textCardsCreateCard;

  /// Edit card title
  ///
  /// In en, this message translates to:
  /// **'Edit Card'**
  String get textCardsEditCard;

  /// Save button text
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get textCardsSave;

  /// Edit tab text
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get textCardsEdit;

  /// Preview tab text
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get textCardsPreview;

  /// Card title input hint
  ///
  /// In en, this message translates to:
  /// **'Enter card title...'**
  String get textCardsEnterCardTitle;

  /// Content input hint with markdown examples
  ///
  /// In en, this message translates to:
  /// **'Enter content...\n\nSupports Markdown format:\n• **Bold**\n• *Italic*\n• • Unordered list\n• 1. Ordered list'**
  String get textCardsEnterContent;

  /// Validation message when content is empty
  ///
  /// In en, this message translates to:
  /// **'Please enter content'**
  String get textCardsContentRequired;

  /// Error message when save fails
  ///
  /// In en, this message translates to:
  /// **'Save failed: {error}'**
  String textCardsSaveFailed(Object error);

  /// Change template dialog title
  ///
  /// In en, this message translates to:
  /// **'Change Template'**
  String get textCardsChangeTemplate;

  /// Hide title tooltip
  ///
  /// In en, this message translates to:
  /// **'Hide Title'**
  String get textCardsHideTitle;

  /// Show title tooltip
  ///
  /// In en, this message translates to:
  /// **'Show Title'**
  String get textCardsShowTitle;

  /// Bold text placeholder
  ///
  /// In en, this message translates to:
  /// **'Bold text'**
  String get textCardsBoldText;

  /// Italic text placeholder
  ///
  /// In en, this message translates to:
  /// **'Italic text'**
  String get textCardsItalicText;

  /// Underline text placeholder
  ///
  /// In en, this message translates to:
  /// **'Underline text'**
  String get textCardsUnderlineText;

  /// Preview placeholder text
  ///
  /// In en, this message translates to:
  /// **'Enter content in the edit tab to see preview...'**
  String get textCardsPreviewPlaceholder;

  /// Content editor page title
  ///
  /// In en, this message translates to:
  /// **'Content Editor'**
  String get textCardsContentEditor;

  /// Title input hint
  ///
  /// In en, this message translates to:
  /// **'Enter title (optional)'**
  String get textCardsEnterTitle;

  /// Add split marker tooltip
  ///
  /// In en, this message translates to:
  /// **'Add Split Marker'**
  String get textCardsAddSplitMarker;

  /// Enter renderer tooltip
  ///
  /// In en, this message translates to:
  /// **'Enter Renderer'**
  String get textCardsEnterRenderer;

  /// Split marker info text
  ///
  /// In en, this message translates to:
  /// **'Set {count} split markers, will generate {sections} cards'**
  String textCardsSplitMarkerInfo(Object count, Object sections);

  /// Content input hint with tips
  ///
  /// In en, this message translates to:
  /// **'Enter or paste your content here...\n\nTips:\n- Use # to create headings\n- Use - or * to create lists\n- Use > to create quotes\n- Click split button to add split marker at cursor position'**
  String get textCardsEnterContentHint;

  /// Text preview label
  ///
  /// In en, this message translates to:
  /// **'Text Preview'**
  String get textCardsTextPreview;

  /// Preview placeholder text
  ///
  /// In en, this message translates to:
  /// **'Preview will appear when content is entered'**
  String get textCardsPreviewWillAppear;

  /// Split marker dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Split Marker'**
  String get textCardsSelectSplitMarker;

  /// Predefined markers label
  ///
  /// In en, this message translates to:
  /// **'Predefined Markers:'**
  String get textCardsPredefinedMarkers;

  /// Custom marker label
  ///
  /// In en, this message translates to:
  /// **'Custom Marker:'**
  String get textCardsCustomMarker;

  /// Custom marker input hint
  ///
  /// In en, this message translates to:
  /// **'Enter custom split marker'**
  String get textCardsEnterCustomMarker;

  /// Use custom marker button
  ///
  /// In en, this message translates to:
  /// **'Use Custom'**
  String get textCardsUseCustom;

  /// Default card name
  ///
  /// In en, this message translates to:
  /// **'Unnamed Card'**
  String get textCardsUnnamedCard;

  /// Default document name
  ///
  /// In en, this message translates to:
  /// **'Unnamed Document'**
  String get textCardsUnnamedDocument;

  /// SVG editor page title
  ///
  /// In en, this message translates to:
  /// **'SVG Editor'**
  String get svgEditorTitle;

  /// SVG manager page title
  ///
  /// In en, this message translates to:
  /// **'SVG Manager'**
  String get svgManagerTitle;

  /// Default SVG name
  ///
  /// In en, this message translates to:
  /// **'Untitled SVG'**
  String get svgUntitled;

  /// Edit tab label
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get svgEditTab;

  /// Preview tab label
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get svgPreviewTab;

  /// More actions tooltip
  ///
  /// In en, this message translates to:
  /// **'More Actions'**
  String get svgMoreActions;

  /// Import SVG file menu item
  ///
  /// In en, this message translates to:
  /// **'Import SVG File'**
  String get svgImportFile;

  /// Save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get svgSave;

  /// Export as PNG menu item
  ///
  /// In en, this message translates to:
  /// **'Export as PNG'**
  String get svgExportPng;

  /// Share as PNG menu item
  ///
  /// In en, this message translates to:
  /// **'Share as PNG'**
  String get svgSharePng;

  /// Share SVG menu item
  ///
  /// In en, this message translates to:
  /// **'Share SVG'**
  String get svgShareSvg;

  /// Rename menu item
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get svgRename;

  /// Save changes button
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get svgSaveChanges;

  /// File name input dialog title
  ///
  /// In en, this message translates to:
  /// **'Enter SVG File Name'**
  String get svgEnterFileName;

  /// File name input label
  ///
  /// In en, this message translates to:
  /// **'File Name'**
  String get svgFileName;

  /// File name input hint
  ///
  /// In en, this message translates to:
  /// **'Enter SVG file name'**
  String get svgFileNameHint;

  /// File name validation error
  ///
  /// In en, this message translates to:
  /// **'File name cannot be empty'**
  String get svgFileNameRequired;

  /// Processing message
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get svgProcessing;

  /// Loading message
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get svgLoading;

  /// Document not found error
  ///
  /// In en, this message translates to:
  /// **'Document not found'**
  String get svgDocumentNotFound;

  /// Load document error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to load document: {error}'**
  String svgLoadDocumentFailed(Object error);

  /// Create document error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to create document: {error}'**
  String svgCreateDocumentFailed(Object error);

  /// Save document error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to save document: {error}'**
  String svgSaveDocumentFailed(Object error);

  /// Save success message
  ///
  /// In en, this message translates to:
  /// **'Saved successfully'**
  String get svgSaveSuccess;

  /// Import file error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to import SVG file: {error}'**
  String svgImportFailed(Object error);

  /// PNG export success message with parameter
  ///
  /// In en, this message translates to:
  /// **'PNG export successful: {path}'**
  String svgExportPngSuccess(Object path);

  /// PNG export failed message
  ///
  /// In en, this message translates to:
  /// **'PNG export failed'**
  String get svgExportPngFailed;

  /// Share SVG error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to share SVG: {error}'**
  String svgShareSvgFailed(Object error);

  /// Share PNG error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to share PNG: {error}'**
  String svgSharePngFailed(Object error);

  /// Invalid SVG error with parameter
  ///
  /// In en, this message translates to:
  /// **'Invalid SVG: {error}'**
  String svgInvalidSvg(Object error);

  /// Save first reminder
  ///
  /// In en, this message translates to:
  /// **'Please save document first'**
  String get svgSaveFirst;

  /// SVG code input hint
  ///
  /// In en, this message translates to:
  /// **'Enter SVG code'**
  String get svgEnterSvgCode;

  /// No content message
  ///
  /// In en, this message translates to:
  /// **'No SVG content'**
  String get svgNoContent;

  /// Instruction message
  ///
  /// In en, this message translates to:
  /// **'Please enter SVG code in the editor tab'**
  String get svgEnterCodeInEditor;

  /// Create new SVG button
  ///
  /// In en, this message translates to:
  /// **'Create New SVG'**
  String get svgCreateNew;

  /// Import SVG button
  ///
  /// In en, this message translates to:
  /// **'Import SVG'**
  String get svgImport;

  /// Import SVG file button
  ///
  /// In en, this message translates to:
  /// **'Import SVG File'**
  String get svgImportSvgFile;

  /// Import tooltip
  ///
  /// In en, this message translates to:
  /// **'Import SVG'**
  String get svgImportTooltip;

  /// New SVG tooltip
  ///
  /// In en, this message translates to:
  /// **'New SVG'**
  String get svgNewTooltip;

  /// Create new SVG tooltip
  ///
  /// In en, this message translates to:
  /// **'Create new SVG'**
  String get svgCreateNewTooltip;

  /// No documents message
  ///
  /// In en, this message translates to:
  /// **'No SVG Documents'**
  String get svgNoDocuments;

  /// No documents description
  ///
  /// In en, this message translates to:
  /// **'Create a new SVG document or import existing files to get started'**
  String get svgNoDocumentsDesc;

  /// Load documents error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to load documents: {error}'**
  String svgLoadFailed(Object error);

  /// Delete confirmation dialog title
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get svgDeleteConfirm;

  /// Delete confirmation message with parameter
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete \"{title}\"?'**
  String svgDeleteConfirmMessage(Object title);

  /// Delete document error with parameter
  ///
  /// In en, this message translates to:
  /// **'Failed to delete document: {error}'**
  String svgDeleteFailed(Object error);

  /// Edit button
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get svgEdit;

  /// Share button
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get svgShare;

  /// Share as PNG button
  ///
  /// In en, this message translates to:
  /// **'Share as PNG'**
  String get svgShareAsPng;

  /// Delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get svgDelete;

  /// Saved to library message
  ///
  /// In en, this message translates to:
  /// **'SVG saved to content library'**
  String get svgSavedToLibrary;

  /// Created date with parameter
  ///
  /// In en, this message translates to:
  /// **'Created: {date}'**
  String svgCreatedAt(Object date);

  /// PDF manager page title
  ///
  /// In en, this message translates to:
  /// **'PDF Document Management'**
  String get pdfManagerTitle;

  /// PDF search hint text
  ///
  /// In en, this message translates to:
  /// **'Search...'**
  String get pdfSearch;

  /// Search hint text
  ///
  /// In en, this message translates to:
  /// **'Search...'**
  String get pdfSearchHint;

  /// No documents found message
  ///
  /// In en, this message translates to:
  /// **'No PDF documents found'**
  String get pdfNoDocuments;

  /// Search suggestion
  ///
  /// In en, this message translates to:
  /// **'Try using different search keywords'**
  String get pdfTryDifferentSearch;

  /// Delete confirmation title
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get pdfConfirmDelete;

  /// Delete confirmation message with filename
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete \"{filename}\"? This action cannot be undone.'**
  String pdfDeleteConfirm(Object filename);

  /// Batch delete confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete {count} selected files? This action cannot be undone.'**
  String pdfBatchDeleteConfirm(Object count);

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get pdfCancel;

  /// Delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get pdfDelete;

  /// Import PDF button
  ///
  /// In en, this message translates to:
  /// **'Import PDF'**
  String get pdfImport;

  /// Import PDF tooltip
  ///
  /// In en, this message translates to:
  /// **'Import PDF'**
  String get pdfImportTooltip;

  /// PDF security settings title
  ///
  /// In en, this message translates to:
  /// **'PDF Security Settings'**
  String get pdfSecuritySettings;

  /// Select menu item
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get pdfSelect;

  /// Merge button
  ///
  /// In en, this message translates to:
  /// **'Merge'**
  String get pdfMerge;

  /// Merge validation message
  ///
  /// In en, this message translates to:
  /// **'Please select at least two PDF files to merge'**
  String get pdfSelectAtLeastTwo;

  /// Merge success message
  ///
  /// In en, this message translates to:
  /// **'PDF merge successful'**
  String get pdfMergeSuccess;

  /// Merge failed message
  ///
  /// In en, this message translates to:
  /// **'PDF merge failed'**
  String get pdfMergeFailed;

  /// PDF center title
  ///
  /// In en, this message translates to:
  /// **'PDF Intelligent Management Center'**
  String get pdfIntelligentCenter;

  /// PDF center subtitle
  ///
  /// In en, this message translates to:
  /// **'Professional PDF tool integrating reading, editing, security, and sharing'**
  String get pdfCenterSubtitle;

  /// Version info
  ///
  /// In en, this message translates to:
  /// **'v2.0 Professional'**
  String get pdfVersion;

  /// Core features section title
  ///
  /// In en, this message translates to:
  /// **'Core Features'**
  String get pdfCoreFeatures;

  /// Professional label
  ///
  /// In en, this message translates to:
  /// **'Professional'**
  String get pdfProfessional;

  /// Security feature title
  ///
  /// In en, this message translates to:
  /// **'Security Encryption'**
  String get pdfSecurityEncryption;

  /// Password protection feature
  ///
  /// In en, this message translates to:
  /// **'Password Protection'**
  String get pdfPasswordProtection;

  /// Permission control feature
  ///
  /// In en, this message translates to:
  /// **'Permission Control'**
  String get pdfPermissionControl;

  /// Annotations feature title
  ///
  /// In en, this message translates to:
  /// **'Smart Annotations'**
  String get pdfSmartAnnotations;

  /// Highlight feature
  ///
  /// In en, this message translates to:
  /// **'Highlight'**
  String get pdfHighlight;

  /// Text annotations feature
  ///
  /// In en, this message translates to:
  /// **'Text Annotations'**
  String get pdfTextAnnotations;

  /// Search feature title
  ///
  /// In en, this message translates to:
  /// **'Fast Search'**
  String get pdfFastSearch;

  /// Full text search feature
  ///
  /// In en, this message translates to:
  /// **'Full Text Search'**
  String get pdfFullTextSearch;

  /// Precise location feature
  ///
  /// In en, this message translates to:
  /// **'Precise Location'**
  String get pdfPreciseLocation;

  /// Document merge feature title
  ///
  /// In en, this message translates to:
  /// **'Document Merge'**
  String get pdfDocumentMerge;

  /// Multi-file merge feature
  ///
  /// In en, this message translates to:
  /// **'Multi-file Merge'**
  String get pdfMultiFileMerge;

  /// Sharing feature title
  ///
  /// In en, this message translates to:
  /// **'Easy Sharing'**
  String get pdfEasySharing;

  /// One-click share feature
  ///
  /// In en, this message translates to:
  /// **'One-click Share'**
  String get pdfOneClickShare;

  /// Cloud sync feature title
  ///
  /// In en, this message translates to:
  /// **'Cloud Sync'**
  String get pdfCloudSync;

  /// Auto sync feature
  ///
  /// In en, this message translates to:
  /// **'Auto Sync'**
  String get pdfAutoSync;

  /// Quick start section
  ///
  /// In en, this message translates to:
  /// **'Quick Start'**
  String get pdfQuickStart;

  /// Import PDF button
  ///
  /// In en, this message translates to:
  /// **'Import PDF Document'**
  String get pdfImportDocument;

  /// View demo button
  ///
  /// In en, this message translates to:
  /// **'View Demo'**
  String get pdfViewDemo;

  /// Help button
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get pdfHelp;

  /// File support info
  ///
  /// In en, this message translates to:
  /// **'Supports .pdf format files, up to 100MB'**
  String get pdfSupportInfo;

  /// Modified date with parameter
  ///
  /// In en, this message translates to:
  /// **'Modified: {date}'**
  String pdfModified(Object date);

  /// Just now time format
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get pdfJustNow;

  /// Minutes ago format
  ///
  /// In en, this message translates to:
  /// **'{minutes} minutes ago'**
  String pdfMinutesAgo(Object minutes);

  /// Hours ago format
  ///
  /// In en, this message translates to:
  /// **'{hours} hours ago'**
  String pdfHoursAgo(Object hours);

  /// Days ago format
  ///
  /// In en, this message translates to:
  /// **'{days} days ago'**
  String pdfDaysAgo(Object days);

  /// Page count format
  ///
  /// In en, this message translates to:
  /// **'{count} pages'**
  String pdfPages(Object count);

  /// Security status
  ///
  /// In en, this message translates to:
  /// **'Security Status'**
  String get pdfSecurityStatus;

  /// Protected status
  ///
  /// In en, this message translates to:
  /// **'Protected'**
  String get pdfProtected;

  /// Restricted status
  ///
  /// In en, this message translates to:
  /// **'Restricted'**
  String get pdfRestricted;

  /// Unprotected status
  ///
  /// In en, this message translates to:
  /// **'Unprotected'**
  String get pdfUnprotected;

  /// Usage statistics section
  ///
  /// In en, this message translates to:
  /// **'Usage Statistics'**
  String get pdfUsageStats;

  /// Total documents stat
  ///
  /// In en, this message translates to:
  /// **'Total Documents'**
  String get pdfTotalDocuments;

  /// Today processed stat
  ///
  /// In en, this message translates to:
  /// **'Today Processed'**
  String get pdfTodayProcessed;

  /// Storage space stat
  ///
  /// In en, this message translates to:
  /// **'Storage Space'**
  String get pdfStorageSpace;

  /// Usage tips section
  ///
  /// In en, this message translates to:
  /// **'Usage Tips'**
  String get pdfTips;

  /// Long press tip
  ///
  /// In en, this message translates to:
  /// **'Long Press to Select'**
  String get pdfLongPressSelect;

  /// Long press description
  ///
  /// In en, this message translates to:
  /// **'Long press document cards to enter multi-select mode for batch operations'**
  String get pdfLongPressDesc;

  /// Security tip
  ///
  /// In en, this message translates to:
  /// **'Security Encryption'**
  String get pdfSecurityTip;

  /// Security tip description
  ///
  /// In en, this message translates to:
  /// **'Set password protection for important documents to ensure information security'**
  String get pdfSecurityTipDesc;

  /// Merge tip
  ///
  /// In en, this message translates to:
  /// **'Document Merge'**
  String get pdfMergeTip;

  /// Merge tip description
  ///
  /// In en, this message translates to:
  /// **'Select multiple PDF documents to merge into a single file with one click'**
  String get pdfMergeTipDesc;

  /// PDF viewer page title
  ///
  /// In en, this message translates to:
  /// **'PDF Viewer'**
  String get pdfViewerTitle;

  /// Search text field label
  ///
  /// In en, this message translates to:
  /// **'Search text'**
  String get pdfSearchText;

  /// Show annotations menu item
  ///
  /// In en, this message translates to:
  /// **'Show Annotations'**
  String get pdfShowAnnotations;

  /// Hide annotations menu item
  ///
  /// In en, this message translates to:
  /// **'Hide Annotations'**
  String get pdfHideAnnotations;

  /// Document info menu item
  ///
  /// In en, this message translates to:
  /// **'Document Info'**
  String get pdfDocumentInfo;

  /// Annotation details title
  ///
  /// In en, this message translates to:
  /// **'Annotation Details'**
  String get pdfAnnotationDetails;

  /// Annotation author label
  ///
  /// In en, this message translates to:
  /// **'Author: {author}'**
  String pdfAuthor(Object author);

  /// Annotation creation time
  ///
  /// In en, this message translates to:
  /// **'Created: {time}'**
  String pdfCreatedAt(Object datetime, Object time);

  /// Annotation content
  ///
  /// In en, this message translates to:
  /// **'Content: {content}'**
  String pdfContent(Object content);

  /// Highlighted text content
  ///
  /// In en, this message translates to:
  /// **'Highlighted text: {text}'**
  String pdfHighlightedText(Object text);

  /// File name label
  ///
  /// In en, this message translates to:
  /// **'File Name'**
  String get pdfFileName;

  /// File size label
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get pdfFileSize;

  /// Page count label
  ///
  /// In en, this message translates to:
  /// **'Pages'**
  String get pdfPageCount;

  /// Created date label
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get pdfCreatedDate;

  /// Modified date label
  ///
  /// In en, this message translates to:
  /// **'Modified'**
  String get pdfModifiedDate;

  /// Annotation count label
  ///
  /// In en, this message translates to:
  /// **'Annotations'**
  String get pdfAnnotationCount;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get pdfClose;

  /// PDF security page title
  ///
  /// In en, this message translates to:
  /// **'PDF Security Settings'**
  String get pdfSecurityTitle;

  /// Document info section title
  ///
  /// In en, this message translates to:
  /// **'Document Information'**
  String get pdfDocumentInfoSection;

  /// Document status
  ///
  /// In en, this message translates to:
  /// **'Status: {status}'**
  String pdfStatus(Object status);

  /// Decrypt PDF section title
  ///
  /// In en, this message translates to:
  /// **'Decrypt PDF'**
  String get pdfDecryptPdf;

  /// Encrypted PDF description
  ///
  /// In en, this message translates to:
  /// **'This PDF is encrypted, please enter password to decrypt'**
  String get pdfEncryptedDesc;

  /// Current password field
  ///
  /// In en, this message translates to:
  /// **'Current Password'**
  String get pdfCurrentPassword;

  /// Current password hint
  ///
  /// In en, this message translates to:
  /// **'Enter current password'**
  String get pdfCurrentPasswordHint;

  /// Decrypt button
  ///
  /// In en, this message translates to:
  /// **'Decrypt'**
  String get pdfDecryptButton;

  /// Encryption settings section
  ///
  /// In en, this message translates to:
  /// **'Encryption Settings'**
  String get pdfEncryptionSettings;

  /// User password field
  ///
  /// In en, this message translates to:
  /// **'User Password *'**
  String get pdfUserPassword;

  /// User password hint
  ///
  /// In en, this message translates to:
  /// **'Password to open PDF'**
  String get pdfUserPasswordHint;

  /// Owner password field
  ///
  /// In en, this message translates to:
  /// **'Owner Password (Optional)'**
  String get pdfOwnerPassword;

  /// Owner password hint
  ///
  /// In en, this message translates to:
  /// **'Password to modify permissions'**
  String get pdfOwnerPasswordHint;

  /// Permission settings section
  ///
  /// In en, this message translates to:
  /// **'Permission Settings'**
  String get pdfPermissionSettings;

  /// Allow print permission
  ///
  /// In en, this message translates to:
  /// **'Allow Printing'**
  String get pdfAllowPrint;

  /// Allow print permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to print PDF document'**
  String get pdfAllowPrintDesc;

  /// Allow copy permission
  ///
  /// In en, this message translates to:
  /// **'Allow Copying'**
  String get pdfAllowCopy;

  /// Allow copy permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to copy PDF content'**
  String get pdfAllowCopyDesc;

  /// Allow edit permission
  ///
  /// In en, this message translates to:
  /// **'Allow Editing'**
  String get pdfAllowEdit;

  /// Allow edit permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to edit PDF document'**
  String get pdfAllowEditDesc;

  /// Allow edit annotations permission
  ///
  /// In en, this message translates to:
  /// **'Allow Edit Annotations'**
  String get pdfAllowEditAnnotations;

  /// Allow edit annotations permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to add or edit annotations'**
  String get pdfAllowEditAnnotationsDesc;

  /// Allow fill forms permission
  ///
  /// In en, this message translates to:
  /// **'Allow Fill Forms'**
  String get pdfAllowFillForms;

  /// Allow fill forms permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to fill form fields'**
  String get pdfAllowFillFormsDesc;

  /// Allow extract pages permission
  ///
  /// In en, this message translates to:
  /// **'Allow Extract Pages'**
  String get pdfAllowExtractPages;

  /// Allow extract pages permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to extract page content'**
  String get pdfAllowExtractPagesDesc;

  /// Allow assemble document permission
  ///
  /// In en, this message translates to:
  /// **'Allow Assemble Document'**
  String get pdfAllowAssembleDocument;

  /// Allow assemble document permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to insert, delete, rotate pages'**
  String get pdfAllowAssembleDocumentDesc;

  /// Allow high quality print permission
  ///
  /// In en, this message translates to:
  /// **'Allow High Quality Print'**
  String get pdfAllowHighQualityPrint;

  /// Allow high quality print permission description
  ///
  /// In en, this message translates to:
  /// **'Allow users to print in high quality'**
  String get pdfAllowHighQualityPrintDesc;

  /// Preset permissions section
  ///
  /// In en, this message translates to:
  /// **'Preset Permissions'**
  String get pdfPresetPermissions;

  /// All permissions preset
  ///
  /// In en, this message translates to:
  /// **'All Permissions'**
  String get pdfAllPermissions;

  /// Basic permissions preset
  ///
  /// In en, this message translates to:
  /// **'Basic Permissions'**
  String get pdfBasicPermissions;

  /// Read only preset
  ///
  /// In en, this message translates to:
  /// **'Read Only'**
  String get pdfReadOnly;

  /// Set permissions only button
  ///
  /// In en, this message translates to:
  /// **'Set Permissions Only'**
  String get pdfSetPermissionsOnly;

  /// Encrypt and set permissions button
  ///
  /// In en, this message translates to:
  /// **'Encrypt and Set Permissions'**
  String get pdfEncryptAndSetPermissions;

  /// User password validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter user password'**
  String get pdfEnterUserPassword;

  /// PDF encryption success message
  ///
  /// In en, this message translates to:
  /// **'PDF encrypted successfully'**
  String get pdfEncryptSuccess;

  /// PDF encryption failed message
  ///
  /// In en, this message translates to:
  /// **'PDF encryption failed'**
  String get pdfEncryptFailed;

  /// Encryption failed with error
  ///
  /// In en, this message translates to:
  /// **'Encryption failed: {error}'**
  String pdfEncryptionFailed(Object error);

  /// Current password hint
  ///
  /// In en, this message translates to:
  /// **'Please enter current password'**
  String get pdfEnterCurrentPassword;

  /// PDF decryption success message
  ///
  /// In en, this message translates to:
  /// **'PDF decrypted successfully'**
  String get pdfDecryptSuccess;

  /// PDF decryption failed message
  ///
  /// In en, this message translates to:
  /// **'PDF decryption failed, please check password'**
  String get pdfDecryptFailed;

  /// Decryption failed with error
  ///
  /// In en, this message translates to:
  /// **'Decryption failed: {error}'**
  String pdfDecryptionFailed(Object error);

  /// Permissions set success message
  ///
  /// In en, this message translates to:
  /// **'Permissions set successfully'**
  String get pdfPermissionsSetSuccess;

  /// Permissions set failed message
  ///
  /// In en, this message translates to:
  /// **'Permissions set failed'**
  String get pdfPermissionsSetFailed;

  /// Set permissions failed with error
  ///
  /// In en, this message translates to:
  /// **'Settings failed: {error}'**
  String pdfSetPermissionsFailed(Object error);

  /// HTML manager screen title
  ///
  /// In en, this message translates to:
  /// **'HTML Management'**
  String get htmlManagerTitle;

  /// HTML manager description
  ///
  /// In en, this message translates to:
  /// **'Create and edit HTML documents, all content will be automatically saved to the content library for unified management'**
  String get htmlManagerDescription;

  /// Create new HTML button
  ///
  /// In en, this message translates to:
  /// **'Create New HTML'**
  String get htmlCreateNew;

  /// Import HTML file button
  ///
  /// In en, this message translates to:
  /// **'Import HTML File'**
  String get htmlImportFile;

  /// Importing HTML status
  ///
  /// In en, this message translates to:
  /// **'Importing...'**
  String get htmlImporting;

  /// Import success dialog title
  ///
  /// In en, this message translates to:
  /// **'Import Successful'**
  String get htmlImportSuccess;

  /// Import success message with count
  ///
  /// In en, this message translates to:
  /// **'Successfully imported {count} HTML files to content library'**
  String htmlImportSuccessMessage(Object count);

  /// Import failed error message
  ///
  /// In en, this message translates to:
  /// **'Failed to import HTML file: {error}'**
  String htmlImportFailed(Object error);

  /// Import progress message
  ///
  /// In en, this message translates to:
  /// **'Importing HTML files ({imported}/{total})'**
  String htmlImportingProgress(Object imported, Object total);

  /// View content library link
  ///
  /// In en, this message translates to:
  /// **'View Content Library'**
  String get htmlViewContentLibrary;

  /// HTML editor screen title
  ///
  /// In en, this message translates to:
  /// **'HTML Editor'**
  String get htmlEditorTitle;

  /// New HTML document default title
  ///
  /// In en, this message translates to:
  /// **'New HTML Document'**
  String get htmlNewDocument;

  /// Filename input dialog title
  ///
  /// In en, this message translates to:
  /// **'Enter HTML Filename'**
  String get htmlInputFilename;

  /// Filename input label
  ///
  /// In en, this message translates to:
  /// **'Filename'**
  String get htmlFilename;

  /// Filename input hint
  ///
  /// In en, this message translates to:
  /// **'Please enter HTML filename'**
  String get htmlFilenameHint;

  /// Filename empty error message
  ///
  /// In en, this message translates to:
  /// **'Filename cannot be empty'**
  String get htmlFilenameEmpty;

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get htmlCancel;

  /// Confirm button
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get htmlConfirm;

  /// Save success message
  ///
  /// In en, this message translates to:
  /// **'Save successful'**
  String get htmlSaveSuccess;

  /// Document not found error
  ///
  /// In en, this message translates to:
  /// **'Document not found'**
  String get htmlDocumentNotFound;

  /// Load document failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to load document: {error}'**
  String htmlLoadDocumentFailed(Object error);

  /// Create document failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to create document: {error}'**
  String htmlCreateDocumentFailed(Object error);

  /// Save document failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to save document: {error}'**
  String htmlSaveDocumentFailed(Object error);

  /// Import file failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to import HTML file: {error}'**
  String htmlImportFileFailed(Object error);

  /// Export image failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to export image: {error}'**
  String htmlExportImageFailed(Object error);

  /// Share HTML failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to share HTML: {error}'**
  String htmlShareHtmlFailed(Object error);

  /// Share image failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to share image: {error}'**
  String htmlShareImageFailed(Object error);

  /// Save to library failed error
  ///
  /// In en, this message translates to:
  /// **'Failed to save to content library: {error}'**
  String htmlSaveToLibraryFailed(Object error);

  /// Please save first message
  ///
  /// In en, this message translates to:
  /// **'Please save document first'**
  String get htmlPleaseSaveFirst;

  /// Select save location dialog title
  ///
  /// In en, this message translates to:
  /// **'Select save location'**
  String get htmlSelectSaveLocation;

  /// Export image success message
  ///
  /// In en, this message translates to:
  /// **'Export image successful: {path}'**
  String htmlExportImageSuccess(Object path);

  /// Saved to library message
  ///
  /// In en, this message translates to:
  /// **'Saved to content library: {title}'**
  String htmlSavedToLibrary(Object title);

  /// Processing status
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get htmlProcessing;

  /// Processing large text status
  ///
  /// In en, this message translates to:
  /// **'Processing large text...'**
  String get htmlProcessingLargeText;

  /// HTML input hint
  ///
  /// In en, this message translates to:
  /// **'Enter HTML code'**
  String get htmlInputHtmlCode;

  /// No HTML content message
  ///
  /// In en, this message translates to:
  /// **'No HTML content'**
  String get htmlNoContent;

  /// Please input HTML message
  ///
  /// In en, this message translates to:
  /// **'Please enter HTML code'**
  String get htmlPleaseInputHtml;

  /// Edit mode tooltip
  ///
  /// In en, this message translates to:
  /// **'Edit Mode'**
  String get htmlEditMode;

  /// Preview mode tooltip
  ///
  /// In en, this message translates to:
  /// **'Preview Mode'**
  String get htmlPreviewMode;

  /// Single screen mode tooltip
  ///
  /// In en, this message translates to:
  /// **'Single Screen Mode'**
  String get htmlSingleScreenMode;

  /// Split screen mode tooltip
  ///
  /// In en, this message translates to:
  /// **'Split Screen Mode'**
  String get htmlSplitScreenMode;

  /// More actions tooltip
  ///
  /// In en, this message translates to:
  /// **'More Actions'**
  String get htmlMoreActions;

  /// Import HTML file menu item
  ///
  /// In en, this message translates to:
  /// **'Import HTML File'**
  String get htmlImportHtmlFile;

  /// Export as image menu item
  ///
  /// In en, this message translates to:
  /// **'Export as Image'**
  String get htmlExportAsImage;

  /// Share as image menu item
  ///
  /// In en, this message translates to:
  /// **'Share as Image'**
  String get htmlShareAsImage;

  /// Share HTML menu item
  ///
  /// In en, this message translates to:
  /// **'Share HTML'**
  String get htmlShareHtml;

  /// Rename menu item
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get htmlRename;

  /// Save to library menu item
  ///
  /// In en, this message translates to:
  /// **'Save to Content Library'**
  String get htmlSaveToLibrary;

  /// Save to library button tooltip
  ///
  /// In en, this message translates to:
  /// **'Save to Content Library'**
  String get htmlSaveToLibraryTooltip;

  /// Save button tooltip
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get htmlSaveTooltip;

  /// Save to gallery button label
  ///
  /// In en, this message translates to:
  /// **'Save to Gallery'**
  String get htmlSaveToGallery;

  /// New HTML button tooltip
  ///
  /// In en, this message translates to:
  /// **'New HTML'**
  String get htmlNewHtmlTooltip;

  /// Import HTML button tooltip
  ///
  /// In en, this message translates to:
  /// **'Import HTML'**
  String get htmlImportHtmlTooltip;

  /// Language preference description
  ///
  /// In en, this message translates to:
  /// **'Select your preferred app language'**
  String get settingsLanguagePreference;

  /// Storage management title
  ///
  /// In en, this message translates to:
  /// **'Storage Management'**
  String get settingsStorageManagement;

  /// Help center title
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get settingsHelpCenter;

  /// Feedback title
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get settingsFeedback;

  /// Version info setting
  ///
  /// In en, this message translates to:
  /// **'Version Info'**
  String get settingsVersionInfo;

  /// Help and feedback setting
  ///
  /// In en, this message translates to:
  /// **'Help & Feedback'**
  String get settingsHelpAndFeedback;

  /// Help and feedback description
  ///
  /// In en, this message translates to:
  /// **'Get help or provide feedback'**
  String get settingsGetHelpOrProvideFeedback;

  /// Help and feedback dialog content
  ///
  /// In en, this message translates to:
  /// **'If you need help or have suggestions, please contact us through the feedback page.'**
  String get settingsHelpAndFeedbackContent;

  /// OK button
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get settingsOk;

  /// Theme selection dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Theme'**
  String get settingsSelectTheme;

  /// System theme mode
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get settingsSystemMode;

  /// Light theme mode
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get settingsLightMode;

  /// Dark theme mode
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get settingsDarkMode;

  /// Loading storage info message
  ///
  /// In en, this message translates to:
  /// **'Loading storage information'**
  String get storageLoadingStorageInfo;

  /// Storage info load error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load storage information: {error}'**
  String storageLoadStorageInfoFailed(Object error);

  /// Total storage usage label
  ///
  /// In en, this message translates to:
  /// **'Total Storage Usage'**
  String get storageTotalUsage;

  /// Storage details section title
  ///
  /// In en, this message translates to:
  /// **'Storage Details'**
  String get storageDetails;

  /// App data label
  ///
  /// In en, this message translates to:
  /// **'App Data'**
  String get storageAppData;

  /// Cache files label
  ///
  /// In en, this message translates to:
  /// **'Cache Files'**
  String get storageCacheFiles;

  /// Content data label
  ///
  /// In en, this message translates to:
  /// **'Content Data'**
  String get storageContentData;

  /// Voice files label
  ///
  /// In en, this message translates to:
  /// **'Voice Files'**
  String get storageVoiceFiles;

  /// Image files label
  ///
  /// In en, this message translates to:
  /// **'Image Files'**
  String get storageImageFiles;

  /// Settings data label
  ///
  /// In en, this message translates to:
  /// **'Settings Data'**
  String get storageSettingsData;

  /// Cleanup options section title
  ///
  /// In en, this message translates to:
  /// **'Cleanup Options'**
  String get storageCleanupOptions;

  /// Clear cache button
  ///
  /// In en, this message translates to:
  /// **'Clear Cache'**
  String get storageClearCache;

  /// Clear cache description
  ///
  /// In en, this message translates to:
  /// **'Delete temporary files and cache data'**
  String get storageClearCacheDesc;

  /// Clear temporary files button
  ///
  /// In en, this message translates to:
  /// **'Clear Temporary Files'**
  String get storageClearTempFiles;

  /// Clear temporary files description
  ///
  /// In en, this message translates to:
  /// **'Delete temporary files generated during processing'**
  String get storageClearTempFilesDesc;

  /// Data management section title
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get storageDataManagement;

  /// Export data button
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get storageExportData;

  /// Export data description
  ///
  /// In en, this message translates to:
  /// **'Export app data to files'**
  String get storageExportDataDesc;

  /// Import data button
  ///
  /// In en, this message translates to:
  /// **'Import Data'**
  String get storageImportData;

  /// Import data description
  ///
  /// In en, this message translates to:
  /// **'Import app data from files'**
  String get storageImportDataDesc;

  /// Reset app data button
  ///
  /// In en, this message translates to:
  /// **'Reset App Data'**
  String get storageResetAppData;

  /// Reset app data description
  ///
  /// In en, this message translates to:
  /// **'Clear all data and restore default settings'**
  String get storageResetAppDataDesc;

  /// Cache cleared success message
  ///
  /// In en, this message translates to:
  /// **'Cache cleared successfully'**
  String get storageCacheCleared;

  /// Cache clear error message
  ///
  /// In en, this message translates to:
  /// **'Failed to clear cache: {error}'**
  String storageClearCacheFailed(Object error);

  /// Temp files cleared success message
  ///
  /// In en, this message translates to:
  /// **'Temporary files cleared successfully'**
  String get storageTempFilesCleared;

  /// Temp files clear error message
  ///
  /// In en, this message translates to:
  /// **'Failed to clear temporary files: {error}'**
  String storageClearTempFilesFailed(Object error);

  /// Voice management development message
  ///
  /// In en, this message translates to:
  /// **'Voice file management feature is in development'**
  String get storageVoiceManagementInDevelopment;

  /// Image management development message
  ///
  /// In en, this message translates to:
  /// **'Image file management feature is in development'**
  String get storageImageManagementInDevelopment;

  /// Data export development message
  ///
  /// In en, this message translates to:
  /// **'Data export feature is in development'**
  String get storageDataExportInDevelopment;

  /// Data import development message
  ///
  /// In en, this message translates to:
  /// **'Data import feature is in development'**
  String get storageDataImportInDevelopment;

  /// Reset data dialog title
  ///
  /// In en, this message translates to:
  /// **'Reset App Data'**
  String get storageResetDataTitle;

  /// Reset data dialog message
  ///
  /// In en, this message translates to:
  /// **'This operation will delete all data and restore default settings. This cannot be undone. Are you sure you want to continue?'**
  String get storageResetDataMessage;

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get storageCancel;

  /// Confirm button
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get storageConfirm;

  /// Data reset success message
  ///
  /// In en, this message translates to:
  /// **'App data reset completed'**
  String get storageDataResetComplete;

  /// Data reset error message
  ///
  /// In en, this message translates to:
  /// **'Failed to reset app data: {error}'**
  String storageDataResetFailed(Object error);

  /// General settings section
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get iosSettingsGeneral;

  /// Language and region setting
  ///
  /// In en, this message translates to:
  /// **'Language & Region'**
  String get iosSettingsLanguageRegion;

  /// Display and brightness section
  ///
  /// In en, this message translates to:
  /// **'Display & Brightness'**
  String get iosSettingsDisplayBrightness;

  /// Appearance setting
  ///
  /// In en, this message translates to:
  /// **'Appearance'**
  String get iosSettingsAppearance;

  /// Privacy and security section
  ///
  /// In en, this message translates to:
  /// **'Privacy & Security'**
  String get iosSettingsPrivacySecurity;

  /// Privacy settings option
  ///
  /// In en, this message translates to:
  /// **'Privacy Settings'**
  String get iosSettingsPrivacySettings;

  /// Storage section
  ///
  /// In en, this message translates to:
  /// **'Storage'**
  String get iosSettingsStorage;

  /// Storage management option
  ///
  /// In en, this message translates to:
  /// **'Storage Management'**
  String get iosSettingsStorageManagement;

  /// Storage management description
  ///
  /// In en, this message translates to:
  /// **'View storage usage'**
  String get iosSettingsViewStorageUsage;

  /// Data import/export option
  ///
  /// In en, this message translates to:
  /// **'Data Import/Export'**
  String get iosSettingsDataImportExport;

  /// Data management description
  ///
  /// In en, this message translates to:
  /// **'Backup and restore data'**
  String get iosSettingsBackupRestoreData;

  /// Support section
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get iosSettingsSupport;

  /// Help center option
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get iosSettingsHelpCenter;

  /// Feedback option
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get iosSettingsFeedback;

  /// Rate app option
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get iosSettingsRateApp;

  /// About section
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get iosSettingsAbout;

  /// About app option
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get iosSettingsAboutApp;

  /// Current language display
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get iosSettingsCurrentLanguage;

  /// Light theme option
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get iosSettingsLightTheme;

  /// Dark theme option
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get iosSettingsDarkTheme;

  /// System theme option
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get iosSettingsSystemTheme;

  /// Language selection title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get iosSettingsSelectLanguage;

  /// Simplified Chinese option
  ///
  /// In en, this message translates to:
  /// **'简体中文'**
  String get iosSettingsSimplifiedChinese;

  /// Traditional Chinese option
  ///
  /// In en, this message translates to:
  /// **'繁體中文'**
  String get iosSettingsTraditionalChinese;

  /// English option
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get iosSettingsEnglish;

  /// Language selection message
  ///
  /// In en, this message translates to:
  /// **'Language selected: {language}'**
  String iosSettingsLanguageSelected(Object language);

  /// Appearance selection title
  ///
  /// In en, this message translates to:
  /// **'Select Appearance'**
  String get iosSettingsSelectAppearance;

  /// Privacy settings content
  ///
  /// In en, this message translates to:
  /// **'We value your privacy. All data processing is done locally and will not be uploaded to servers.'**
  String get iosSettingsPrivacyContent;

  /// Privacy settings management text
  ///
  /// In en, this message translates to:
  /// **'You can manage your data in settings at any time.'**
  String get iosSettingsPrivacyManage;

  /// Understand button
  ///
  /// In en, this message translates to:
  /// **'Understand'**
  String get iosSettingsUnderstand;

  /// Data management title
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get iosSettingsDataManagementTitle;

  /// Data management message
  ///
  /// In en, this message translates to:
  /// **'Select operation to perform'**
  String get iosSettingsSelectOperation;

  /// Export data option
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get iosSettingsExportData;

  /// Import data option
  ///
  /// In en, this message translates to:
  /// **'Import Data'**
  String get iosSettingsImportData;

  /// Create backup option
  ///
  /// In en, this message translates to:
  /// **'Create Backup'**
  String get iosSettingsCreateBackup;

  /// Rate app dialog title
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get iosSettingsRateAppTitle;

  /// Rate app dialog message
  ///
  /// In en, this message translates to:
  /// **'Do you like this app? Please rate us on the App Store!'**
  String get iosSettingsRateAppMessage;

  /// Later button
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get iosSettingsLater;

  /// Rate now button
  ///
  /// In en, this message translates to:
  /// **'Rate Now'**
  String get iosSettingsRateNow;

  /// App store error message
  ///
  /// In en, this message translates to:
  /// **'Cannot open App Store'**
  String get iosSettingsCannotOpenAppStore;

  /// App store error details
  ///
  /// In en, this message translates to:
  /// **'Error opening App Store'**
  String get iosSettingsAppStoreError;

  /// Version info format
  ///
  /// In en, this message translates to:
  /// **'Version: {version}'**
  String iosSettingsVersion(Object version);

  /// App description text
  ///
  /// In en, this message translates to:
  /// **'A powerful content management tool to help you create and manage various formats of content more efficiently.'**
  String get iosSettingsAppDescription;

  /// Copyright text
  ///
  /// In en, this message translates to:
  /// **'© 2023-2024 ContentPal Team'**
  String get iosSettingsCopyright;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get iosSettingsClose;

  /// OK button
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get iosSettingsOK;

  /// Error dialog title
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get iosSettingsError;

  /// Data export development message
  ///
  /// In en, this message translates to:
  /// **'Data export feature is in development...'**
  String get iosSettingsDataExportInDevelopment;

  /// Data import development message
  ///
  /// In en, this message translates to:
  /// **'Data import feature is in development...'**
  String get iosSettingsDataImportInDevelopment;

  /// Backup development message
  ///
  /// In en, this message translates to:
  /// **'Backup feature is in development...'**
  String get iosSettingsBackupInDevelopment;

  /// Help center title
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get helpCenterTitle;

  /// Need help title
  ///
  /// In en, this message translates to:
  /// **'Need Help?'**
  String get helpCenterNeedHelp;

  /// Help center description
  ///
  /// In en, this message translates to:
  /// **'Find answers to common questions or contact us for support'**
  String get helpCenterDescription;

  /// Contact support button
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get helpCenterContactSupport;

  /// User manual button
  ///
  /// In en, this message translates to:
  /// **'User Manual'**
  String get helpCenterUserManual;

  /// Getting started help item
  ///
  /// In en, this message translates to:
  /// **'Getting Started'**
  String get helpCenterGettingStarted;

  /// Getting started help content
  ///
  /// In en, this message translates to:
  /// **'Welcome to 内容君! You can select feature modules from the home page, such as text cards, Markdown editing, PDF processing, etc.'**
  String get helpCenterGettingStartedContent;

  /// Text cards help item
  ///
  /// In en, this message translates to:
  /// **'Text Cards'**
  String get helpCenterTextCards;

  /// Text cards help content
  ///
  /// In en, this message translates to:
  /// **'Text cards feature helps you convert text content into beautiful card images, supporting multiple templates and style customization.'**
  String get helpCenterTextCardsContent;

  /// Markdown editing help item
  ///
  /// In en, this message translates to:
  /// **'Markdown Editing'**
  String get helpCenterMarkdownEditing;

  /// Markdown editing help content
  ///
  /// In en, this message translates to:
  /// **'Markdown editor supports real-time preview, multiple themes, export to HTML/PDF and other features, making your document writing more efficient.'**
  String get helpCenterMarkdownEditingContent;

  /// Traffic guide generation help item
  ///
  /// In en, this message translates to:
  /// **'Traffic Guide Generation'**
  String get helpCenterTrafficGuideGeneration;

  /// Traffic guide generation help content
  ///
  /// In en, this message translates to:
  /// **'Traffic guide generation feature can create eye-catching marketing images, supporting anti-theft features like interference and watermarks.'**
  String get helpCenterTrafficGuideGenerationContent;

  /// Voice features help item
  ///
  /// In en, this message translates to:
  /// **'Voice Features'**
  String get helpCenterVoiceFeatures;

  /// Voice features help content
  ///
  /// In en, this message translates to:
  /// **'Voice features include recording, transcription, text-to-speech, etc., supporting multiple languages and high-quality voice processing.'**
  String get helpCenterVoiceFeaturesContent;

  /// PDF processing help item
  ///
  /// In en, this message translates to:
  /// **'PDF Processing'**
  String get helpCenterPDFProcessing;

  /// PDF processing help content
  ///
  /// In en, this message translates to:
  /// **'PDF processing features support viewing, annotation, security settings, etc., allowing you to better manage PDF documents.'**
  String get helpCenterPDFProcessingContent;

  /// Data sync backup help item
  ///
  /// In en, this message translates to:
  /// **'Data Sync & Backup'**
  String get helpCenterDataSyncBackup;

  /// Data sync backup help content
  ///
  /// In en, this message translates to:
  /// **'Your data is automatically saved locally. It is recommended to regularly use the export feature to backup important content.'**
  String get helpCenterDataSyncBackupContent;

  /// Privacy security help item
  ///
  /// In en, this message translates to:
  /// **'Privacy & Security'**
  String get helpCenterPrivacySecurity;

  /// Privacy security help content
  ///
  /// In en, this message translates to:
  /// **'We value your privacy, all data processing is done locally and will not be uploaded to servers.'**
  String get helpCenterPrivacySecurityContent;

  /// Search help dialog title
  ///
  /// In en, this message translates to:
  /// **'Search Help'**
  String get helpCenterSearchHelp;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Enter keywords to search...'**
  String get helpCenterSearchPlaceholder;

  /// Search cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get helpCenterSearchCancel;

  /// Search button
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get helpCenterSearch;

  /// Support email subject
  ///
  /// In en, this message translates to:
  /// **'ContentPal Support Request'**
  String get helpCenterSupportRequestSubject;

  /// Support email body
  ///
  /// In en, this message translates to:
  /// **'Please describe the issue you encountered...'**
  String get helpCenterSupportRequestBody;

  /// Email app error message
  ///
  /// In en, this message translates to:
  /// **'Cannot open email app'**
  String get helpCenterCannotOpenEmailApp;

  /// Email error message
  ///
  /// In en, this message translates to:
  /// **'Error sending email'**
  String get helpCenterEmailError;

  /// Manual open error message
  ///
  /// In en, this message translates to:
  /// **'Cannot open user manual'**
  String get helpCenterCannotOpenManual;

  /// Manual error message
  ///
  /// In en, this message translates to:
  /// **'Error opening user manual'**
  String get helpCenterManualError;

  /// Feedback screen title
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedbackTitle;

  /// Feedback screen subtitle
  ///
  /// In en, this message translates to:
  /// **'Your opinion matters'**
  String get feedbackSubtitle;

  /// Feedback screen description
  ///
  /// In en, this message translates to:
  /// **'Tell us your thoughts and help us improve the app'**
  String get feedbackDescription;

  /// Feedback type label
  ///
  /// In en, this message translates to:
  /// **'Feedback Type'**
  String get feedbackType;

  /// Feedback title label
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get feedbackTitleLabel;

  /// Feedback description label
  ///
  /// In en, this message translates to:
  /// **'Detailed Description'**
  String get feedbackDetailedDescription;

  /// Contact email label
  ///
  /// In en, this message translates to:
  /// **'Contact Email (Optional)'**
  String get feedbackContactEmail;

  /// System info option
  ///
  /// In en, this message translates to:
  /// **'Include System Info'**
  String get feedbackIncludeSystemInfo;

  /// System info description
  ///
  /// In en, this message translates to:
  /// **'Help us better diagnose issues'**
  String get feedbackIncludeSystemInfoDesc;

  /// Submit feedback button
  ///
  /// In en, this message translates to:
  /// **'Submit Feedback'**
  String get feedbackSubmit;

  /// Bug report type
  ///
  /// In en, this message translates to:
  /// **'Bug Report'**
  String get feedbackBugReport;

  /// Bug report description
  ///
  /// In en, this message translates to:
  /// **'Report errors or anomalies in the app'**
  String get feedbackBugReportDesc;

  /// Feature suggestion type
  ///
  /// In en, this message translates to:
  /// **'Feature Suggestion'**
  String get feedbackFeatureSuggestion;

  /// Feature suggestion description
  ///
  /// In en, this message translates to:
  /// **'Suggest new features or improvements'**
  String get feedbackFeatureSuggestionDesc;

  /// Complaint type
  ///
  /// In en, this message translates to:
  /// **'Complaint'**
  String get feedbackComplaint;

  /// Complaint description
  ///
  /// In en, this message translates to:
  /// **'Complaints or issues with the app'**
  String get feedbackComplaintDesc;

  /// Praise type
  ///
  /// In en, this message translates to:
  /// **'Praise'**
  String get feedbackPraise;

  /// Praise description
  ///
  /// In en, this message translates to:
  /// **'Praise or positive feedback for the app'**
  String get feedbackPraiseDesc;

  /// Title field hint
  ///
  /// In en, this message translates to:
  /// **'Please briefly describe your feedback'**
  String get feedbackTitleHint;

  /// Title required error
  ///
  /// In en, this message translates to:
  /// **'Please enter feedback title'**
  String get feedbackTitleRequired;

  /// Description field hint
  ///
  /// In en, this message translates to:
  /// **'Please describe your issue, suggestion, or thoughts in detail...'**
  String get feedbackDescriptionHint;

  /// Description required error
  ///
  /// In en, this message translates to:
  /// **'Please enter detailed description'**
  String get feedbackDescriptionRequired;

  /// Description too short error
  ///
  /// In en, this message translates to:
  /// **'Description must be at least 10 characters'**
  String get feedbackDescriptionTooShort;

  /// Email field hint
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get feedbackEmailHint;

  /// Email invalid error
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get feedbackEmailInvalid;

  /// Submitting text
  ///
  /// In en, this message translates to:
  /// **'Submitting...'**
  String get feedbackSubmitting;

  /// Submission error message
  ///
  /// In en, this message translates to:
  /// **'Error submitting feedback: {error}'**
  String feedbackSubmissionError(Object error);

  /// Success dialog title
  ///
  /// In en, this message translates to:
  /// **'Feedback Submitted Successfully'**
  String get feedbackSubmissionSuccessTitle;

  /// Success dialog message
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback! We will carefully consider your suggestions.'**
  String get feedbackSubmissionSuccessMessage;

  /// Confirm button
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get feedbackConfirm;

  /// System info error message
  ///
  /// In en, this message translates to:
  /// **'Failed to get system information'**
  String get feedbackSystemInfoFailed;

  /// App version format
  ///
  /// In en, this message translates to:
  /// **'App Version: {version}'**
  String feedbackAppVersion(Object version);

  /// Build number format
  ///
  /// In en, this message translates to:
  /// **'Build Number: {buildNumber}'**
  String feedbackBuildNumber(Object buildNumber);

  /// Device format
  ///
  /// In en, this message translates to:
  /// **'Device: {device}'**
  String feedbackDevice(Object device);

  /// System version format
  ///
  /// In en, this message translates to:
  /// **'System Version: {version}'**
  String feedbackSystemVersion(Object version);

  /// Device model format
  ///
  /// In en, this message translates to:
  /// **'Device Model: {model}'**
  String feedbackDeviceModel(Object model);

  /// Manufacturer format
  ///
  /// In en, this message translates to:
  /// **'Manufacturer: {manufacturer}'**
  String feedbackManufacturer(Object manufacturer);

  /// Feedback email subject format
  ///
  /// In en, this message translates to:
  /// **'内容君 - {feedbackType}'**
  String feedbackEmailSubject(Object feedbackType);

  /// Email app error message
  ///
  /// In en, this message translates to:
  /// **'Cannot open email app'**
  String get feedbackCannotOpenEmailApp;

  /// Content library feature card title
  ///
  /// In en, this message translates to:
  /// **'New Content Library Experience'**
  String get newContentLibraryExperience;

  /// Content library feature card subtitle
  ///
  /// In en, this message translates to:
  /// **'Support multiple content types, prioritize rendering results'**
  String get supportMultipleContentTypes;

  /// Content library demo page title
  ///
  /// In en, this message translates to:
  /// **'Content Library Demo'**
  String get contentLibraryDemoPage;

  /// Content service load item error message
  ///
  /// In en, this message translates to:
  /// **'Failed to load content item: {error}'**
  String contentServiceLoadItemFailed(Object error);

  /// Content service text type validation error
  ///
  /// In en, this message translates to:
  /// **'Must be text type content'**
  String get contentServiceMustBeTextType;

  /// Content service image type validation error
  ///
  /// In en, this message translates to:
  /// **'Must be image type content'**
  String get contentServiceMustBeImageType;

  /// Content service item not found error
  ///
  /// In en, this message translates to:
  /// **'Content item not found'**
  String get contentServiceItemNotFound;

  /// Content service not initialized error
  ///
  /// In en, this message translates to:
  /// **'ContentService not initialized'**
  String get contentServiceNotInitialized;

  /// Content service render failed error
  ///
  /// In en, this message translates to:
  /// **'Render failed: {error}'**
  String contentServiceRenderFailed(Object error);

  /// Permission helper storage permission request failed error
  ///
  /// In en, this message translates to:
  /// **'Request storage permission failed: {error}'**
  String permissionHelperRequestStorageFailed(Object error);

  /// Permission helper camera permission request failed error
  ///
  /// In en, this message translates to:
  /// **'Request camera permission failed: {error}'**
  String permissionHelperRequestCameraFailed(Object error);

  /// Permission helper multiple permissions request failed error
  ///
  /// In en, this message translates to:
  /// **'Request multiple permissions failed: {error}'**
  String permissionHelperRequestMultipleFailed(Object error);

  /// Permission helper iOS permission check failed error
  ///
  /// In en, this message translates to:
  /// **'iOS permission check failed: {error}'**
  String permissionHelperIosPermissionCheckFailed(Object error);

  /// Chinese traditional color theme selector title
  ///
  /// In en, this message translates to:
  /// **'Traditional Chinese Colors'**
  String get chineseTraditionalColorTitle;

  /// Chinese traditional color theme selector subtitle
  ///
  /// In en, this message translates to:
  /// **'Choose your favorite traditional color theme'**
  String get chineseTraditionalColorSubtitle;

  /// System theme option in Chinese traditional color selector
  ///
  /// In en, this message translates to:
  /// **'Follow System Theme'**
  String get chineseTraditionalColorSystemTheme;

  /// System theme description in Chinese traditional color selector
  ///
  /// In en, this message translates to:
  /// **'Use app default theme colors'**
  String get chineseTraditionalColorSystemThemeDesc;

  /// Message when switching to a specific Chinese traditional color theme
  ///
  /// In en, this message translates to:
  /// **'Switched to \"{themeName}\" theme'**
  String chineseTraditionalColorSwitchedToTheme(Object themeName);

  /// Message when switching to system default theme
  ///
  /// In en, this message translates to:
  /// **'Switched to system default theme'**
  String get chineseTraditionalColorSwitchedToSystem;

  /// Subscription management page title
  ///
  /// In en, this message translates to:
  /// **'Subscription Management'**
  String get subscriptionManagement;

  /// Upgrade subscription button text
  ///
  /// In en, this message translates to:
  /// **'Upgrade Subscription'**
  String get upgradeSubscription;

  /// Upgrade subscription description
  ///
  /// In en, this message translates to:
  /// **'View and purchase higher-tier subscription plans'**
  String get upgradeSubscriptionDesc;

  /// Restore purchase button text
  ///
  /// In en, this message translates to:
  /// **'Restore Purchase'**
  String get restorePurchase;

  /// Restore purchase description
  ///
  /// In en, this message translates to:
  /// **'Restore your previous subscription purchases'**
  String get restorePurchaseDesc;

  /// Help and support section title
  ///
  /// In en, this message translates to:
  /// **'Help and Support'**
  String get helpAndSupport;

  /// FAQ button text
  ///
  /// In en, this message translates to:
  /// **'FAQ'**
  String get frequentlyAskedQuestions;

  /// FAQ description
  ///
  /// In en, this message translates to:
  /// **'View frequently asked questions about subscriptions'**
  String get frequentlyAskedQuestionsDesc;

  /// Contact customer service button text
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get contactCustomerService;

  /// Contact customer service description
  ///
  /// In en, this message translates to:
  /// **'Get help with subscription issues'**
  String get contactCustomerServiceDesc;

  /// Refund policy button text
  ///
  /// In en, this message translates to:
  /// **'Refund Policy'**
  String get refundPolicy;

  /// Refund policy description
  ///
  /// In en, this message translates to:
  /// **'Learn about our refund and cancellation policies'**
  String get refundPolicyDesc;

  /// Title shown when restoring purchases
  ///
  /// In en, this message translates to:
  /// **'Restoring Purchase'**
  String get restoringPurchase;

  /// Message shown while communicating with App Store during restore
  ///
  /// In en, this message translates to:
  /// **'Communicating with App Store...'**
  String get communicatingWithAppStore;

  /// Message when no purchases can be restored
  ///
  /// In en, this message translates to:
  /// **'No restorable purchases found'**
  String get noRestorablePurchasesFound;

  /// Current subscription label
  ///
  /// In en, this message translates to:
  /// **'Current Subscription'**
  String get currentSubscription;

  /// Free version subscription name
  ///
  /// In en, this message translates to:
  /// **'Free Version'**
  String get freeVersion;

  /// Available features section title
  ///
  /// In en, this message translates to:
  /// **'Available Features'**
  String get availableFeatures;

  /// Basic processing feature name
  ///
  /// In en, this message translates to:
  /// **'Basic Processing'**
  String get basicProcessing;

  /// Export with watermark feature name
  ///
  /// In en, this message translates to:
  /// **'Export with Watermark'**
  String get exportWithWatermark;

  /// Unlimited export feature name
  ///
  /// In en, this message translates to:
  /// **'Unlimited Export'**
  String get unlimitedExport;

  /// Batch processing feature name
  ///
  /// In en, this message translates to:
  /// **'Batch Processing'**
  String get batchProcessing;

  /// Advanced tools feature name
  ///
  /// In en, this message translates to:
  /// **'Advanced Tools'**
  String get advancedTools;

  /// Title for saved markdown content
  ///
  /// In en, this message translates to:
  /// **'Saved Markdown'**
  String get markdownSavedMarkdown;

  /// Message shown when no cards are available
  ///
  /// In en, this message translates to:
  /// **'No cards yet'**
  String get textCardNoCardsYet;

  /// Title field label in card editor
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get textCardTitle;

  /// Content field label in card editor
  ///
  /// In en, this message translates to:
  /// **'Content'**
  String get textCardContent;

  /// Permission request message for photo album access
  ///
  /// In en, this message translates to:
  /// **'Photo album permission is required to save images, please enable permission in settings'**
  String get textCardNeedPhotoPermission;

  /// Error message when screenshot capture fails
  ///
  /// In en, this message translates to:
  /// **'Screenshot failed, please try again'**
  String get textCardScreenshotFailed;

  /// Error message when permission is denied
  ///
  /// In en, this message translates to:
  /// **'Permission denied, please enable photo album permission in settings'**
  String get textCardPermissionDenied;

  /// Title for exporting a single card
  ///
  /// In en, this message translates to:
  /// **'Export Card'**
  String get textCardExportCard;

  /// Title for exporting a document
  ///
  /// In en, this message translates to:
  /// **'Export Document'**
  String get textCardExportDocument;

  /// Preview section title
  ///
  /// In en, this message translates to:
  /// **'Preview'**
  String get textCardPreview;

  /// Message shown during export
  ///
  /// In en, this message translates to:
  /// **'Exporting...'**
  String get textCardExporting;

  /// Button to start export
  ///
  /// In en, this message translates to:
  /// **'Start Export'**
  String get textCardStartExport;

  /// Export size section title
  ///
  /// In en, this message translates to:
  /// **'Export Size'**
  String get textCardExportSize;

  /// Target platform section title
  ///
  /// In en, this message translates to:
  /// **'Target Platform'**
  String get textCardTargetPlatform;

  /// File format section title
  ///
  /// In en, this message translates to:
  /// **'File Format'**
  String get textCardFileFormat;

  /// Watermark settings section title
  ///
  /// In en, this message translates to:
  /// **'Watermark Settings'**
  String get textCardWatermarkSettings;

  /// Subtitle for watermark switch
  ///
  /// In en, this message translates to:
  /// **'Add app watermark to image corner'**
  String get textCardAddWatermarkSubtitle;

  /// Label for custom watermark text field
  ///
  /// In en, this message translates to:
  /// **'Custom Watermark Text'**
  String get textCardCustomWatermarkText;

  /// Hint for custom watermark text field
  ///
  /// In en, this message translates to:
  /// **'Leave empty to use default watermark'**
  String get textCardCustomWatermarkHint;

  /// Advanced options section title
  ///
  /// In en, this message translates to:
  /// **'Advanced Options'**
  String get textCardAdvancedOptions;

  /// Subtitle for title switch
  ///
  /// In en, this message translates to:
  /// **'Show title in exported image'**
  String get textCardIncludeTitleSubtitle;

  /// Subtitle for timestamp switch
  ///
  /// In en, this message translates to:
  /// **'Show creation time in image'**
  String get textCardIncludeTimestampSubtitle;

  /// More items label
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get textCardMore;

  /// Part label for card sections
  ///
  /// In en, this message translates to:
  /// **'Part'**
  String get textCardPart;

  /// Section label for card sections
  ///
  /// In en, this message translates to:
  /// **'Section'**
  String get textCardSection;

  /// Instructions for text selection
  ///
  /// In en, this message translates to:
  /// **'Select any range of text below to apply styles to the selected content'**
  String get textCardSelectionInstructions;

  /// Export options section title
  ///
  /// In en, this message translates to:
  /// **'Export Options'**
  String get textCardExportOptions;

  /// Save to gallery button text
  ///
  /// In en, this message translates to:
  /// **'Save to Gallery'**
  String get textCardSaveToGallery;

  /// Single card export message
  ///
  /// In en, this message translates to:
  /// **'Will export 1 card image'**
  String get textCardExportSingleCard;

  /// Multiple cards export prefix
  ///
  /// In en, this message translates to:
  /// **'Will export'**
  String get textCardExportMultipleCards;

  /// Card images suffix
  ///
  /// In en, this message translates to:
  /// **'card images'**
  String get textCardImages;

  /// Card label
  ///
  /// In en, this message translates to:
  /// **'Card'**
  String get textCardCard;

  /// Card number suffix (empty in English)
  ///
  /// In en, this message translates to:
  /// **''**
  String get textCardCardNumber;

  /// Share message prefix
  ///
  /// In en, this message translates to:
  /// **'From 内容君'**
  String get textCardShareFromApp;

  /// Share failed message
  ///
  /// In en, this message translates to:
  /// **'Share failed'**
  String get textCardShareFailed;

  /// Saved to gallery prefix
  ///
  /// In en, this message translates to:
  /// **'Saved'**
  String get textCardSavedToGallery;

  /// Cards to gallery suffix
  ///
  /// In en, this message translates to:
  /// **'cards to gallery'**
  String get textCardCardsToGallery;

  /// Create document title
  ///
  /// In en, this message translates to:
  /// **'Create Document'**
  String get textCardCreateDocument;

  /// Text edit mode label
  ///
  /// In en, this message translates to:
  /// **'Text Edit Mode'**
  String get textCardTextEditMode;

  /// Preview mode label
  ///
  /// In en, this message translates to:
  /// **'Preview Mode'**
  String get textCardPreviewMode;

  /// Total cards prefix
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get textCardTotalCards;

  /// Cards suffix
  ///
  /// In en, this message translates to:
  /// **'cards'**
  String get textCardCards;

  /// Use separator prefix
  ///
  /// In en, this message translates to:
  /// **'Use'**
  String get textCardUseSeparator;

  /// Separator hint suffix
  ///
  /// In en, this message translates to:
  /// **'to separate cards'**
  String get textCardSeparatorHint;

  /// Document title label
  ///
  /// In en, this message translates to:
  /// **'Document Title'**
  String get textCardDocumentTitle;

  /// Document title hint
  ///
  /// In en, this message translates to:
  /// **'Give this set of cards a title...'**
  String get textCardDocumentTitleHint;

  /// Document content hint
  ///
  /// In en, this message translates to:
  /// **'Enter or paste long text...\n\n💡 Tips:\n• Click \"Insert Separator\" where you want to split\n• First line will be auto-recognized as title if it looks like one\n• Click \"Preview\" in top right to see split effect'**
  String get textCardDocumentContentHint;

  /// Instruction to go back to edit mode
  ///
  /// In en, this message translates to:
  /// **'Go back to edit mode and add separators to create cards'**
  String get textCardGoBackToEditMode;

  /// List view tooltip
  ///
  /// In en, this message translates to:
  /// **'List View'**
  String get textCardListView;

  /// Grid view tooltip
  ///
  /// In en, this message translates to:
  /// **'Grid View'**
  String get textCardGridView;

  /// Template label
  ///
  /// In en, this message translates to:
  /// **'Template'**
  String get textCardTemplate;

  /// No cards in document message
  ///
  /// In en, this message translates to:
  /// **'No cards in document'**
  String get textCardNoCardsInDocument;

  /// Edit document to add cards hint
  ///
  /// In en, this message translates to:
  /// **'Edit document to add cards'**
  String get textCardEditDocumentToAddCards;

  /// Days ago suffix
  ///
  /// In en, this message translates to:
  /// **'days ago'**
  String get textCardDaysAgo;

  /// Hours ago suffix
  ///
  /// In en, this message translates to:
  /// **'hours ago'**
  String get textCardHoursAgo;

  /// Minutes ago suffix
  ///
  /// In en, this message translates to:
  /// **'minutes ago'**
  String get textCardMinutesAgo;

  /// Just now label
  ///
  /// In en, this message translates to:
  /// **'just now'**
  String get textCardJustNow;

  /// Pure text custom rendering title
  ///
  /// In en, this message translates to:
  /// **'Pure Text Custom Rendering'**
  String get textCardPureTextCustomRendering;

  /// Render content to cards subtitle
  ///
  /// In en, this message translates to:
  /// **'Render your content into beautiful cards'**
  String get textCardRenderContentToCards;

  /// Design description
  ///
  /// In en, this message translates to:
  /// **'This is a separated design: simple editor for content editing and splitting, powerful visual renderer for style customization and final display.'**
  String get textCardDesignDescription;

  /// Simple edit feature title
  ///
  /// In en, this message translates to:
  /// **'Simple Edit'**
  String get textCardSimpleEdit;

  /// Simple edit feature description
  ///
  /// In en, this message translates to:
  /// **'Focus on pure text editing and content splitting, no complex format interference'**
  String get textCardSimpleEditDesc;

  /// Visual rendering feature title
  ///
  /// In en, this message translates to:
  /// **'Visual Rendering'**
  String get textCardVisualRendering;

  /// Visual rendering feature description
  ///
  /// In en, this message translates to:
  /// **'WYSIWYG style customization, select text to directly modify styles'**
  String get textCardVisualRenderingDesc;

  /// Smart recognition feature title
  ///
  /// In en, this message translates to:
  /// **'Smart Recognition'**
  String get textCardSmartRecognition;

  /// Smart recognition feature description
  ///
  /// In en, this message translates to:
  /// **'Automatically recognize content types like headings, lists, quotes and render beautifully'**
  String get textCardSmartRecognitionDesc;

  /// Export share feature title
  ///
  /// In en, this message translates to:
  /// **'Export & Share'**
  String get textCardExportShare;

  /// Export share feature description
  ///
  /// In en, this message translates to:
  /// **'Support single card and batch export, easily share beautiful content'**
  String get textCardExportShareDesc;

  /// Core features section title
  ///
  /// In en, this message translates to:
  /// **'Core Features'**
  String get textCardCoreFeatures;

  /// Markdown tip
  ///
  /// In en, this message translates to:
  /// **'Tip: Supports Markdown format text input, including headings, lists, quotes, etc.'**
  String get textCardMarkdownTip;

  /// Start creating button text
  ///
  /// In en, this message translates to:
  /// **'Start Creating'**
  String get textCardStartCreating;

  /// Edit content hint text
  ///
  /// In en, this message translates to:
  /// **'Click to edit content'**
  String get textCardClickToEditContent;

  /// Light template category
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get textCardsLightCategory;

  /// Dark template category
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get textCardsDarkCategory;

  /// Nature template category
  ///
  /// In en, this message translates to:
  /// **'Nature'**
  String get textCardsNatureCategory;

  /// Warm template category
  ///
  /// In en, this message translates to:
  /// **'Warm'**
  String get textCardsWarmCategory;

  /// Tech template category
  ///
  /// In en, this message translates to:
  /// **'Tech'**
  String get textCardsTechCategory;

  /// Elegant template category
  ///
  /// In en, this message translates to:
  /// **'Elegant'**
  String get textCardsElegantCategory;

  /// Vintage template category
  ///
  /// In en, this message translates to:
  /// **'Vintage'**
  String get textCardsVintageCategory;

  /// Preview effect title
  ///
  /// In en, this message translates to:
  /// **'Preview Effect'**
  String get textCardsPreviewEffect;

  /// Create beautiful card button
  ///
  /// In en, this message translates to:
  /// **'Create Beautiful Card'**
  String get textCardCreateBeautifulCard;

  /// Tip for selecting text to modify style
  ///
  /// In en, this message translates to:
  /// **'Select text to modify font, color and size'**
  String get textCardsSelectTextToModifyStyle;

  /// Select template label
  ///
  /// In en, this message translates to:
  /// **'Select Template'**
  String get textCardsSelectTemplate;

  /// Template gallery title
  ///
  /// In en, this message translates to:
  /// **'Template Gallery'**
  String get textCardsTemplateGallery;

  /// No description provided for @blockMarkdown.
  ///
  /// In en, this message translates to:
  /// **'Block Markdown'**
  String get blockMarkdown;

  /// No description provided for @cardCollection.
  ///
  /// In en, this message translates to:
  /// **'Card Collection'**
  String get cardCollection;

  /// No description provided for @contentDefaultsTitle.
  ///
  /// In en, this message translates to:
  /// **'Default Sample Content'**
  String get contentDefaultsTitle;

  /// No description provided for @markdownDefaultSampleTitle.
  ///
  /// In en, this message translates to:
  /// **'Markdown Default Sample'**
  String get markdownDefaultSampleTitle;

  /// No description provided for @markdownDefaultSampleDesc.
  ///
  /// In en, this message translates to:
  /// **'Auto-fill example content in Markdown'**
  String get markdownDefaultSampleDesc;

  /// No description provided for @textCardsDefaultSampleTitle.
  ///
  /// In en, this message translates to:
  /// **'Text Cards Default Sample'**
  String get textCardsDefaultSampleTitle;

  /// No description provided for @textCardsDefaultSampleDesc.
  ///
  /// In en, this message translates to:
  /// **'Auto-fill example content in Text Cards'**
  String get textCardsDefaultSampleDesc;

  /// No description provided for @svgBuiltInPresetTitle.
  ///
  /// In en, this message translates to:
  /// **'SVG Built-in Preset'**
  String get svgBuiltInPresetTitle;

  /// No description provided for @svgBuiltInPresetDesc.
  ///
  /// In en, this message translates to:
  /// **'Show built-in SVG preset when no document exists'**
  String get svgBuiltInPresetDesc;

  /// No description provided for @htmlDefaultSampleTitle.
  ///
  /// In en, this message translates to:
  /// **'HTML Default Sample'**
  String get htmlDefaultSampleTitle;

  /// No description provided for @htmlDefaultSampleDesc.
  ///
  /// In en, this message translates to:
  /// **'Load built-in HTML sample when no document exists'**
  String get htmlDefaultSampleDesc;

  /// No description provided for @transformerExampleInputTitle.
  ///
  /// In en, this message translates to:
  /// **'Transformer Example Input'**
  String get transformerExampleInputTitle;

  /// No description provided for @transformerExampleInputDesc.
  ///
  /// In en, this message translates to:
  /// **'Auto-fill example input in Text Transformer'**
  String get transformerExampleInputDesc;

  /// No description provided for @privacyPolicyTitle.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicyTitle;

  /// No description provided for @privacyPolicySubtitle.
  ///
  /// In en, this message translates to:
  /// **'Read our privacy policy'**
  String get privacyPolicySubtitle;

  /// No description provided for @openSourceLicensesTitle.
  ///
  /// In en, this message translates to:
  /// **'Open Source Licenses'**
  String get openSourceLicensesTitle;

  /// No description provided for @openSourceLicensesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'View third‑party licenses'**
  String get openSourceLicensesSubtitle;

  /// No description provided for @subscriptionUpgradeTitle.
  ///
  /// In en, this message translates to:
  /// **'Upgrade to Premium'**
  String get subscriptionUpgradeTitle;

  /// No description provided for @subscriptionUpgradeSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Unlock all premium features and enhance your AI experience'**
  String get subscriptionUpgradeSubtitle;

  /// No description provided for @subscriptionChoosePlan.
  ///
  /// In en, this message translates to:
  /// **'Choose your subscription plan'**
  String get subscriptionChoosePlan;

  /// No description provided for @subscriptionDiscountSavePercent.
  ///
  /// In en, this message translates to:
  /// **'Save {percent}%'**
  String subscriptionDiscountSavePercent(Object percent);

  /// No description provided for @subscriptionLoadingPrice.
  ///
  /// In en, this message translates to:
  /// **'Loading price…'**
  String get subscriptionLoadingPrice;

  /// No description provided for @subscriptionEquivalentToPerMonth.
  ///
  /// In en, this message translates to:
  /// **'Equivalent to {price}'**
  String subscriptionEquivalentToPerMonth(Object price);

  /// No description provided for @subscriptionIncludedFeatures.
  ///
  /// In en, this message translates to:
  /// **'Included features'**
  String get subscriptionIncludedFeatures;

  /// No description provided for @subscriptionSubscribeNowWithPrice.
  ///
  /// In en, this message translates to:
  /// **'Subscribe Now {price}'**
  String subscriptionSubscribeNowWithPrice(Object price);

  /// No description provided for @subscriptionAgreementPrefix.
  ///
  /// In en, this message translates to:
  /// **'By subscribing, you agree to our '**
  String get subscriptionAgreementPrefix;

  /// No description provided for @termsOfService.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @subscriptionAgreementSuffix.
  ///
  /// In en, this message translates to:
  /// **'. Subscriptions auto‑renew and can be cancelled anytime.'**
  String get subscriptionAgreementSuffix;

  /// No description provided for @subscriptionDevModeNotice.
  ///
  /// In en, this message translates to:
  /// **'Development mode: Subscription uses mock data for now; real purchases will be enabled after App Store Connect approval'**
  String get subscriptionDevModeNotice;

  /// No description provided for @diagnosticsTitle.
  ///
  /// In en, this message translates to:
  /// **'Diagnostics'**
  String get diagnosticsTitle;

  /// No description provided for @diagnosticsClose.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get diagnosticsClose;

  /// No description provided for @subscriptionDiagnosticsButton.
  ///
  /// In en, this message translates to:
  /// **'Diagnose Purchase Service'**
  String get subscriptionDiagnosticsButton;

  /// No description provided for @restoreCompletedTitle.
  ///
  /// In en, this message translates to:
  /// **'Restore Completed'**
  String get restoreCompletedTitle;

  /// No description provided for @restoreFailedTitle.
  ///
  /// In en, this message translates to:
  /// **'Restore Failed'**
  String get restoreFailedTitle;

  /// No description provided for @restoreCompletedMessage.
  ///
  /// In en, this message translates to:
  /// **'Your purchases have been successfully restored.'**
  String get restoreCompletedMessage;

  /// No description provided for @restoreFailedMessageWithError.
  ///
  /// In en, this message translates to:
  /// **'An error occurred while restoring: {error}'**
  String restoreFailedMessageWithError(Object error);

  /// No description provided for @andText.
  ///
  /// In en, this message translates to:
  /// **'and'**
  String get andText;

  /// No description provided for @subscriptionPlanMonthlyName.
  ///
  /// In en, this message translates to:
  /// **'ContentPal Premium (Monthly)'**
  String get subscriptionPlanMonthlyName;

  /// No description provided for @subscriptionPlanMonthlyDesc.
  ///
  /// In en, this message translates to:
  /// **'Unlock all advanced content processing features'**
  String get subscriptionPlanMonthlyDesc;

  /// No description provided for @subscriptionPlanYearlyName.
  ///
  /// In en, this message translates to:
  /// **'ContentPal Premium (Yearly)'**
  String get subscriptionPlanYearlyName;

  /// No description provided for @subscriptionPlanYearlyDesc.
  ///
  /// In en, this message translates to:
  /// **'Annual subscription, More economical!'**
  String get subscriptionPlanYearlyDesc;

  /// No description provided for @subscriptionPlanLifetimeName.
  ///
  /// In en, this message translates to:
  /// **'ContentPal Premium (Lifetime)'**
  String get subscriptionPlanLifetimeName;

  /// No description provided for @subscriptionPlanLifetimeDesc.
  ///
  /// In en, this message translates to:
  /// **'One-time purchase, lifetime access'**
  String get subscriptionPlanLifetimeDesc;

  /// No description provided for @subscriptionPlanFreeName.
  ///
  /// In en, this message translates to:
  /// **'Free'**
  String get subscriptionPlanFreeName;

  /// No description provided for @subscriptionPlanFreeDesc.
  ///
  /// In en, this message translates to:
  /// **'Basic content processing features'**
  String get subscriptionPlanFreeDesc;

  /// No description provided for @subscriptionFeatureUnlimitedExportsName.
  ///
  /// In en, this message translates to:
  /// **'Unlimited exports'**
  String get subscriptionFeatureUnlimitedExportsName;

  /// No description provided for @subscriptionFeatureUnlimitedExportsDesc.
  ///
  /// In en, this message translates to:
  /// **'Export processed content without limits'**
  String get subscriptionFeatureUnlimitedExportsDesc;

  /// No description provided for @subscriptionFeatureBatchProcessingName.
  ///
  /// In en, this message translates to:
  /// **'Batch processing'**
  String get subscriptionFeatureBatchProcessingName;

  /// No description provided for @subscriptionFeatureBatchProcessingDesc.
  ///
  /// In en, this message translates to:
  /// **'Process multiple files at once'**
  String get subscriptionFeatureBatchProcessingDesc;

  /// No description provided for @subscriptionFeatureAdvancedToolsName.
  ///
  /// In en, this message translates to:
  /// **'Advanced tools'**
  String get subscriptionFeatureAdvancedToolsName;

  /// No description provided for @subscriptionFeatureAdvancedToolsDesc.
  ///
  /// In en, this message translates to:
  /// **'Access all advanced editing and processing tools'**
  String get subscriptionFeatureAdvancedToolsDesc;

  /// No description provided for @subscriptionFeatureNoWatermarkName.
  ///
  /// In en, this message translates to:
  /// **'No watermark'**
  String get subscriptionFeatureNoWatermarkName;

  /// No description provided for @subscriptionFeatureNoWatermarkDesc.
  ///
  /// In en, this message translates to:
  /// **'Exports do not include watermarks'**
  String get subscriptionFeatureNoWatermarkDesc;

  /// No description provided for @subscriptionFeaturePrioritySupportName.
  ///
  /// In en, this message translates to:
  /// **'Priority support'**
  String get subscriptionFeaturePrioritySupportName;

  /// No description provided for @subscriptionFeaturePrioritySupportDesc.
  ///
  /// In en, this message translates to:
  /// **'Get priority customer support'**
  String get subscriptionFeaturePrioritySupportDesc;

  /// No description provided for @subscriptionFeatureFutureUpdatesName.
  ///
  /// In en, this message translates to:
  /// **'Future updates'**
  String get subscriptionFeatureFutureUpdatesName;

  /// No description provided for @subscriptionFeatureFutureUpdatesDesc.
  ///
  /// In en, this message translates to:
  /// **'Receive all future feature updates'**
  String get subscriptionFeatureFutureUpdatesDesc;

  /// No description provided for @subscriptionFeatureBasicProcessingName.
  ///
  /// In en, this message translates to:
  /// **'Basic processing'**
  String get subscriptionFeatureBasicProcessingName;

  /// No description provided for @subscriptionFeatureBasicProcessingDesc.
  ///
  /// In en, this message translates to:
  /// **'Basic content processing features'**
  String get subscriptionFeatureBasicProcessingDesc;

  /// No description provided for @subscriptionFeatureWatermarkedExportsName.
  ///
  /// In en, this message translates to:
  /// **'Watermarked exports'**
  String get subscriptionFeatureWatermarkedExportsName;

  /// No description provided for @subscriptionFeatureWatermarkedExportsDesc.
  ///
  /// In en, this message translates to:
  /// **'Exports include watermark'**
  String get subscriptionFeatureWatermarkedExportsDesc;

  /// No description provided for @subscriptionPeriodPerMonthSuffix.
  ///
  /// In en, this message translates to:
  /// **'/month'**
  String get subscriptionPeriodPerMonthSuffix;

  /// No description provided for @subscriptionPeriodPerYearSuffix.
  ///
  /// In en, this message translates to:
  /// **'/year'**
  String get subscriptionPeriodPerYearSuffix;

  /// No description provided for @subscriptionPeriodLifetime.
  ///
  /// In en, this message translates to:
  /// **'Lifetime'**
  String get subscriptionPeriodLifetime;

  /// No description provided for @subscriptionPeriodFree.
  ///
  /// In en, this message translates to:
  /// **'Free'**
  String get subscriptionPeriodFree;

  /// Message shown when subscription purchase is successful
  ///
  /// In en, this message translates to:
  /// **'Purchase successful. Your subscription is now active. Thank you!'**
  String get purchaseSuccessMessage;

  /// Text shown when user is viewing their current subscription plan
  ///
  /// In en, this message translates to:
  /// **'Current Plan'**
  String get currentPlan;

  /// Status text for expired subscription
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get expired;

  /// Text showing days until subscription expires
  ///
  /// In en, this message translates to:
  /// **'{days} days until expiry'**
  String daysUntilExpiry(int days);

  /// Text shown when subscription has expired
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get subscriptionExpired;

  /// Browse templates button
  ///
  /// In en, this message translates to:
  /// **'Browse Templates'**
  String get textCardsBrowseTemplates;

  /// Beautiful templates text
  ///
  /// In en, this message translates to:
  /// **'beautiful templates'**
  String get textCardsBeautifulTemplates;

  /// All categories filter
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get textCardsAllCategories;

  /// Business category name
  ///
  /// In en, this message translates to:
  /// **'Business'**
  String get textCardsBusinessCategory;

  /// Academic category name
  ///
  /// In en, this message translates to:
  /// **'Academic'**
  String get textCardsAcademicCategory;

  /// Creative category name
  ///
  /// In en, this message translates to:
  /// **'Creative'**
  String get textCardsCreativeCategory;

  /// Minimal category name
  ///
  /// In en, this message translates to:
  /// **'Minimal'**
  String get textCardsMinimalCategory;

  /// Modern category name
  ///
  /// In en, this message translates to:
  /// **'Modern'**
  String get textCardsModernCategory;

  /// Export settings title
  ///
  /// In en, this message translates to:
  /// **'Export Settings'**
  String get textCardExportSettings;

  /// Image size setting
  ///
  /// In en, this message translates to:
  /// **'Image Size'**
  String get textCardImageSize;

  /// Image ratio setting
  ///
  /// In en, this message translates to:
  /// **'Image Ratio'**
  String get textCardImageRatio;

  /// Smart text splitter title
  ///
  /// In en, this message translates to:
  /// **'Smart Text Splitter'**
  String get smartTextSplitter;

  /// Smart text splitter subtitle
  ///
  /// In en, this message translates to:
  /// **'Split long text into multiple cards intelligently'**
  String get smartTextSplitterSubtitle;

  /// Character count format
  ///
  /// In en, this message translates to:
  /// **'Character count: {count}'**
  String characterCount(int count);

  /// Split configuration title
  ///
  /// In en, this message translates to:
  /// **'Split Configuration'**
  String get splitConfig;

  /// Split configuration description
  ///
  /// In en, this message translates to:
  /// **'Choose split mode and related parameters'**
  String get splitConfigDescription;

  /// Split mode section title
  ///
  /// In en, this message translates to:
  /// **'Split Mode'**
  String get splitMode;

  /// Custom separator field
  ///
  /// In en, this message translates to:
  /// **'Custom Separator'**
  String get customSeparator;

  /// Custom separator hint
  ///
  /// In en, this message translates to:
  /// **'Enter separator, such as: ---'**
  String get customSeparatorHint;

  /// Advanced options section title
  ///
  /// In en, this message translates to:
  /// **'Advanced Options'**
  String get advancedOptions;

  /// Auto-detect titles option
  ///
  /// In en, this message translates to:
  /// **'Auto-detect Titles'**
  String get autoDetectTitles;

  /// Auto-detect titles description
  ///
  /// In en, this message translates to:
  /// **'Intelligently identify title content in text'**
  String get autoDetectTitlesDescription;

  /// Preserve formatting option
  ///
  /// In en, this message translates to:
  /// **'Preserve Formatting'**
  String get preserveFormatting;

  /// Preserve formatting description
  ///
  /// In en, this message translates to:
  /// **'Preserve original text Markdown formatting'**
  String get preserveFormattingDescription;

  /// Smart merge option
  ///
  /// In en, this message translates to:
  /// **'Smart Merge'**
  String get smartMerge;

  /// Smart merge description
  ///
  /// In en, this message translates to:
  /// **'Automatically merge short paragraphs'**
  String get smartMergeDescription;

  /// Max length setting
  ///
  /// In en, this message translates to:
  /// **'Max Length'**
  String get maxLength;

  /// Max length description
  ///
  /// In en, this message translates to:
  /// **'Maximum characters per card'**
  String get maxLengthDescription;

  /// Split preview title
  ///
  /// In en, this message translates to:
  /// **'Split Preview'**
  String get splitPreview;

  /// Total cards count format
  ///
  /// In en, this message translates to:
  /// **'Total {count} cards'**
  String totalCards(int count);

  /// Split preview description
  ///
  /// In en, this message translates to:
  /// **'Preview split results, can edit, merge or delete cards'**
  String get splitPreviewDescription;

  /// Deselect all button
  ///
  /// In en, this message translates to:
  /// **'Deselect All'**
  String get deselectAll;

  /// Delete selected button
  ///
  /// In en, this message translates to:
  /// **'Delete Selected'**
  String get deleteSelected;

  /// No split results message
  ///
  /// In en, this message translates to:
  /// **'No Split Results'**
  String get noSplitResults;

  /// No split results description
  ///
  /// In en, this message translates to:
  /// **'Please return to previous step to check input text and configuration'**
  String get noSplitResultsDescription;

  /// Previous step button
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previousStep;

  /// Next step button
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextStep;

  /// Start splitting button
  ///
  /// In en, this message translates to:
  /// **'Start Splitting'**
  String get startSplitting;

  /// Create cards button
  ///
  /// In en, this message translates to:
  /// **'Create Cards'**
  String get createCards;

  /// Input text hint
  ///
  /// In en, this message translates to:
  /// **'Enter or paste text content here...'**
  String get inputTextHint;

  /// Title optional field
  ///
  /// In en, this message translates to:
  /// **'Title (Optional)'**
  String get titleOptional;

  /// Export settings title
  ///
  /// In en, this message translates to:
  /// **'Export Settings'**
  String get exportSettings;

  /// Confirm export button
  ///
  /// In en, this message translates to:
  /// **'Confirm Export'**
  String get confirmExport;

  /// Preview info section
  ///
  /// In en, this message translates to:
  /// **'Preview Info'**
  String get previewInfo;

  /// Dimensions label
  ///
  /// In en, this message translates to:
  /// **'Dimensions'**
  String get dimensions;

  /// Ratio label
  ///
  /// In en, this message translates to:
  /// **'Ratio'**
  String get ratio;

  /// Quality percentage label
  ///
  /// In en, this message translates to:
  /// **'Quality'**
  String get qualityPercent;

  /// Watermark status label
  ///
  /// In en, this message translates to:
  /// **'Watermark'**
  String get watermarkStatus;

  /// Include status
  ///
  /// In en, this message translates to:
  /// **'Include'**
  String get include;

  /// Not include status
  ///
  /// In en, this message translates to:
  /// **'Not Include'**
  String get notInclude;

  /// Pixels unit
  ///
  /// In en, this message translates to:
  /// **'pixels'**
  String get pixels;

  /// Created time label
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get pdfCreatedTime;

  /// Modified time label
  ///
  /// In en, this message translates to:
  /// **'Modified'**
  String get pdfModifiedTime;

  /// Permissions set success message
  ///
  /// In en, this message translates to:
  /// **'Permissions set successfully'**
  String get pdfPermissionsSuccess;

  /// Permissions set failed message
  ///
  /// In en, this message translates to:
  /// **'Permissions set failed'**
  String get pdfPermissionsFailed;

  /// Settings failed with error
  ///
  /// In en, this message translates to:
  /// **'Settings failed: {error}'**
  String pdfSettingsFailed(Object error);

  /// Document information section title
  ///
  /// In en, this message translates to:
  /// **'Document Information'**
  String get pdfDocumentInformation;

  /// PDF encrypted message
  ///
  /// In en, this message translates to:
  /// **'This PDF is encrypted, please enter password to decrypt'**
  String get pdfEncryptedMessage;

  /// Encrypted security status
  ///
  /// In en, this message translates to:
  /// **'Encrypted'**
  String get pdfSecurityEncrypted;

  /// Read only security status
  ///
  /// In en, this message translates to:
  /// **'Read Only'**
  String get pdfSecurityReadOnly;

  /// Restricted security status
  ///
  /// In en, this message translates to:
  /// **'Restricted'**
  String get pdfSecurityRestricted;

  /// Open security status
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get pdfSecurityOpen;

  /// Allow copying permission
  ///
  /// In en, this message translates to:
  /// **'Allow Copying'**
  String get pdfAllowCopying;

  /// Multiple formats feature
  ///
  /// In en, this message translates to:
  /// **'Multiple Formats'**
  String get pdfMultipleFormats;

  /// Multi-device sync feature
  ///
  /// In en, this message translates to:
  /// **'Multi-device Sync'**
  String get pdfMultiDeviceSync;

  /// Document protection subtitle
  ///
  /// In en, this message translates to:
  /// **'Protect your important documents'**
  String get pdfProtectYourDocuments;

  /// Password protection description
  ///
  /// In en, this message translates to:
  /// **'Set user and owner passwords for PDF documents to ensure document security'**
  String get pdfPasswordProtectionDesc;

  /// Permission control description
  ///
  /// In en, this message translates to:
  /// **'Fine-grained control over document printing, copying, editing and other permissions'**
  String get pdfPermissionControlDesc;

  /// Encryption algorithm feature
  ///
  /// In en, this message translates to:
  /// **'Encryption Algorithm'**
  String get pdfEncryptionAlgorithm;

  /// Encryption algorithm description
  ///
  /// In en, this message translates to:
  /// **'Adopt industry-standard AES encryption algorithm to ensure document security'**
  String get pdfEncryptionAlgorithmDesc;

  /// Import PDF to start button
  ///
  /// In en, this message translates to:
  /// **'Import PDF to Start'**
  String get pdfImportToStart;

  /// Usage tips section
  ///
  /// In en, this message translates to:
  /// **'Usage Tips'**
  String get pdfUsageTips;

  /// Default HTML document title
  ///
  /// In en, this message translates to:
  /// **'Untitled HTML'**
  String get htmlUntitled;

  /// New HTML document title
  ///
  /// In en, this message translates to:
  /// **'New HTML Document'**
  String get htmlNewDocumentTitle;

  /// Copy suffix for document title
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get htmlCopy;

  /// Error message when HTML cannot be rendered
  ///
  /// In en, this message translates to:
  /// **'Cannot render HTML content'**
  String get htmlRenderError;

  /// Voice home page title
  ///
  /// In en, this message translates to:
  /// **'Voice Assistant'**
  String get voiceHomeTitle;

  /// Voice home page subtitle
  ///
  /// In en, this message translates to:
  /// **'Record ideas, convert to text, smart reading'**
  String get voiceHomeSubtitle;

  /// Usage statistics section title
  ///
  /// In en, this message translates to:
  /// **'Usage Statistics'**
  String get voiceUsageStats;

  /// Recording count label
  ///
  /// In en, this message translates to:
  /// **'Recording Count'**
  String get voiceRecordingCount;

  /// Recordings unit
  ///
  /// In en, this message translates to:
  /// **'recordings'**
  String get voiceRecordingsUnit;

  /// Total duration label
  ///
  /// In en, this message translates to:
  /// **'Total Duration'**
  String get voiceTotalDuration;

  /// Cumulative duration label
  ///
  /// In en, this message translates to:
  /// **'Cumulative Duration'**
  String get voiceCumulativeDuration;

  /// Quick actions section title
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get voiceQuickActions;

  /// Record new voice subtitle
  ///
  /// In en, this message translates to:
  /// **'Record new voice'**
  String get voiceRecordNewVoice;

  /// Text to speech button
  ///
  /// In en, this message translates to:
  /// **'Text to Speech'**
  String get voiceTextToSpeech;

  /// Convert text to voice subtitle
  ///
  /// In en, this message translates to:
  /// **'Convert text to voice'**
  String get voiceConvertTextToVoice;

  /// Powerful features section title
  ///
  /// In en, this message translates to:
  /// **'Powerful Features'**
  String get voicePowerfulFeatures;

  /// Smart transcription feature title
  ///
  /// In en, this message translates to:
  /// **'Smart Transcription'**
  String get voiceSmartTranscription;

  /// Smart transcription feature description
  ///
  /// In en, this message translates to:
  /// **'Automatically convert voice to text'**
  String get voiceSmartTranscriptionDesc;

  /// Audio adjustment feature title
  ///
  /// In en, this message translates to:
  /// **'Audio Adjustment'**
  String get voiceAudioAdjustment;

  /// Audio adjustment feature description
  ///
  /// In en, this message translates to:
  /// **'Adjust speed, pitch and volume'**
  String get voiceAudioAdjustmentDesc;

  /// Playlist feature title
  ///
  /// In en, this message translates to:
  /// **'Playlist'**
  String get voicePlaylist;

  /// Playlist feature description
  ///
  /// In en, this message translates to:
  /// **'Manage and play multiple audio files'**
  String get voicePlaylistDesc;

  /// Cloud sync feature title
  ///
  /// In en, this message translates to:
  /// **'Cloud Sync'**
  String get voiceCloudSync;

  /// Cloud sync feature description
  ///
  /// In en, this message translates to:
  /// **'Sync your recordings across devices'**
  String get voiceCloudSyncDesc;

  /// Recent recordings section title
  ///
  /// In en, this message translates to:
  /// **'Recent Recordings'**
  String get voiceRecentRecordings;

  /// View all button
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get voiceViewAll;

  /// No recordings yet message
  ///
  /// In en, this message translates to:
  /// **'No recordings yet'**
  String get voiceNoRecordingsYet;

  /// Start first recording instruction
  ///
  /// In en, this message translates to:
  /// **'Click the button below to start your first recording'**
  String get voiceStartFirstRecording;

  /// Voice record detail page title
  ///
  /// In en, this message translates to:
  /// **'Voice Details'**
  String get voiceRecordDetailTitle;

  /// Save all changes tooltip
  ///
  /// In en, this message translates to:
  /// **'Save all changes'**
  String get voiceSaveAllChanges;

  /// Recording file corrupted message
  ///
  /// In en, this message translates to:
  /// **'Recording file may be corrupted, cannot play'**
  String get voiceRecordingFileMayBeCorrupted;

  /// Audio file not exist or corrupted message
  ///
  /// In en, this message translates to:
  /// **'Audio file does not exist or is corrupted'**
  String get voiceAudioFileNotExistOrCorrupted;

  /// File path label
  ///
  /// In en, this message translates to:
  /// **'File path: {path}'**
  String voiceFilePath(Object path);

  /// Recheck button
  ///
  /// In en, this message translates to:
  /// **'Recheck'**
  String get voiceRecheck;

  /// File status rechecked message
  ///
  /// In en, this message translates to:
  /// **'File status rechecked: {status}{corrupted}'**
  String voiceFileStatusRechecked(Object corrupted, Object status);

  /// File exists status
  ///
  /// In en, this message translates to:
  /// **'file exists'**
  String get voiceFileExists;

  /// File not exists status
  ///
  /// In en, this message translates to:
  /// **'file does not exist'**
  String get voiceFileNotExists;

  /// File corrupted suffix
  ///
  /// In en, this message translates to:
  /// **', but file may be corrupted'**
  String get voiceFileButCorrupted;

  /// Voice transcription section title
  ///
  /// In en, this message translates to:
  /// **'Voice Transcription'**
  String get voiceVoiceTranscription;

  /// Read text button
  ///
  /// In en, this message translates to:
  /// **'Read Text'**
  String get voiceReadText;

  /// No transcription text hint
  ///
  /// In en, this message translates to:
  /// **'No transcription text yet'**
  String get voiceNoTranscriptionText;

  /// Save transcription text button
  ///
  /// In en, this message translates to:
  /// **'Save Transcription Text'**
  String get voiceSaveTranscriptionText;

  /// Create time label
  ///
  /// In en, this message translates to:
  /// **'Create Time: {time}'**
  String voiceCreateTime(Object time);

  /// Playing status
  ///
  /// In en, this message translates to:
  /// **'Playing...'**
  String get voicePlaying;

  /// Paused status
  ///
  /// In en, this message translates to:
  /// **'Paused'**
  String get voicePaused;

  /// Audio file not exist message
  ///
  /// In en, this message translates to:
  /// **'Audio file does not exist, cannot play'**
  String get voiceAudioFileNotExist;

  /// Audio duration abnormal message
  ///
  /// In en, this message translates to:
  /// **'Audio duration abnormal, may not play properly'**
  String get voiceAudioDurationAbnormal;

  /// Load audio failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to load audio: {error}'**
  String voiceLoadAudioFailed(Object error);

  /// Play failed message
  ///
  /// In en, this message translates to:
  /// **'Play failed: {error}'**
  String voicePlayFailed(Object error);

  /// Title saved message
  ///
  /// In en, this message translates to:
  /// **'Title saved'**
  String get voiceTitleSaved;

  /// Transcription text saved message
  ///
  /// In en, this message translates to:
  /// **'Transcription text saved'**
  String get voiceTranscriptionTextSaved;

  /// TTS player page title
  ///
  /// In en, this message translates to:
  /// **'Text to Speech'**
  String get voiceTtsPlayerTitle;

  /// Input text to read label
  ///
  /// In en, this message translates to:
  /// **'Input text to read'**
  String get voiceInputTextToRead;

  /// Input text hint
  ///
  /// In en, this message translates to:
  /// **'Input text to read, click \"Add\" button to add to playlist'**
  String get voiceInputTextHint;

  /// Add to playlist tooltip
  ///
  /// In en, this message translates to:
  /// **'Add to playlist'**
  String get voiceAddToPlaylist;

  /// Added to playlist message
  ///
  /// In en, this message translates to:
  /// **'Added to playlist'**
  String get voiceAddedToPlaylist;

  /// TTS settings section title
  ///
  /// In en, this message translates to:
  /// **'TTS Settings'**
  String get voiceTtsSettings;

  /// Speech rate label
  ///
  /// In en, this message translates to:
  /// **'Speech Rate:'**
  String get voiceSpeechRate;

  /// Pitch label
  ///
  /// In en, this message translates to:
  /// **'Pitch:'**
  String get voicePitch;

  /// Volume label
  ///
  /// In en, this message translates to:
  /// **'Volume:'**
  String get voiceVolume;

  /// Language label
  ///
  /// In en, this message translates to:
  /// **'Language:'**
  String get voiceLanguage;

  /// Playlist section title
  ///
  /// In en, this message translates to:
  /// **'Playlist'**
  String get voicePlaylistTitle;

  /// Stop button
  ///
  /// In en, this message translates to:
  /// **'Stop'**
  String get voiceStop;

  /// Playlist empty message
  ///
  /// In en, this message translates to:
  /// **'Playlist is empty'**
  String get voicePlaylistEmpty;

  /// Transcription page title
  ///
  /// In en, this message translates to:
  /// **'Smart Transcription'**
  String get voiceTranscriptionTitle;

  /// Realtime transcription mode
  ///
  /// In en, this message translates to:
  /// **'Realtime Transcription'**
  String get voiceRealtimeTranscription;

  /// File transcription mode
  ///
  /// In en, this message translates to:
  /// **'File Transcription'**
  String get voiceFileTranscription;

  /// Batch transcription mode
  ///
  /// In en, this message translates to:
  /// **'Batch Transcription'**
  String get voiceBatchTranscription;

  /// Select audio file instruction
  ///
  /// In en, this message translates to:
  /// **'Select audio file for transcription'**
  String get voiceSelectAudioFile;

  /// File selected status
  ///
  /// In en, this message translates to:
  /// **'File Selected'**
  String get voiceFileSelected;

  /// Select file button
  ///
  /// In en, this message translates to:
  /// **'Select File'**
  String get voiceSelectFile;

  /// Reselect file button
  ///
  /// In en, this message translates to:
  /// **'Reselect File'**
  String get voiceReselectFile;

  /// Transcription result section title
  ///
  /// In en, this message translates to:
  /// **'Transcription Result'**
  String get voiceTranscriptionResult;

  /// Batch transcription feature title
  ///
  /// In en, this message translates to:
  /// **'Batch Transcription Feature'**
  String get voiceBatchTranscriptionFeature;

  /// Select multiple files instruction
  ///
  /// In en, this message translates to:
  /// **'Select multiple files for batch transcription'**
  String get voiceSelectMultipleFiles;

  /// Select multiple files button
  ///
  /// In en, this message translates to:
  /// **'Select Multiple Files'**
  String get voiceSelectMultipleFilesBtn;

  /// Batch processing progress section title
  ///
  /// In en, this message translates to:
  /// **'Batch Processing Progress'**
  String get voiceBatchProcessingProgress;

  /// Batch transcription in development message
  ///
  /// In en, this message translates to:
  /// **'Batch transcription feature in development...'**
  String get voiceBatchTranscriptionInDev;

  /// Ready to start status
  ///
  /// In en, this message translates to:
  /// **'Ready to start'**
  String get voiceReadyToStart;

  /// Transcribing status
  ///
  /// In en, this message translates to:
  /// **'Transcribing...'**
  String get voiceTranscribing;

  /// Click to start realtime transcription instruction
  ///
  /// In en, this message translates to:
  /// **'Click button below to start realtime transcription'**
  String get voiceClickToStartRealtime;

  /// Export button
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get voiceExport;

  /// Share button
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get voiceShare;

  /// No transcription content to export message
  ///
  /// In en, this message translates to:
  /// **'No transcription content to export'**
  String get voiceNoTranscriptionContent;

  /// No transcription content to share message
  ///
  /// In en, this message translates to:
  /// **'No transcription content to share'**
  String get voiceNoTranscriptionContentToShare;

  /// Export feature in development message
  ///
  /// In en, this message translates to:
  /// **'Export feature in development...'**
  String get voiceExportFeatureInDev;

  /// Share feature in development message
  ///
  /// In en, this message translates to:
  /// **'Share feature in development...'**
  String get voiceShareFeatureInDev;

  /// Transcription settings dialog title
  ///
  /// In en, this message translates to:
  /// **'Transcription Settings'**
  String get voiceTranscriptionSettings;

  /// Enable punctuation setting
  ///
  /// In en, this message translates to:
  /// **'Enable Punctuation'**
  String get voiceEnablePunctuation;

  /// Auto add punctuation description
  ///
  /// In en, this message translates to:
  /// **'Automatically add punctuation'**
  String get voiceAutoAddPunctuation;

  /// Speaker detection setting
  ///
  /// In en, this message translates to:
  /// **'Speaker Detection'**
  String get voiceSpeakerDetection;

  /// Detect different speakers description
  ///
  /// In en, this message translates to:
  /// **'Detect different speakers'**
  String get voiceDetectDifferentSpeakers;

  /// Confidence threshold setting
  ///
  /// In en, this message translates to:
  /// **'Confidence Threshold'**
  String get voiceConfidenceThreshold;

  /// Permission required message
  ///
  /// In en, this message translates to:
  /// **'Voice transcription feature requires microphone permission. Please enable microphone access in settings.'**
  String get voicePermissionRequiredMessage;

  /// Recording complete message
  ///
  /// In en, this message translates to:
  /// **'Recording complete'**
  String get voiceRecordingComplete;

  /// Recording failed retry message
  ///
  /// In en, this message translates to:
  /// **'Recording failed, please retry'**
  String get voiceRecordingFailedRetry;

  /// Select file failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to select file: {error}'**
  String voiceSelectFileFailed(Object error);

  /// Process audio file failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to process audio file: {error}'**
  String voiceProcessAudioFileFailed(Object error);

  /// Select batch files failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to select batch files: {error}'**
  String voiceSelectBatchFilesFailed(Object error);

  /// Files selected message
  ///
  /// In en, this message translates to:
  /// **'Selected {count} files'**
  String voiceFilesSelected(Object count);

  /// No transcription content to save message
  ///
  /// In en, this message translates to:
  /// **'No transcription content to save'**
  String get voiceNoTranscriptionContentToSave;

  /// Save transcription result dialog title
  ///
  /// In en, this message translates to:
  /// **'Save Transcription Result'**
  String get voiceSaveTranscriptionResult;

  /// Transcription content preview label
  ///
  /// In en, this message translates to:
  /// **'Transcription content preview:'**
  String get voiceTranscriptionContentPreview;

  /// Transcription result saved message
  ///
  /// In en, this message translates to:
  /// **'Transcription result saved'**
  String get voiceTranscriptionResultSaved;

  /// Save failed message
  ///
  /// In en, this message translates to:
  /// **'Save failed: {error}'**
  String voiceSaveFailed(Object error);

  /// Transcription failed retry message
  ///
  /// In en, this message translates to:
  /// **'Transcription failed, please retry'**
  String get voiceTranscriptionFailedRetry;

  /// Smart transcription page title
  ///
  /// In en, this message translates to:
  /// **'Smart Transcription'**
  String get voiceSmartTranscriptionPageTitle;

  /// Initialization failed check permission message
  ///
  /// In en, this message translates to:
  /// **'Initialization failed, please check microphone permission'**
  String get voiceInitializationFailedCheckPermission;

  /// Initialization exception message
  ///
  /// In en, this message translates to:
  /// **'Initialization exception: {error}'**
  String voiceInitializationException(Object error);

  /// Service initialization failed message
  ///
  /// In en, this message translates to:
  /// **'Service initialization failed'**
  String get voiceServiceInitializationFailed;

  /// Start transcription failed message
  ///
  /// In en, this message translates to:
  /// **'Failed to start transcription'**
  String get voiceStartTranscriptionFailed;

  /// Transcription idle status
  ///
  /// In en, this message translates to:
  /// **'Ready'**
  String get voiceTranscriptionIdle;

  /// Transcription in progress status
  ///
  /// In en, this message translates to:
  /// **'Transcribing...'**
  String get voiceTranscriptionInProgress;

  /// Transcription completed status
  ///
  /// In en, this message translates to:
  /// **'Transcription Completed'**
  String get voiceTranscriptionCompleted;

  /// Transcription error status
  ///
  /// In en, this message translates to:
  /// **'Transcription Error'**
  String get voiceTranscriptionError;

  /// Language selector label
  ///
  /// In en, this message translates to:
  /// **'Language:'**
  String get voiceLanguageSelector;

  /// Transcription result title
  ///
  /// In en, this message translates to:
  /// **'Transcription Result'**
  String get voiceTranscriptionResultTitle;

  /// Click to start transcription instruction
  ///
  /// In en, this message translates to:
  /// **'Click start button to begin transcription...'**
  String get voiceClickToStartTranscription;

  /// Stop transcription button
  ///
  /// In en, this message translates to:
  /// **'Stop Transcription'**
  String get voiceStopTranscription;

  /// Clear button
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get voiceClear;

  /// iOS permission test page title
  ///
  /// In en, this message translates to:
  /// **'iOS Permission Test'**
  String get voiceIosPermissionTestTitle;

  /// Direct iOS permission test title
  ///
  /// In en, this message translates to:
  /// **'Direct iOS Permission Test'**
  String get voiceDirectIosPermissionTest;

  /// Page loaded message
  ///
  /// In en, this message translates to:
  /// **'Page loaded'**
  String get voicePageLoaded;

  /// Initial microphone permission status message
  ///
  /// In en, this message translates to:
  /// **'Initial microphone permission status: {status}'**
  String voiceInitialMicPermissionStatus(Object status);

  /// Initial speech recognition permission status message
  ///
  /// In en, this message translates to:
  /// **'Initial speech recognition permission status: {status}'**
  String voiceInitialSpeechPermissionStatus(Object status);

  /// Non-iOS platform message
  ///
  /// In en, this message translates to:
  /// **'Non-iOS platform, not checking permissions'**
  String get voiceNonIosPlatform;

  /// Request microphone permission button
  ///
  /// In en, this message translates to:
  /// **'Request Microphone Permission'**
  String get voiceRequestMicPermission;

  /// Microphone permission status message
  ///
  /// In en, this message translates to:
  /// **'Microphone permission status: {status}'**
  String voiceMicPermissionStatus(Object status);

  /// Request speech recognition permission button
  ///
  /// In en, this message translates to:
  /// **'Request Speech Recognition Permission'**
  String get voiceRequestSpeechPermission;

  /// Speech recognition permission status message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition permission status: {status}'**
  String voiceSpeechPermissionStatus(Object status);

  /// Test recording button
  ///
  /// In en, this message translates to:
  /// **'Test Recording'**
  String get voiceTestRecording;

  /// Test recording function message
  ///
  /// In en, this message translates to:
  /// **'Test recording function...'**
  String get voiceTestRecordingFunction;

  /// Recorder instance created message
  ///
  /// In en, this message translates to:
  /// **'Recorder instance created'**
  String get voiceRecorderInstanceCreated;

  /// Recorder initialized message
  ///
  /// In en, this message translates to:
  /// **'Recorder initialized'**
  String get voiceRecorderInitialized;

  /// Recorder test complete message
  ///
  /// In en, this message translates to:
  /// **'Recorder test complete'**
  String get voiceRecorderTestComplete;

  /// Recorder closed message
  ///
  /// In en, this message translates to:
  /// **'Recorder closed'**
  String get voiceRecorderClosed;

  /// Recorder error message
  ///
  /// In en, this message translates to:
  /// **'Recorder error: {error}'**
  String voiceRecorderError(Object error);

  /// Test speech recognition button
  ///
  /// In en, this message translates to:
  /// **'Test Speech Recognition'**
  String get voiceTestSpeechRecognition;

  /// Test speech recognition function message
  ///
  /// In en, this message translates to:
  /// **'Test speech recognition...'**
  String get voiceTestSpeechRecognitionFunction;

  /// Speech recognition error message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition error: {error}'**
  String voiceSpeechRecognitionError(Object error);

  /// Speech recognition status message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition status: {status}'**
  String voiceSpeechRecognitionStatus(Object status);

  /// Speech recognition init message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition init: {success}'**
  String voiceSpeechRecognitionInit(Object success);

  /// Start listening message
  ///
  /// In en, this message translates to:
  /// **'Start listening...'**
  String get voiceStartListening;

  /// Recognition result message
  ///
  /// In en, this message translates to:
  /// **'Recognition result: {result}'**
  String voiceRecognitionResult(Object result);

  /// Stop listening message
  ///
  /// In en, this message translates to:
  /// **'Stop listening'**
  String get voiceStopListening;

  /// Speech recognition test error message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition test error: {error}'**
  String voiceSpeechRecognitionTestError(Object error);

  /// Open app settings button
  ///
  /// In en, this message translates to:
  /// **'Open App Settings'**
  String get voiceOpenAppSettings;

  /// Operation log label
  ///
  /// In en, this message translates to:
  /// **'Operation Log:'**
  String get voiceOperationLog;

  /// iOS permission test page loaded message
  ///
  /// In en, this message translates to:
  /// **'Page loaded'**
  String get voiceIosPageLoaded;

  /// Initial microphone permission status message
  ///
  /// In en, this message translates to:
  /// **'Initial microphone permission status'**
  String get voiceIosInitialMicrophonePermission;

  /// Initial speech recognition permission status message
  ///
  /// In en, this message translates to:
  /// **'Initial speech recognition permission status'**
  String get voiceIosInitialSpeechPermission;

  /// Non-iOS platform message
  ///
  /// In en, this message translates to:
  /// **'Non-iOS platform, not checking permissions'**
  String get voiceIosNonIosPlatform;

  /// Requesting microphone permission message
  ///
  /// In en, this message translates to:
  /// **'Requesting microphone permission...'**
  String get voiceIosRequestMicrophonePermission;

  /// Microphone permission status message
  ///
  /// In en, this message translates to:
  /// **'Microphone permission status'**
  String get voiceIosMicrophonePermissionStatus;

  /// Requesting speech recognition permission message
  ///
  /// In en, this message translates to:
  /// **'Requesting speech recognition permission...'**
  String get voiceIosRequestSpeechPermission;

  /// Speech recognition permission status message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition permission status'**
  String get voiceIosSpeechPermissionStatus;

  /// Testing recording function message
  ///
  /// In en, this message translates to:
  /// **'Testing recording function...'**
  String get voiceIosTestRecordingFunction;

  /// Recorder instance created message
  ///
  /// In en, this message translates to:
  /// **'Recorder instance created'**
  String get voiceIosRecorderInstanceCreated;

  /// Recorder initialized message
  ///
  /// In en, this message translates to:
  /// **'Recorder initialized'**
  String get voiceIosRecorderInitialized;

  /// Recorder test completed message
  ///
  /// In en, this message translates to:
  /// **'Recorder test completed'**
  String get voiceIosRecorderTestCompleted;

  /// Recorder closed message
  ///
  /// In en, this message translates to:
  /// **'Recorder closed'**
  String get voiceIosRecorderClosed;

  /// Recorder error message
  ///
  /// In en, this message translates to:
  /// **'Recorder error'**
  String get voiceIosRecorderError;

  /// Testing speech recognition message
  ///
  /// In en, this message translates to:
  /// **'Testing speech recognition...'**
  String get voiceIosTestSpeechRecognition;

  /// Speech recognition error message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition error'**
  String get voiceIosSpeechRecognitionError;

  /// Speech recognition status message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition status'**
  String get voiceIosSpeechRecognitionStatus;

  /// Speech recognition initialization message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition initialization'**
  String get voiceIosSpeechRecognitionInitialization;

  /// Success message
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get voiceIosSuccess;

  /// Failed message
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get voiceIosFailed;

  /// Start listening message
  ///
  /// In en, this message translates to:
  /// **'Start listening...'**
  String get voiceIosStartListening;

  /// Recognition result message
  ///
  /// In en, this message translates to:
  /// **'Recognition result'**
  String get voiceIosRecognitionResult;

  /// Stop listening message
  ///
  /// In en, this message translates to:
  /// **'Stop listening'**
  String get voiceIosStopListening;

  /// Speech recognition test error message
  ///
  /// In en, this message translates to:
  /// **'Speech recognition test error'**
  String get voiceIosSpeechRecognitionTestError;

  /// Open app settings message
  ///
  /// In en, this message translates to:
  /// **'Open app settings'**
  String get voiceIosOpenAppSettings;

  /// iOS permission test page title
  ///
  /// In en, this message translates to:
  /// **'iOS Permission Test'**
  String get voiceIosPermissionTest;

  /// Direct iOS permission test title
  ///
  /// In en, this message translates to:
  /// **'Direct iOS Permission Test'**
  String get voiceIosDirectPermissionTest;

  /// Operation logs label
  ///
  /// In en, this message translates to:
  /// **'Operation Logs'**
  String get voiceIosOperationLogs;

  /// OK button text
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get voiceOK;

  /// Microphone permission required message
  ///
  /// In en, this message translates to:
  /// **'Voice transcription requires microphone permission. Please allow microphone access in settings.'**
  String get voiceMicrophonePermissionRequired;

  /// Chinese simplified language option
  ///
  /// In en, this message translates to:
  /// **'中文（简体）'**
  String get voiceLanguageChineseSimplified;

  /// Chinese traditional language option
  ///
  /// In en, this message translates to:
  /// **'中文（繁体）'**
  String get voiceLanguageChineseTraditional;

  /// English language option
  ///
  /// In en, this message translates to:
  /// **'English (US)'**
  String get voiceLanguageEnglish;

  /// Japanese language option
  ///
  /// In en, this message translates to:
  /// **'日本語'**
  String get voiceLanguageJapanese;

  /// Korean language option
  ///
  /// In en, this message translates to:
  /// **'한국어'**
  String get voiceLanguageKorean;

  /// Title for the traffic guide watermark processing screen
  ///
  /// In en, this message translates to:
  /// **'Text Watermark Processing'**
  String get trafficGuideWatermarkTitle;

  /// Subtitle for add watermark mode
  ///
  /// In en, this message translates to:
  /// **'Add visible or invisible watermark to text'**
  String get trafficGuideAddWatermarkModeSubtitle;

  /// Subtitle for remove watermark mode
  ///
  /// In en, this message translates to:
  /// **'Remove added watermark from text'**
  String get trafficGuideRemoveWatermarkModeSubtitle;

  /// Visible watermark option
  ///
  /// In en, this message translates to:
  /// **'Visible Watermark'**
  String get trafficGuideVisibleWatermark;

  /// Watermark type selection
  ///
  /// In en, this message translates to:
  /// **'Watermark Type'**
  String get trafficGuideWatermarkType;

  /// Process failure message
  ///
  /// In en, this message translates to:
  /// **'Processing failed: {error}'**
  String trafficGuideProcessFailed(Object error);

  /// Show preview button text
  ///
  /// In en, this message translates to:
  /// **'Show Preview'**
  String get trafficGuideShowPreview;

  /// Hide preview button text
  ///
  /// In en, this message translates to:
  /// **'Hide Preview'**
  String get trafficGuideHidePreview;

  /// Process success message
  ///
  /// In en, this message translates to:
  /// **'Processing successful'**
  String get trafficGuideProcessSuccess;

  /// Label for detected watermark information
  ///
  /// In en, this message translates to:
  /// **'Detected Watermark'**
  String get trafficGuideDetectedWatermark;

  /// Label for unknown watermark type
  ///
  /// In en, this message translates to:
  /// **'Unknown Watermark'**
  String get trafficGuideUnknownWatermark;

  /// Message when no watermark is found
  ///
  /// In en, this message translates to:
  /// **'No Watermark Detected'**
  String get trafficGuideNoWatermarkDetected;

  /// Label for processed text output
  ///
  /// In en, this message translates to:
  /// **'Processed Text'**
  String get trafficGuideProcessedText;

  /// Information about invisible watermark functionality
  ///
  /// In en, this message translates to:
  /// **'This will add an invisible watermark using special Unicode characters that won\'t be visible to readers but can be detected by this tool.'**
  String get trafficGuideInvisibleWatermarkInfo;

  /// Success message when watermark is removed
  ///
  /// In en, this message translates to:
  /// **'Watermark removed successfully!'**
  String get trafficGuideWatermarkRemovedSuccess;

  /// Success message when watermark is added
  ///
  /// In en, this message translates to:
  /// **'Watermark added successfully!'**
  String get trafficGuideWatermarkAddedSuccess;

  /// No description provided for @exportSocialWeChatMoments.
  ///
  /// In en, this message translates to:
  /// **'WeChat Moments'**
  String get exportSocialWeChatMoments;

  /// No description provided for @exportSocialWeibo.
  ///
  /// In en, this message translates to:
  /// **'Weibo Image'**
  String get exportSocialWeibo;

  /// No description provided for @exportSocialXiaohongshu.
  ///
  /// In en, this message translates to:
  /// **'Xiaohongshu'**
  String get exportSocialXiaohongshu;

  /// No description provided for @exportSocialInstagram.
  ///
  /// In en, this message translates to:
  /// **'Instagram'**
  String get exportSocialInstagram;

  /// No description provided for @exportSocialTwitter.
  ///
  /// In en, this message translates to:
  /// **'Twitter'**
  String get exportSocialTwitter;

  /// No description provided for @exportWidthLabel.
  ///
  /// In en, this message translates to:
  /// **'Width'**
  String get exportWidthLabel;

  /// No description provided for @exportHeightLabel.
  ///
  /// In en, this message translates to:
  /// **'Height'**
  String get exportHeightLabel;

  /// No description provided for @exportOptimizeForSocial.
  ///
  /// In en, this message translates to:
  /// **'Social Media Optimization'**
  String get exportOptimizeForSocial;

  /// No description provided for @exportOptimizeForSocialSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Optimize size for social platforms'**
  String get exportOptimizeForSocialSubtitle;

  /// No description provided for @exportSocialPlatformSizes.
  ///
  /// In en, this message translates to:
  /// **'Social Platform Sizes'**
  String get exportSocialPlatformSizes;

  /// No description provided for @exportSizeSmall.
  ///
  /// In en, this message translates to:
  /// **'Small (400×300)'**
  String get exportSizeSmall;

  /// No description provided for @exportSizeMedium.
  ///
  /// In en, this message translates to:
  /// **'Medium (800×600)'**
  String get exportSizeMedium;

  /// No description provided for @exportSizeLarge.
  ///
  /// In en, this message translates to:
  /// **'Large (1200×900)'**
  String get exportSizeLarge;

  /// No description provided for @exportSizeCustom.
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get exportSizeCustom;

  /// General settings title
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get general;

  /// Display & Brightness settings title
  ///
  /// In en, this message translates to:
  /// **'Display & Brightness'**
  String get appearanceAndBrightness;

  /// Privacy & Security settings title
  ///
  /// In en, this message translates to:
  /// **'Privacy & Security'**
  String get privacyAndSecurity;

  /// Storage settings title
  ///
  /// In en, this message translates to:
  /// **'Storage'**
  String get storage;

  /// Support settings title
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// Language & Region setting item
  ///
  /// In en, this message translates to:
  /// **'Language & Region'**
  String get languageAndRegion;

  /// Privacy Settings item
  ///
  /// In en, this message translates to:
  /// **'Privacy Settings'**
  String get privacySettings;

  /// Storage Management item
  ///
  /// In en, this message translates to:
  /// **'Storage Management'**
  String get storageManagement;

  /// View storage usage description
  ///
  /// In en, this message translates to:
  /// **'View storage usage'**
  String get viewStorageUsage;

  /// Data Import & Export item
  ///
  /// In en, this message translates to:
  /// **'Data Import & Export'**
  String get dataImportExport;

  /// Backup and restore data description
  ///
  /// In en, this message translates to:
  /// **'Backup and restore data'**
  String get backupAndRestoreData;

  /// Help Center item
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get helpCenter;

  /// Feedback item
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// Rate App item
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get rateApp;

  /// About App item
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// Data Management title
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get dataManagement;

  /// Select operation prompt
  ///
  /// In en, this message translates to:
  /// **'Select operation to perform'**
  String get selectOperation;

  /// Export data operation
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportData;

  /// Import data operation
  ///
  /// In en, this message translates to:
  /// **'Import Data'**
  String get importData;

  /// Create backup operation
  ///
  /// In en, this message translates to:
  /// **'Create Backup'**
  String get createBackup;

  /// Data export in progress message
  ///
  /// In en, this message translates to:
  /// **'Data export feature is in development...'**
  String get dataExportInProgress;

  /// Data import in progress message
  ///
  /// In en, this message translates to:
  /// **'Data import feature is in development...'**
  String get dataImportInProgress;

  /// Backup in progress message
  ///
  /// In en, this message translates to:
  /// **'Backup feature is in development...'**
  String get backupInProgress;

  /// Rate app prompt
  ///
  /// In en, this message translates to:
  /// **'Do you like this app? Please rate us on the App Store!'**
  String get doYouLikeThisApp;

  /// Later button
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get later;

  /// Go to rate button
  ///
  /// In en, this message translates to:
  /// **'Rate Now'**
  String get goToRate;

  /// Cannot open App Store error
  ///
  /// In en, this message translates to:
  /// **'Cannot open App Store'**
  String get cannotOpenAppStore;

  /// Error opening App Store
  ///
  /// In en, this message translates to:
  /// **'Error occurred while opening App Store'**
  String get errorOpeningAppStore;

  /// Understand button
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get understand;

  /// Privacy statement
  ///
  /// In en, this message translates to:
  /// **'We value your privacy. All data processing is done locally and will not be uploaded to servers.'**
  String get weValueYourPrivacy;

  /// Data management statement
  ///
  /// In en, this message translates to:
  /// **'You can manage your data in settings at any time.'**
  String get manageDataAnytime;

  /// Language selection notification
  ///
  /// In en, this message translates to:
  /// **'Language selected: {language}'**
  String languageSelected(Object language);

  /// Light theme
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// Dark theme
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get dark;

  /// Select language title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Select appearance title
  ///
  /// In en, this message translates to:
  /// **'Select Appearance'**
  String get selectAppearance;

  /// Traditional Chinese
  ///
  /// In en, this message translates to:
  /// **'Traditional Chinese'**
  String get traditionalChinese;

  /// Simplified Chinese
  ///
  /// In en, this message translates to:
  /// **'Simplified Chinese'**
  String get simplifiedChinese;

  /// Text for save/favorite button in content manager screens
  ///
  /// In en, this message translates to:
  /// **'Favorite'**
  String get contentSaveButtonFavorite;

  /// Title for template selection section
  ///
  /// In en, this message translates to:
  /// **'Select Transformation Mode'**
  String get textTransformerSelectMode;

  /// Label for input text section
  ///
  /// In en, this message translates to:
  /// **'Input Text'**
  String get textTransformerInputText;

  /// Label for output result section
  ///
  /// In en, this message translates to:
  /// **'Transformation Result'**
  String get textTransformerOutputResult;

  /// Hint text for input field
  ///
  /// In en, this message translates to:
  /// **'Enter text to transform here...'**
  String get textTransformerHint;

  /// Hint text for output field
  ///
  /// In en, this message translates to:
  /// **'Transformed text will appear here...'**
  String get textTransformerOutputHint;

  /// Text for character count
  ///
  /// In en, this message translates to:
  /// **'characters'**
  String get textTransformerCharacters;

  /// Transform button text
  ///
  /// In en, this message translates to:
  /// **'Transform'**
  String get textTransformerTransform;

  /// Text shown while transforming
  ///
  /// In en, this message translates to:
  /// **'Transforming...'**
  String get textTransformerTransforming;

  /// Clear all button tooltip
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get textTransformerClearAll;

  /// Copy result button tooltip
  ///
  /// In en, this message translates to:
  /// **'Copy Result'**
  String get textTransformerCopyResult;

  /// Message shown when text is copied
  ///
  /// In en, this message translates to:
  /// **'Copied to clipboard'**
  String get textTransformerCopied;

  /// Name for emoji template
  ///
  /// In en, this message translates to:
  /// **'Emoji Conversion'**
  String get textTransformerTemplateEmojiName;

  /// Description for emoji template
  ///
  /// In en, this message translates to:
  /// **'Convert text to special emoji characters'**
  String get textTransformerTemplateEmojiDesc;

  /// Name for fancy letters template
  ///
  /// In en, this message translates to:
  /// **'Fancy Letters'**
  String get textTransformerTemplateFancyName;

  /// Description for fancy letters template
  ///
  /// In en, this message translates to:
  /// **'Transform to elegant fancy letters'**
  String get textTransformerTemplateFancyDesc;

  /// Name for bold text template
  ///
  /// In en, this message translates to:
  /// **'Bold Text'**
  String get textTransformerTemplateBoldName;

  /// Description for bold text template
  ///
  /// In en, this message translates to:
  /// **'Convert to bold Unicode characters'**
  String get textTransformerTemplateBoldDesc;

  /// Name for decorative text template
  ///
  /// In en, this message translates to:
  /// **'Decorative Text'**
  String get textTransformerTemplateDecorativeName;

  /// Description for decorative text template
  ///
  /// In en, this message translates to:
  /// **'Add decorative symbols'**
  String get textTransformerTemplateDecorativeDesc;

  /// Name for mixed effects template
  ///
  /// In en, this message translates to:
  /// **'Mixed Effects'**
  String get textTransformerTemplateMixedName;

  /// Description for mixed effects template
  ///
  /// In en, this message translates to:
  /// **'Randomly combine multiple transformation effects'**
  String get textTransformerTemplateMixedDesc;

  /// Name for invisible characters template
  ///
  /// In en, this message translates to:
  /// **'Invisible Characters'**
  String get textTransformerTemplateInvisibleName;

  /// Description for invisible characters template
  ///
  /// In en, this message translates to:
  /// **'Add invisible characters to bypass detection'**
  String get textTransformerTemplateInvisibleDesc;

  /// Name for Unicode variants template
  ///
  /// In en, this message translates to:
  /// **'Unicode Variants'**
  String get textTransformerTemplateUnicodeName;

  /// Description for Unicode variants template
  ///
  /// In en, this message translates to:
  /// **'Use Unicode variant characters'**
  String get textTransformerTemplateUnicodeDesc;

  /// Effect description for emoji mode
  ///
  /// In en, this message translates to:
  /// **'Convert numbers and letters to special Unicode characters'**
  String get textTransformerEffectEmojiDesc;

  /// Effect description for fancy mode
  ///
  /// In en, this message translates to:
  /// **'Transform to elegant fancy letters'**
  String get textTransformerEffectFancyDesc;

  /// Effect description for bold mode
  ///
  /// In en, this message translates to:
  /// **'Convert to bold Unicode characters'**
  String get textTransformerEffectBoldDesc;

  /// Effect description for decorative mode
  ///
  /// In en, this message translates to:
  /// **'Add decorative symbols'**
  String get textTransformerEffectDecorativeDesc;

  /// Effect description for mixed mode
  ///
  /// In en, this message translates to:
  /// **'Randomly combine multiple transformation effects'**
  String get textTransformerEffectMixedDesc;

  /// Effect description for invisible mode
  ///
  /// In en, this message translates to:
  /// **'Add invisible characters between characters to bypass detection'**
  String get textTransformerEffectInvisibleDesc;

  /// Effect description for Unicode mode
  ///
  /// In en, this message translates to:
  /// **'Add diacritical marks to change character appearance'**
  String get textTransformerEffectUnicodeDesc;

  /// Generic sample text for transformations
  ///
  /// In en, this message translates to:
  /// **'Sample Text'**
  String get textTransformerSample;

  /// Sample text for invisible character mode
  ///
  /// In en, this message translates to:
  /// **'Sensitive content detection bypass test'**
  String get textTransformerSampleInvisible;

  /// Sample text for Unicode variant mode
  ///
  /// In en, this message translates to:
  /// **'Special character conversion test'**
  String get textTransformerSampleUnicode;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'ja', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'ja':
      return AppLocalizationsJa();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
