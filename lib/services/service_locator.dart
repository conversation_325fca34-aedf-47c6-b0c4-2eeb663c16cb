import 'package:flutter/material.dart';

import '../html/html_service.dart';
import '../subscription/subscription_service.dart';
import '../svg/svg_service.dart';
import 'content_service.dart';
import 'settings_service.dart';
import 'storage_service.dart';

/// 服务定位器/管理器，负责初始化和管理所有服务
class ServiceLocator {
  // 单例模式
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  // 全局key，用于重建整个应用
  final GlobalKey<NavigatorState> globalKey = GlobalKey<NavigatorState>();

  // 服务实例 - 使用懒加载方式初始化
  late final StorageService _storageService = StorageService();
  late final SettingsService _settingsService = SettingsService();
  late final SvgService _svgService = SvgService();
  late final HtmlService _htmlService = HtmlService();
  late final SubscriptionService _subscriptionService = SubscriptionService();
  final ContentService _contentService = ContentService();

  bool _isInitialized = false;
  bool _criticalServicesInitialized = false;
  bool _nonCriticalServicesInitialized = false;

  /// 初始化所有服务（保持向后兼容）
  Future<void> initServices() async {
    await initCriticalServices();
    await initNonCriticalServices();
  }

  /// 初始化关键服务（必须在UI显示前完成）
  Future<void> initCriticalServices() async {
    if (_criticalServicesInitialized) return;

    try {
      debugPrint('正在初始化关键服务...');

      // 只初始化关键服务
      await _storageService.init();
      await _settingsService.init();

      _criticalServicesInitialized = true;
      debugPrint('关键服务初始化完成');
    } catch (e) {
      debugPrint('关键服务初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化非关键服务（可以在后台进行）
  Future<void> initNonCriticalServices() async {
    if (_nonCriticalServicesInitialized) return;

    try {
      debugPrint('正在初始化非关键服务...');

      // 并行初始化独立的服务
      await Future.wait([
        _contentService.initialize(),
        _subscriptionService.init(),
        _svgService.initialize(),
        _htmlService.initialize(),
      ]);

      _nonCriticalServicesInitialized = true;
      _isInitialized = true; // 所有服务都已初始化
      debugPrint('非关键服务初始化完成');
    } catch (e) {
      debugPrint('非关键服务初始化失败: $e');
      // 非关键服务失败不应该阻止应用运行
    }
  }

  /// 获取存储服务
  StorageService get storageService => _storageService;

  /// 获取设置服务
  SettingsService get settingsService => _settingsService;

  /// 获取SVG服务
  SvgService get svgService => _svgService;

  /// 获取HTML服务
  HtmlService get htmlService => _htmlService;

  /// 获取订阅服务
  SubscriptionService get subscriptionService => _subscriptionService;

  /// 获取内容服务
  ContentService get contentService => _contentService;

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;

  /// 检查关键服务是否已初始化
  bool get criticalServicesInitialized => _criticalServicesInitialized;

  /// 检查非关键服务是否已初始化
  bool get nonCriticalServicesInitialized => _nonCriticalServicesInitialized;
}
