import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/constants.dart';
import 'service_locator.dart';

/// 应用初始化管理器
/// 负责管理应用启动时的初始化流程，分为关键服务和非关键服务
class AppInitializationManager {
  static bool _criticalServicesInitialized = false;
  static bool _nonCriticalServicesInitialized = false;
  static bool _isInitializing = false;

  /// 初始化关键服务（必须在UI显示前完成）
  static Future<void> initializeCriticalServices() async {
    if (_criticalServicesInitialized || _isInitializing) return;
    
    _isInitializing = true;
    
    try {
      debugPrint('开始初始化关键服务...');
      
      // 设备方向设置（快速操作）
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      
      // 基础存储初始化
      await _initializeBasicStorage();
      
      // 初始化关键服务
      await ServiceLocator().initCriticalServices();
      
      _criticalServicesInitialized = true;
      debugPrint('关键服务初始化完成');
    } catch (e) {
      debugPrint('关键服务初始化失败: $e');
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  /// 初始化非关键服务（可以在UI显示后在后台进行）
  static Future<void> initializeNonCriticalServices() async {
    if (_nonCriticalServicesInitialized) return;
    
    try {
      debugPrint('开始初始化非关键服务...');
      
      // 确保关键服务已初始化
      if (!_criticalServicesInitialized) {
        await initializeCriticalServices();
      }
      
      // 初始化非关键服务
      await ServiceLocator().initNonCriticalServices();
      
      _nonCriticalServicesInitialized = true;
      debugPrint('非关键服务初始化完成');
    } catch (e) {
      debugPrint('非关键服务初始化失败: $e');
      // 非关键服务失败不应该阻止应用运行
    }
  }

  /// 基础存储初始化（仅包含必要的存储设置）
  static Future<void> _initializeBasicStorage() async {
    final appDocumentDir = await getApplicationDocumentsDirectory();
    await Hive.initFlutter(appDocumentDir.path);
    await Hive.openBox(AppConstants.mainBoxName);

    // 检查首次启动（快速操作）
    final prefs = await SharedPreferences.getInstance();
    final hasLaunchedBefore = prefs.getBool(AppConstants.keyHasLaunchedBefore);

    if (hasLaunchedBefore == null) {
      // 首次启动，设置标记
      await prefs.setBool(AppConstants.keyHasLaunchedBefore, true);
    }
  }

  /// 检查关键服务是否已初始化
  static bool get criticalServicesInitialized => _criticalServicesInitialized;

  /// 检查非关键服务是否已初始化
  static bool get nonCriticalServicesInitialized => _nonCriticalServicesInitialized;

  /// 检查所有服务是否已初始化
  static bool get allServicesInitialized => 
      _criticalServicesInitialized && _nonCriticalServicesInitialized;

  /// 重置初始化状态（用于测试）
  static void reset() {
    _criticalServicesInitialized = false;
    _nonCriticalServicesInitialized = false;
    _isInitializing = false;
  }
}
