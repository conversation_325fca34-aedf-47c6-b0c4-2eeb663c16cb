{"@@locale": "zh", "@@last_modified": "2024-01-15T10:00:00.000Z", "appName": "内容君", "appNameChinese": "内容君", "appDescription": "专业的内容处理工具，让内容创作更轻松", "home": "首页", "settings": "设置", "language": "语言", "theme": "主题", "lightTheme": "浅色", "darkTheme": "深色", "systemTheme": "跟随系统", "sharedText": "分享的文本", "failedToGetInitialIntent": "获取初始意图失败：{error}", "failedToLoadThemeSettings": "加载主题设置失败：{error}", "themeMaterialYou": "Material You", "themeMorandi": "莫兰迪风格", "themeMonochrome": "极简黑白", "themeNature": "自然色系", "themeTech": "科技感", "themeChinese": "中国传统色", "themeMaterialYouDesc": "根据壁纸自动提取的动态主题", "themeMorandiDesc": "柔和优雅的莫兰迪色系", "themeMonochromeDesc": "简约纯粹的黑白配色", "themeNatureDesc": "舒适自然的生态色系", "themeTechDesc": "充满未来感的科技色彩", "themeChineseDesc": "传统与现代结合的东方美学", "markdown": "<PERSON><PERSON>", "textCards": "文本卡片", "textCardSelectColor": "选择颜色", "textCardStyleApplied": "样式已应用", "textCardSplitFailed": "拆分失败：{error}", "textCardCreateFailed": "创建失败：{error}", "textCardEditCard": "编辑卡片", "textCardPreviousStep": "上一步", "textCardSaveToContentLibrary": "保存到内容库", "textCardStartExportingImage": "开始导出图片...", "textCardImageSavedSuccess": "✅ 图片已成功保存到相册", "textCardPleaseEnterContent": "请输入卡片内容", "textCardCreateBeautifulCard": "创建精美卡片", "textCardExportImage": "导出图片", "textCardsSelectTemplate": "选择模板", "textCardsPreviewEffect": "预览效果", "textCardsSelectTextToModifyStyle": "选中文本可以修改字体、颜色和大小", "textCardsTemplateGallery": "模板画廊", "contentDefaultsTitle": "默认示例内容", "markdownDefaultSampleTitle": "Markdown 默认示例", "markdownDefaultSampleDesc": "进入 Markdown 时自动填充示例内容", "textCardsDefaultSampleTitle": "文本卡片 默认示例", "textCardsDefaultSampleDesc": "进入文本卡片时自动填充示例内容", "svgBuiltInPresetTitle": "SVG 内置示例", "svgBuiltInPresetDesc": "当没有文档时，显示内置 SVG 预设", "htmlDefaultSampleTitle": "HTML 默认示例", "htmlDefaultSampleDesc": "当没有文档时，加载内置 HTML 示例", "transformerExampleInputTitle": "文本转换 示例输入", "transformerExampleInputDesc": "进入文本转换时自动填充示例输入", "privacyPolicyTitle": "隐私政策", "privacyPolicySubtitle": "查看我们的隐私政策", "openSourceLicensesTitle": "开源许可", "openSourceLicensesSubtitle": "查看第三方开源库许可", "subscriptionUpgradeTitle": "升级到高级版", "subscriptionUpgradeSubtitle": "解锁所有高级功能，提升您的AI体验", "subscriptionChoosePlan": "选择您的订阅计划", "subscriptionDiscountSavePercent": "省{percent}%", "subscriptionLoadingPrice": "价格加载中…", "subscriptionEquivalentToPerMonth": "相当于 {price}", "subscriptionIncludedFeatures": "包含的功能", "subscriptionSubscribeNowWithPrice": "立即订阅 {price}", "subscriptionAgreementPrefix": "订阅即表示您同意我们的", "termsOfService": "服务条款", "privacyPolicy": "隐私政策", "subscriptionAgreementSuffix": "。订阅会自动续费，可随时取消。", "subscriptionDevModeNotice": "开发模式：订阅功能当前使用模拟数据，App Store Connect批准后将使用真实数据", "diagnosticsTitle": "诊断结果", "diagnosticsClose": "关闭", "subscriptionDiagnosticsButton": "诊断购买服务", "restoreCompletedTitle": "恢复完成", "restoreFailedTitle": "恢复失败", "restoreCompletedMessage": "您的购买已成功恢复。", "restoreFailedMessageWithError": "恢复购买时发生错误：{error}", "andText": "和", "subscriptionPlanMonthlyName": "内容君专业版（月度）", "subscriptionPlanMonthlyDesc": "解锁所有高级内容处理功能", "subscriptionPlanYearlyName": "内容君专业版（年度）", "subscriptionPlanYearlyDesc": "年度订阅，更省哦", "subscriptionPlanLifetimeName": "内容君专业版（永久）", "subscriptionPlanLifetimeDesc": "一次性付费，终身使用", "subscriptionPlanFreeName": "免费版", "subscriptionPlanFreeDesc": "基础内容处理功能", "subscriptionFeatureUnlimitedExportsName": "无限导出", "subscriptionFeatureUnlimitedExportsDesc": "无限制导出处理后的内容", "subscriptionFeatureBatchProcessingName": "批量处理", "subscriptionFeatureBatchProcessingDesc": "支持批量处理多个文件", "subscriptionFeatureAdvancedToolsName": "高级工具", "subscriptionFeatureAdvancedToolsDesc": "访问所有高级编辑和处理工具", "subscriptionFeatureNoWatermarkName": "无水印", "subscriptionFeatureNoWatermarkDesc": "导出内容不含水印", "subscriptionFeaturePrioritySupportName": "优先支持", "subscriptionFeaturePrioritySupportDesc": "获得优先客户支持", "subscriptionFeatureFutureUpdatesName": "未来更新", "subscriptionFeatureFutureUpdatesDesc": "获得所有未来功能更新", "subscriptionFeatureBasicProcessingName": "基础处理", "subscriptionFeatureBasicProcessingDesc": "基础内容处理功能", "subscriptionFeatureWatermarkedExportsName": "带水印导出", "subscriptionFeatureWatermarkedExportsDesc": "导出内容带水印", "subscriptionPeriodPerMonthSuffix": "/月", "subscriptionPeriodPerYearSuffix": "/年", "subscriptionPeriodLifetime": "终身", "subscriptionPeriodFree": "免费", "purchaseSuccessMessage": "购买成功，订阅已激活。感谢支持！", "restoringPurchase": "正在恢复购买", "communicatingWithAppStore": "正在与应用商店通信，请稍候...", "noRestorablePurchasesFound": "未找到可恢复的购买记录", "ok": "确定", "currentPlan": "当前计划", "active": "有效", "expired": "已过期", "inactive": "非活跃", "daysUntilExpiry": "{days}天后到期", "subscriptionExpired": "已过期", "textCardsBrowseTemplates": "浏览模板", "textCardsBeautifulTemplates": "个精美模板，找到最适合的设计", "textCardsAllCategories": "全部", "blockMarkdown": "分块Markdown", "cardCollection": "卡片合集", "textCardsBusinessCategory": "商务", "textCardsAcademicCategory": "学术", "textCardsCreativeCategory": "创意", "textCardsMinimalCategory": "简约", "textCardsModernCategory": "现代", "textCardsDarkCategory": "暗黑", "textCardsNatureCategory": "自然", "textCardsWarmCategory": "温暖", "textCardsTechCategory": "科技", "textCardsElegantCategory": "优雅", "textCardsVintageCategory": "复古", "textCardExportSettings": "导出设置", "textCardImageSize": "图片尺寸", "textCardImageRatio": "图片比例", "textCardDimensions": "尺寸", "textCardNotInclude": "不包含", "textCardInclude": "包含", "textCardDeleteCard": "删除卡片", "textCardDeleteConfirm": "确定要删除这个卡片吗？此操作无法撤销。", "textCardCategory": "分类：{category}", "textCardDescription": "描述：{description}", "textCardClose": "关闭", "textCardUseTemplate": "使用模板", "textCardConfirmExport": "确定导出", "textCardQuality": "质量", "textCardIncludeWatermark": "包含水印", "textCardPreviewInfo": "预览信息", "textCardRatio": "比例: {ratio}", "textCardQualityPercent": "质量: {quality}%", "textCardWatermarkStatus": "水印: {status}", "textCardAddCard": "添加卡片", "textCardPreviewEffect": "预览效果", "textCardExportInfo": "导出信息", "textCardImageDimensions": "图片尺寸", "textCardAspectRatio": "宽高比", "textCardFileSize": "文件大小", "textCardUsageScenario": "适用场景", "textCardImageQuality": "图片质量", "textCardBestQuality": "最佳质量 (100%)", "textCardWatermarkDescription": "在图片底部添加应用标识", "textCardAddNewCard": "添加新卡片", "textCardEdit": "编辑", "textCardExportingImage": "正在导出图片...", "textCardExportSuccess": "✅ 导出成功！已保存到相册", "textCardExportFailed": "导出失败：", "textCardAddWatermark": "添加水印", "textCardAddWatermarkDesc": "在图片角落添加应用水印", "textCardIncludeTitle": "包含标题", "textCardIncludeTitleDesc": "在导出的图片中显示标题", "textCardIncludeTimestamp": "包含时间戳", "textCardIncludeTimestampDesc": "在图片中显示创建时间", "textCardTextStyleCustomization": "文本样式定制", "textCardExportCurrentCard": "导出当前卡片", "textCardBatchExport": "批量导出", "textCardClearSelection": "清除选择", "textCardResetStyle": "重置样式", "textCardResetStyleConfirm": "确定要清除所有文本样式吗？此操作不可撤销。", "textCardExportAsImage": "导出为图片", "textCardExportAsImageDesc": "保存这个卡片为图片", "textCardEditCardDesc": "修改标题和内容", "textCardDeleteCardDesc": "从文档中移除", "textCardDeleteCardConfirm": "确定要删除卡片\"{title}\"吗？此操作无法撤销。", "textCardEditDocument": "编辑文档", "textCardUniformStyle": "统一样式", "textCardInsertSeparator": "插入分隔符", "textCardPleaseEnterTitleAndCards": "请输入标题并确保至少有一个卡片", "textCardSaveFailed": "保存失败", "textCardContentRendering": "内容渲染", "textCardExportWithTitle": "导出 - {title}", "textCardShare": "分享", "textCardSaveToAlbum": "保存到相册", "textCardUnderstood": "了解了", "textCardStartExperience": "立即体验", "textCardFeatureDemo": "功能演示", "textCardGotIt": "知道了", "textCardStartUsing": "开始使用", "pdf": "PDF", "voice": "语音", "svg": "SVG", "html": "HTML", "copywriting": "文案", "trafficGuide": "内容引流工具", "content": "内容", "create": "创建", "edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "confirm": "确定", "yes": "是", "no": "否", "loading": "加载中...", "error": "错误", "success": "成功", "warning": "警告", "info": "信息", "search": "搜索", "searchHint": "请输入搜索内容...", "noResults": "未找到结果", "tryAgain": "重试", "refresh": "刷新", "share": "分享", "export": "导出", "import": "导入", "copy": "复制", "paste": "粘贴", "cut": "剪切", "undo": "撤销", "redo": "重做", "selectAll": "全选", "close": "关闭", "back": "返回", "next": "下一步", "previous": "上一步", "done": "完成", "finish": "结束", "skip": "跳过", "continueAction": "继续", "retry": "重试", "reset": "重置", "clear": "清除", "apply": "应用", "preview": "预览", "download": "下载", "upload": "上传", "file": "文件", "folder": "文件夹", "name": "名称", "title": "标题", "description": "描述", "size": "大小", "date": "日期", "time": "时间", "type": "类型", "status": "状态", "version": "版本", "author": "作者", "tags": "标签", "category": "分类", "priority": "优先级", "high": "高", "medium": "中", "low": "低", "enabled": "已启用", "disabled": "已禁用", "online": "在线", "offline": "离线", "connected": "已连接", "disconnected": "已断开", "available": "可用", "unavailable": "不可用", "public": "公开", "private": "私有", "draft": "草稿", "published": "已发布", "archived": "已归档", "pdfProfessionalTool": "PDF 专业工具", "pdfToolDescription": "强大的PDF处理能力，让文档管理更简单", "securityEncryption": "安全加密", "passwordProtectionPermissionControl": "密码保护\n权限控制", "intelligentAnnotation": "智能注释", "highlightMarkingTextAnnotation": "高亮标记\n文字批注", "quickSearch": "快速搜索", "fullTextSearchContentLocation": "全文检索\n内容定位", "convenientSharing": "便捷分享", "multipleFormatsOneClickExport": "多种格式\n一键导出", "welcomeToPdfTool": "欢迎使用PDF专业工具！", "importFirstPdfDocument": "导入第一个PDF文档", "appearance": "外观", "followSystem": "跟随系统", "languageChangeEffect": "更改语言后，应用将立即生效", "contentLibrary": "内容库", "manageAllCards": "管理所有卡片", "templateLibrary": "模板库", "browseBeautifulTemplates": "浏览精美模板", "inputTextToSplit": "输入文本", "pasteOrInputLongText": "粘贴或输入长文本内容", "pasteClipboard": "粘贴剪贴板", "clearContent": "清空内容", "cardNumber": "卡片 {number}", "loadingDemoData": "正在加载演示数据...", "modernUIDesign": "✨ 现代化UI设计\n🖼️ 渲染结果预览\n📱 分块模式支持\n⚡ 高性能体验", "editFunction": "编辑功能：{title}", "deleted": "已删除：{title}", "shareFunction": "分享功能：{title}", "createNewContent": "创建新内容", "selectContentType": "选择您要创建的内容类型", "bold": "**粗体**", "italic": "*斜体*", "heading1": "# 标题1", "heading2": "## 标题2", "heading3": "### 标题3", "list": "- 列表项\n- 列表项", "link": "[链接文本](URL)", "image": "![图片描述](图片URL)", "code": "`代码`", "codeBlock": "```\n代码块\n```", "quote": "> 引用文本", "table": "| 列1 | 列2 |\n| --- | --- |\n| 内容1 | 内容2 |", "myContentLibrary": "我的内容库", "manageAndBrowseContent": "管理和浏览您的所有内容", "contentTools": "内容工具", "recommendedTools": "推荐工具", "markdownTitle": "<PERSON><PERSON>", "markdownDescription": "文档编辑与渲染", "textCardsTitle": "文本卡片", "textCardsDescription": "知识卡片定制渲染", "trafficGuideTitle": "内容引流工具", "trafficGuideDescription": "内容引流图片与文本处理", "fileTools": "文件工具", "svgTitle": "SVG", "svgDescription": "矢量图形处理", "htmlTitle": "HTML", "htmlDescription": "网页内容编辑", "loadingContent": "正在加载内容...", "languageChangedTo": "语言已更改为 {language}", "developer": "开发者", "contentLibraryDemo": "内容库演示", "viewNewContentLibraryFeatures": "查看新的内容库功能", "i18nDemo": "国际化演示", "viewMultiLanguageSupport": "查看多语言支持效果", "about": "关于", "versionInfo": "版本信息", "helpAndFeedback": "帮助与反馈", "getHelpOrProvideFeedback": "获取帮助或提供反馈", "helpAndFeedbackContent": "如果您有任何问题或建议，请通过以下方式联系我们：\n\n邮箱：<EMAIL>", "selectTheme": "选择主题", "lightMode": "浅色模式", "darkMode": "深色模式", "systemMode": "跟随系统", "personalizeYourAppExperience": "个性化您的应用体验", "useDefaultInitialText": "使用默认初始文本", "useDefaultInitialTextDescription": "进入模块时自动填充默认示例内容", "contentSettings": "内容设置", "trafficGuideImageGenerator": "引流图片生成器", "@trafficGuideImageGenerator": {"description": "Image generator tool title"}, "trafficGuideImageGeneratorSubtitle": "生成方便在各平台使用的引流图片", "@trafficGuideImageGeneratorSubtitle": {"description": "Subtitle for image generator"}, "trafficGuideTabText": "文本", "@trafficGuideTabText": {"description": "Text tab label"}, "trafficGuideTabTemplate": "模板", "@trafficGuideTabTemplate": {"description": "Template tab label"}, "trafficGuideTabEffects": "效果", "@trafficGuideTabEffects": {"description": "Effects tab label"}, "trafficGuideTextContent": "文本内容", "@trafficGuideTextContent": {"description": "Text content field label"}, "trafficGuideTextHint": "输入要显示的文本", "@trafficGuideTextHint": {"description": "Text content field hint"}, "trafficGuideFontSettings": "字体设置", "@trafficGuideFontSettings": {"description": "Font settings section title"}, "trafficGuideFontSize": "字体大小", "@trafficGuideFontSize": {"description": "Font size field label"}, "trafficGuideColorSettings": "颜色设置", "@trafficGuideColorSettings": {"description": "Color settings section title"}, "trafficGuideTextColor": "文字颜色", "@trafficGuideTextColor": {"description": "Text color field label"}, "trafficGuideBackgroundColor": "背景颜色", "@trafficGuideBackgroundColor": {"description": "Background color field label"}, "trafficGuideVisualEffects": "视觉效果", "@trafficGuideVisualEffects": {"description": "Visual effects section title"}, "trafficGuideNoiseLevel": "干扰程度", "@trafficGuideNoiseLevel": {"description": "Noise level slider label"}, "trafficGuideDistortionLevel": "扭曲程度", "@trafficGuideDistortionLevel": {"description": "Distortion level slider label"}, "trafficGuideAddWatermark": "添加水印", "@trafficGuideAddWatermark": {"description": "Add watermark checkbox label"}, "trafficGuideWatermarkText": "水印文本", "@trafficGuideWatermarkText": {"description": "Watermark text field label"}, "trafficGuideWatermarkHint": "输入水印内容...", "@trafficGuideWatermarkHint": {"description": "Watermark hint text"}, "trafficGuideExport": "导出", "@trafficGuideExport": {"description": "Export button text"}, "trafficGuideSelectTemplateFirst": "请先选择模板", "@trafficGuideSelectTemplateFirst": {"description": "Template selection error"}, "trafficGuideImageSavedSuccess": "图片保存成功", "@trafficGuideImageSavedSuccess": {"description": "Image save success message"}, "trafficGuideSaveFailed": "保存失败: {error}", "@trafficGuideSaveFailed": {"description": "Save error message"}, "trafficGuidePermissionPermanentlyDenied": "权限被永久拒绝", "@trafficGuidePermissionPermanentlyDenied": {"description": "Permission permanently denied error"}, "trafficGuidePermissionRequired": "需要权限", "@trafficGuidePermissionRequired": {"description": "Permission required error"}, "trafficGuideSaveFailedWithMessage": "保存失败: {message}", "@trafficGuideSaveFailedWithMessage": {"description": "Save failed with message error"}, "trafficGuideSaveFailedEmptyResult": "保存失败: 空结果", "@trafficGuideSaveFailedEmptyResult": {"description": "Save failed empty result error"}, "markdownPreview": "预览", "@markdownPreview": {"description": "Preview tab label"}, "markdownContentLabel": "Markdown内容", "@markdownContentLabel": {"description": "Markdown content label"}, "markdownRenderModeLabel": "渲染模式：", "@markdownRenderModeLabel": {"description": "Render mode label"}, "markdownNormalMode": "普通模式", "@markdownNormalMode": {"description": "Normal render mode"}, "markdownBlockMode": "分块模式", "@markdownBlockMode": {"description": "Block render mode"}, "markdownConfigTab": "配置", "@markdownConfigTab": {"description": "Configuration tab label"}, "markdownManageTab": "管理", "@markdownManageTab": {"description": "Management tab label"}, "markdownPreviewTab": "预览", "@markdownPreviewTab": {"description": "Preview tab label"}, "markdownBlockInfo": "分块信息", "@markdownBlockInfo": {"description": "Block information section title"}, "markdownTotalBlocks": "总分块数", "@markdownTotalBlocks": {"description": "Total blocks count label"}, "markdownVisibleBlocks": "可见分块", "@markdownVisibleBlocks": {"description": "Visible blocks count label"}, "markdownEditorTitle": "Markdown编辑器", "@markdownEditorTitle": {"description": "Markdown editor title"}, "markdownPreviewTitle": "Markdown预览", "@markdownPreviewTitle": {"description": "Markdown preview title"}, "markdownTitleLabel": "标题", "@markdownTitleLabel": {"description": "Title field label"}, "markdownSubtitleLabel": "副标题（可选）", "@markdownSubtitleLabel": {"description": "Subtitle field label"}, "markdownUntitledDocument": "未命名文档", "@markdownUntitledDocument": {"description": "Default document title"}, "markdownUntitledSection": "未命名段落", "@markdownUntitledSection": {"description": "Default section title"}, "markdownSplitSections": "拆分段落", "@markdownSplitSections": {"description": "Split sections button text"}, "markdownSaveDocument": "保存文档", "@markdownSaveDocument": {"description": "Save document button text in markdown module"}, "markdownActionOptions": "操作选项", "@markdownActionOptions": {"description": "Action options title in markdown module"}, "markdownShareImage": "分享图片", "@markdownShareImage": {"description": "Share image button text in markdown module"}, "markdownCopyContent": "复制内容", "@markdownCopyContent": {"description": "Copy content option"}, "markdownSaveToAlbum": "保存到相册", "@markdownSaveToAlbum": {"description": "Save to album button text in markdown module"}, "commonCancel": "取消", "@commonCancel": {"description": "Cancel button text"}, "commonReset": "重置", "@commonReset": {"description": "Reset button text"}, "commonSelectAll": "全选", "@commonSelectAll": {"description": "Select all button text"}, "commonDeselectAll": "取消全选", "@commonDeselectAll": {"description": "Deselect all button text"}, "markdownShowSelected": "显示选中", "@markdownShowSelected": {"description": "Show selected blocks button text in markdown module"}, "markdownHideSelected": "隐藏选中", "@markdownHideSelected": {"description": "Hide selected blocks button text in markdown module"}, "markdownExportSelected": "导出选中", "@markdownExportSelected": {"description": "Export selected blocks button text in markdown module"}, "markdownHideBlock": "隐藏分块", "@markdownHideBlock": {"description": "Hide block action text in markdown module"}, "markdownShowBlock": "显示分块", "@markdownShowBlock": {"description": "Show block action text in markdown module"}, "markdownExportAsImage": "导出为图片", "@markdownExportAsImage": {"description": "Export as image option in markdown module"}, "markdownExportAsMarkdown": "导出为Markdown", "@markdownExportAsMarkdown": {"description": "Export as markdown option in markdown module"}, "commonGotIt": "知道了", "@commonGotIt": {"description": "Got it button text"}, "markdownBlockRenderSettings": "分块渲染设置", "@markdownBlockRenderSettings": {"description": "Block render settings title"}, "markdownBasicSettings": "基本设置", "@markdownBasicSettings": {"description": "Basic settings section title"}, "markdownEnableBlockRender": "启用分块渲染", "@markdownEnableBlockRender": {"description": "Enable block rendering option"}, "markdownSeparatorSettings": "分隔符设置", "@markdownSeparatorSettings": {"description": "Separator settings section title"}, "markdownSplitByH1": "按一级标题分隔", "@markdownSplitByH1": {"description": "Split by H1 headers option"}, "markdownSplitByH2": "按二级标题分隔", "@markdownSplitByH2": {"description": "Split by H2 headers option"}, "markdownCustomSeparatorPattern": "自定义分隔符模式（正则表达式）", "@markdownCustomSeparatorPattern": {"description": "Custom separator pattern label"}, "markdownAppearanceSettings": "外观设置", "@markdownAppearanceSettings": {"description": "Appearance settings section title"}, "markdownBlockSpacing": "分块间距", "@markdownBlockSpacing": {"description": "Block spacing setting"}, "markdownSectionSplitSettings": "段落拆分设置", "@markdownSectionSplitSettings": {"description": "Section split settings title"}, "markdownSplitByHorizontalRule": "按水平分割线拆分", "@markdownSplitByHorizontalRule": {"description": "Split by horizontal rule option"}, "markdownMaxSectionLength": "最大段落长度", "@markdownMaxSectionLength": {"description": "Maximum section length setting"}, "commonUnlimited": "不限制", "@commonUnlimited": {"description": "Unlimited option"}, "markdownSetMaxSectionLength": "设置最大段落长度", "@markdownSetMaxSectionLength": {"description": "Set maximum section length dialog title"}, "markdownMaxCharacters": "最大字符数", "@markdownMaxCharacters": {"description": "Maximum characters field label"}, "markdownLeaveEmptyUnlimited": "留空表示不限制", "@markdownLeaveEmptyUnlimited": {"description": "Helper text for unlimited characters"}, "templateSimpleName": "简约", "@templateSimpleName": {"description": "Simple template name"}, "templateSimpleDescription": "简洁优雅的设计风格", "@templateSimpleDescription": {"description": "Simple template description"}, "templateModernName": "现代", "@templateModernName": {"description": "Modern template name"}, "templateModernDescription": "现代设计风格，带有阴影效果", "@templateModernDescription": {"description": "Modern template description"}, "templateElegantName": "优雅", "@templateElegantName": {"description": "Elegant template name"}, "templateElegantDescription": "优雅设计风格，带有细边框", "@templateElegantDescription": {"description": "Elegant template description"}, "templateCodeName": "代码", "@templateCodeName": {"description": "Code template name"}, "templateCodeDescription": "深色主题，适合代码展示", "@templateCodeDescription": {"description": "Code template description"}, "templateCardName": "卡片", "@templateCardName": {"description": "Card template name"}, "templateCardDescription": "社交媒体卡片风格设计", "@templateCardDescription": {"description": "Card template description"}, "templateMorandiName": "莫兰迪", "@templateMorandiName": {"description": "Morandi template name"}, "templateMorandiDescription": "高级莫兰迪色系，柔和优雅", "@templateMorandiDescription": {"description": "Morandi template description"}, "templateChineseBlueName": "青花", "@templateChineseBlueName": {"description": "Chinese blue template name"}, "templateChineseBlueDescription": "传统中国青花瓷色彩与图案设计", "@templateChineseBlueDescription": {"description": "Chinese blue template description"}, "templateChineseVermilionName": "中国红", "@templateChineseVermilionName": {"description": "Chinese vermilion template name"}, "templateChineseVermilionDescription": "传统中国朱红色彩，庄重典雅", "@templateChineseVermilionDescription": {"description": "Chinese vermilion template description"}, "templateGradientPurpleName": "渐变紫", "@templateGradientPurpleName": {"description": "Gradient purple template name"}, "templateGradientPurpleDescription": "现代紫蓝渐变背景，时尚优雅", "@templateGradientPurpleDescription": {"description": "Gradient purple template description"}, "templateFestiveRedName": "喜庆红", "@templateFestiveRedName": {"description": "Festive red template name"}, "templateFestiveRedDescription": "喜庆主题，适合春节等场合", "@templateFestiveRedDescription": {"description": "Festive red template description"}, "templateBambooSlipName": "竹简", "@templateBambooSlipName": {"description": "Bamboo slip template name"}, "templateBambooSlipDescription": "传统竹简风格，富有古韵", "@templateBambooSlipDescription": {"description": "Bamboo slip template description"}, "watermarkPositionTopLeft": "左上", "@watermarkPositionTopLeft": {"description": "Watermark position top left"}, "watermarkPositionTopCenter": "上中", "@watermarkPositionTopCenter": {"description": "Watermark position top center"}, "watermarkPositionTopRight": "右上", "@watermarkPositionTopRight": {"description": "Watermark position top right"}, "watermarkPositionBottomLeft": "左下", "@watermarkPositionBottomLeft": {"description": "Watermark position bottom left"}, "watermarkPositionBottomCenter": "下中", "@watermarkPositionBottomCenter": {"description": "Watermark position bottom center"}, "watermarkPositionBottomRight": "右下", "@watermarkPositionBottomRight": {"description": "Watermark position bottom right"}, "watermarkPositionTiled": "平铺", "@watermarkPositionTiled": {"description": "Watermark position tiled"}, "markdownEnterContentFirst": "请先输入Markdown内容", "@markdownEnterContentFirst": {"description": "Error message for empty content"}, "markdownSplitSuccess": "成功拆分为{count}个段落", "@markdownSplitSuccess": {"description": "Success message for splitting sections", "placeholders": {"count": {"type": "int", "description": "Number of sections"}}}, "markdownSplitError": "拆分段落时出错：{error}", "@markdownSplitError": {"description": "Error message for splitting sections", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownNoSectionsToPreview": "没有可预览的段落", "@markdownNoSectionsToPreview": {"description": "Error message for no sections"}, "markdownSplitContentFirst": "请先拆分Markdown内容", "@markdownSplitContentFirst": {"description": "Error message for unsplitted content"}, "markdownDocumentSaveSuccess": "文档保存成功", "@markdownDocumentSaveSuccess": {"description": "Success message for saving document"}, "markdownDocumentSaveError": "保存文档失败：{error}", "@markdownDocumentSaveError": {"description": "Error message for saving document", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "errorStoragePermissionRequired": "需要存储权限才能保存图片", "@errorStoragePermissionRequired": {"description": "Storage permission error message"}, "markdownNoContentToPreview": "没有内容可预览", "@markdownNoContentToPreview": {"description": "No content to preview message"}, "markdownImageGenerationFailed": "图片生成失败，无法分享", "@markdownImageGenerationFailed": {"description": "Image generation failed message"}, "markdownShareError": "分享失败：{error}", "@markdownShareError": {"description": "Share error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownEnableBlockModeFirst": "请先启用分块模式", "@markdownEnableBlockModeFirst": {"description": "Error message for disabled block mode"}, "markdownNoBlocks": "暂无分块", "@markdownNoBlocks": {"description": "No blocks message"}, "markdownEnableBlockRenderToList": "启用分块渲染以查看分块列表", "@markdownEnableBlockRenderToList": {"description": "Helper text for enabling block rendering"}, "commonContentCopied": "内容已复制到剪贴板", "@commonContentCopied": {"description": "Content copied message"}, "markdownResetDemo": "重置演示", "@markdownResetDemo": {"description": "Reset demo button text"}, "commonHelp": "帮助", "@commonHelp": {"description": "Help button text"}, "markdownBlockRenderHelp": "分块渲染帮助", "@markdownBlockRenderHelp": {"description": "Block render help title"}, "markdownFeatureDescription": "功能说明：", "@markdownFeatureDescription": {"description": "Feature description label"}, "markdownOperationMethod": "操作方法：", "@markdownOperationMethod": {"description": "Operation method label"}, "commonTips": "提示：", "@commonTips": {"description": "Tips label"}, "markdownSectionSettings": "段落设置", "@markdownSectionSettings": {"description": "Section settings tooltip"}, "markdownSelectTemplate": "选择模板", "@markdownSelectTemplate": {"description": "Select template tooltip"}, "markdownSelectHtmlTemplate": "选择HTML模板", "@markdownSelectHtmlTemplate": {"description": "Select HTML template title"}, "commonPreview": "预览", "@commonPreview": {"description": "Preview button text"}, "markdownSaveImage": "保存图片", "@markdownSaveImage": {"description": "Save image button text in markdown module"}, "commonShare": "分享", "@commonShare": {"description": "Share button text"}, "commonShowAll": "显示全部", "@commonShowAll": {"description": "Show all filter option"}, "commonShowVisibleOnly": "仅显示可见", "@commonShowVisibleOnly": {"description": "Show visible only filter option"}, "commonSortByIndex": "按索引排序", "@commonSortByIndex": {"description": "Sort by index option"}, "commonSortByTitle": "按标题排序", "@commonSortByTitle": {"description": "Sort by title option"}, "commonSortByType": "按类型排序", "@commonSortByType": {"description": "Sort by type option"}, "commonSortByLength": "按长度排序", "@commonSortByLength": {"description": "Sort by length option"}, "markdownBlockCount": "{count}个分块", "@markdownBlockCount": {"description": "Block count text", "placeholders": {"count": {"type": "int", "description": "Number of blocks"}}}, "commonCharacterCount": "{count}个字符", "@commonCharacterCount": {"description": "Character count text", "placeholders": {"count": {"type": "int", "description": "Number of characters"}}}, "markdownSelectedBlockCount": "已选择{count}个分块", "@markdownSelectedBlockCount": {"description": "Selected block count text", "placeholders": {"count": {"type": "int", "description": "Number of selected blocks"}}}, "commonTotal": "总计", "@commonTotal": {"description": "Total label"}, "commonVisible": "可见", "@commonVisible": {"description": "Visible label"}, "commonHidden": "隐藏", "@commonHidden": {"description": "Hidden label"}, "markdownContentPlaceholder": "在这里输入Markdown内容...", "@markdownContentPlaceholder": {"description": "Markdown content placeholder"}, "markdownClickSplitButton": "点击拆分按钮将Markdown拆分为段落", "@markdownClickSplitButton": {"description": "Helper text for splitting content"}, "markdownHorizontalRuleHelper": "遇到三个以上的-、*或_符号时拆分", "@markdownHorizontalRuleHelper": {"description": "Horizontal rule helper text"}, "markdownH1SplitHelper": "遇到#一级标题时拆分", "@markdownH1SplitHelper": {"description": "H1 split helper text"}, "markdownCharacterCount": "{count}个字符", "@markdownCharacterCount": {"description": "Character count text", "placeholders": {"count": {"type": "int", "description": "Number of characters"}}}, "markdownAutoSplitHelper": "将过长的段落自动拆分为多个段落", "@markdownAutoSplitHelper": {"description": "Auto split helper text"}, "markdownSeparatorExample": "示例：三个或更多连续的短横线", "@markdownSeparatorExample": {"description": "Separator example text"}, "markdownH1SeparatorHelper": "使用#一级标题作为分块分隔符", "@markdownH1SeparatorHelper": {"description": "H1 separator helper text"}, "markdownH2SeparatorHelper": "使用##二级标题作为分块分隔符", "@markdownH2SeparatorHelper": {"description": "H2 separator helper text"}, "markdownBlockRenderHelper": "启用后，Markdown内容将按照设定的规则以分块形式显示", "@markdownBlockRenderHelper": {"description": "Block render helper text"}, "markdownExportBlocks": "导出分块", "@markdownExportBlocks": {"description": "Export blocks option in markdown module"}, "markdownGenerateSummary": "生成摘要报告", "@markdownGenerateSummary": {"description": "Generate summary report option in markdown module"}, "markdownImageExportSuccess": "图片已导出：{filePath}", "@markdownImageExportSuccess": {"description": "Image export success message", "placeholders": {"filePath": {"type": "String", "description": "Exported file path"}}}, "markdownExportError": "导出失败：{error}", "@markdownExportError": {"description": "Export error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownMarkdownExportSuccess": "Markdown文件已导出：{filePath}", "@markdownMarkdownExportSuccess": {"description": "Markdown export success message", "placeholders": {"filePath": {"type": "String", "description": "Exported file path"}}}, "markdownSummaryGenerated": "摘要报告已生成：{filePath}", "@markdownSummaryGenerated": {"description": "Summary generated message", "placeholders": {"filePath": {"type": "String", "description": "Generated file path"}}}, "markdownSummaryError": "生成报告失败：{error}", "@markdownSummaryError": {"description": "Summary generation error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownGeneratingImage": "正在生成图片（{current}/{total}）", "@markdownGeneratingImage": {"description": "Generating image progress message", "placeholders": {"current": {"type": "int", "description": "Current page number"}, "total": {"type": "int", "description": "Total pages"}}}, "markdownImagesSavedSuccess": "成功保存{count}张图片到相册", "@markdownImagesSavedSuccess": {"description": "Images saved success message", "placeholders": {"count": {"type": "int", "description": "Number of images saved"}}}, "templateChineseBlueWatermark": "青花", "@templateChineseBlueWatermark": {"description": "Chinese blue template watermark"}, "templateChineseVermilionWatermark": "赤", "@templateChineseVermilionWatermark": {"description": "Chinese vermilion template watermark"}, "templateFestiveRedWatermark": "福", "@templateFestiveRedWatermark": {"description": "Festive red template watermark"}, "templateBambooSlipWatermark": "竹", "@templateBambooSlipWatermark": {"description": "Bamboo slip template watermark"}, "markdownBlockManagement": "分块管理", "@markdownBlockManagement": {"description": "Block management title"}, "markdownExportOptions": "导出选项", "@markdownExportOptions": {"description": "Export options title in markdown module"}, "commonDeselect": "取消选择", "@commonDeselect": {"description": "Deselect button text"}, "commonSelect": "选择", "@commonSelect": {"description": "Select button text"}, "commonLoading": "加载中...", "@commonLoading": {"description": "Loading text"}, "commonConfirm": "确认", "@commonConfirm": {"description": "Confirm button text"}, "commonEdit": "编辑", "@commonEdit": {"description": "Edit button text"}, "commonDelete": "删除", "@commonDelete": {"description": "Delete button text"}, "commonAdd": "添加", "@commonAdd": {"description": "Add button text"}, "commonRemove": "移除", "@commonRemove": {"description": "Remove button text"}, "commonApply": "应用", "@commonApply": {"description": "Apply button text"}, "commonClose": "关闭", "@commonClose": {"description": "Close button text"}, "commonOpen": "打开", "@commonOpen": {"description": "Open button text"}, "commonView": "查看", "@commonView": {"description": "View button text"}, "commonBrowse": "浏览", "@commonBrowse": {"description": "Browse button text"}, "commonSearch": "搜索", "@commonSearch": {"description": "Search button text"}, "commonFilter": "筛选", "@commonFilter": {"description": "Filter button text"}, "commonSort": "排序", "@commonSort": {"description": "Sort button text"}, "commonRefresh": "刷新", "@commonRefresh": {"description": "Refresh button text"}, "commonReload": "重新加载", "@commonReload": {"description": "Reload button text"}, "commonRetry": "重试", "@commonRetry": {"description": "Retry button text"}, "commonContinue": "继续", "@commonContinue": {"description": "Continue button text"}, "commonFinish": "完成", "@commonFinish": {"description": "Finish button text"}, "commonSkip": "跳过", "@commonSkip": {"description": "Skip button text"}, "commonBack": "返回", "@commonBack": {"description": "Back button text"}, "commonNext": "下一步", "@commonNext": {"description": "Next button text"}, "commonPrevious": "上一步", "@commonPrevious": {"description": "Previous button text"}, "commonDone": "完成", "@commonDone": {"description": "Done button text"}, "commonStart": "开始", "@commonStart": {"description": "Start button text"}, "commonStop": "停止", "@commonStop": {"description": "Stop button text"}, "commonPause": "暂停", "@commonPause": {"description": "Pause button text"}, "commonResume": "恢复", "@commonResume": {"description": "Resume button text"}, "commonPlay": "播放", "@commonPlay": {"description": "Play button text"}, "commonMute": "静音", "@commonMute": {"description": "Mute button text"}, "commonUnmute": "取消静音", "@commonUnmute": {"description": "Unmute button text"}, "commonVolumeUp": "增加音量", "@commonVolumeUp": {"description": "Volume up button text"}, "commonVolumeDown": "减少音量", "@commonVolumeDown": {"description": "Volume down button text"}, "commonFullscreen": "全屏", "@commonFullscreen": {"description": "Fullscreen button text"}, "commonExitFullscreen": "退出全屏", "@commonExitFullscreen": {"description": "Exit fullscreen button text"}, "commonZoomIn": "放大", "@commonZoomIn": {"description": "Zoom in button text"}, "commonZoomOut": "缩小", "@commonZoomOut": {"description": "Zoom out button text"}, "commonZoomReset": "重置缩放", "@commonZoomReset": {"description": "Zoom reset button text"}, "commonRotateLeft": "向左旋转", "@commonRotateLeft": {"description": "Rotate left button text"}, "commonRotateRight": "向右旋转", "@commonRotateRight": {"description": "Rotate right button text"}, "commonFlipHorizontal": "水平翻转", "@commonFlipHorizontal": {"description": "Flip horizontal button text"}, "commonFlipVertical": "垂直翻转", "@commonFlipVertical": {"description": "Flip vertical button text"}, "commonCrop": "裁剪", "@commonCrop": {"description": "Crop button text"}, "commonResize": "调整大小", "@commonResize": {"description": "Resize button text"}, "commonRotate": "旋转", "@commonRotate": {"description": "Rotate button text"}, "commonFlip": "翻转", "@commonFlip": {"description": "Flip button text"}, "commonMirror": "镜像", "@commonMirror": {"description": "Mirror button text"}, "commonSkew": "倾斜", "@commonSkew": {"description": "Skew button text"}, "commonDistort": "扭曲", "@commonDistort": {"description": "Distort button text"}, "commonBlur": "模糊", "@commonBlur": {"description": "Blur button text"}, "commonSharpen": "锐化", "@commonSharpen": {"description": "Sharpen button text"}, "commonBrightness": "亮度", "@commonBrightness": {"description": "Brightness button text"}, "commonContrast": "对比度", "@commonContrast": {"description": "Contrast button text"}, "commonSaturation": "饱和度", "@commonSaturation": {"description": "Saturation button text"}, "commonHue": "色相", "@commonHue": {"description": "Hue button text"}, "commonGamma": "伽马", "@commonGamma": {"description": "Gamma button text"}, "commonExposure": "曝光", "@commonExposure": {"description": "Exposure button text"}, "commonVignette": "暗角", "@commonVignette": {"description": "Vignette button text"}, "commonGrain": "颗粒", "@commonGrain": {"description": "Grain button text"}, "commonNoise": "噪点", "@commonNoise": {"description": "Noise button text"}, "commonPixelate": "像素化", "@commonPixelate": {"description": "Pixelate button text"}, "commonPosterize": "色调分离", "@commonPosterize": {"description": "Posterize button text"}, "commonDither": "抖动", "@commonDither": {"description": "Dither button text"}, "commonThreshold": "阈值", "@commonThreshold": {"description": "Threshold button text"}, "commonQuantize": "量化", "@commonQuantize": {"description": "Quantize button text"}, "commonDesaturate": "去饱和", "@commonDesaturate": {"description": "Desaturate button text"}, "commonSaturate": "饱和", "@commonSaturate": {"description": "Saturate button text"}, "commonInvert": "反色", "@commonInvert": {"description": "Invert button text"}, "commonGrayscale": "灰度", "@commonGrayscale": {"description": "Grayscale button text"}, "commonSepia": "褐色", "@commonSepia": {"description": "Sepia button text"}, "commonVintage": "复古", "@commonVintage": {"description": "Vintage button text"}, "commonRetro": "怀旧", "@commonRetro": {"description": "Retro button text"}, "commonBlackAndWhite": "黑白", "@commonBlackAndWhite": {"description": "Black and white button text"}, "commonCool": "冷色", "@commonCool": {"description": "Cool button text"}, "commonWarm": "暖色", "@commonWarm": {"description": "Warm button text"}, "commonFade": "褪色", "@commonFade": {"description": "Fade button text"}, "commonDuotone": "双色调", "@commonDuotone": {"description": "Duotone button text"}, "commonTricolor": "三色调", "@commonTricolor": {"description": "Tricolor button text"}, "commonMonochrome": "单色", "@commonMonochrome": {"description": "Monochrome button text"}, "commonPolychrome": "多色", "@commonPolychrome": {"description": "Polychrome button text"}, "commonRainbow": "彩虹", "@commonRainbow": {"description": "Rainbow button text"}, "commonGradient": "渐变", "@commonGradient": {"description": "Gradient button text"}, "commonPattern": "图案", "@commonPattern": {"description": "Pattern button text"}, "commonTexture": "纹理", "@commonTexture": {"description": "Texture button text"}, "commonBorder": "边框", "@commonBorder": {"description": "Border button text"}, "commonFrame": "框架", "@commonFrame": {"description": "Frame button text"}, "commonShadow": "阴影", "@commonShadow": {"description": "Shadow button text"}, "commonGlow": "发光", "@commonGlow": {"description": "Glow button text"}, "commonNeon": "霓虹", "@commonNeon": {"description": "Neon button text"}, "commonLight": "亮光", "@commonLight": {"description": "Light button text"}, "commonDark": "暗光", "@commonDark": {"description": "Dark button text"}, "commonBright": "明亮", "@commonBright": {"description": "Bright button text"}, "commonDim": "暗淡", "@commonDim": {"description": "Dim button text"}, "commonClear": "清晰", "@commonClear": {"description": "Clear button text"}, "commonCloudy": "多云", "@commonCloudy": {"description": "Cloudy button text"}, "commonFoggy": "有雾", "@commonFoggy": {"description": "Foggy button text"}, "commonHazy": "朦胧", "@commonHazy": {"description": "Hazy button text"}, "commonSmoky": "烟雾", "@commonSmoky": {"description": "Smoky button text"}, "commonDusty": "灰尘", "@commonDusty": {"description": "Dusty button text"}, "commonMisty": "薄雾", "@commonMisty": {"description": "Misty button text"}, "commonFrosty": "霜冻", "@commonFrosty": {"description": "Frosty button text"}, "commonIcy": "结冰", "@commonIcy": {"description": "Icy button text"}, "commonSnowy": "下雪", "@commonSnowy": {"description": "Snowy button text"}, "commonRainy": "下雨", "@commonRainy": {"description": "Rainy button text"}, "commonStormy": "暴风雨", "@commonStormy": {"description": "Stormy button text"}, "commonWindy": "刮风", "@commonWindy": {"description": "Windy button text"}, "commonBreezy": "微风", "@commonBreezy": {"description": "Breezy button text"}, "commonCalm": "平静", "@commonCalm": {"description": "Calm button text"}, "commonStill": "静止", "@commonStill": {"description": "Still button text"}, "commonQuiet": "安静", "@commonQuiet": {"description": "Quiet button text"}, "commonSilent": "沉默", "@commonSilent": {"description": "Silent button text"}, "commonPeaceful": "和平", "@commonPeaceful": {"description": "Peaceful button text"}, "commonSerene": "宁静", "@commonSerene": {"description": "Serene button text"}, "commonTranquil": "安详", "@commonTranquil": {"description": "Tranquil button text"}, "commonPlacid": "平稳", "@commonPlacid": {"description": "Placid button text"}, "commonSmooth": "平滑", "@commonSmooth": {"description": "Smooth button text"}, "commonRough": "粗糙", "@commonRough": {"description": "Rough button text"}, "commonCoarse": "粗糙", "@commonCoarse": {"description": "Coarse button text"}, "commonFine": "精细", "@commonFine": {"description": "Fine button text"}, "commonSoft": "柔软", "@commonSoft": {"description": "Soft button text"}, "commonHard": "困难", "@commonHard": {"description": "Hard button text"}, "commonTough": "坚韧", "@commonTough": {"description": "Tough button text"}, "commonStrong": "强壮", "@commonStrong": {"description": "Strong button text"}, "commonWeak": "虚弱", "@commonWeak": {"description": "Weak button text"}, "commonGentle": "温柔", "@commonGentle": {"description": "Gentle button text"}, "commonMild": "温和", "@commonMild": {"description": "Mild button text"}, "commonHarsh": "严厉", "@commonHarsh": {"description": "Harsh button text"}, "commonSevere": "严重", "@commonSevere": {"description": "Severe button text"}, "commonExtreme": "极端", "@commonExtreme": {"description": "Extreme button text"}, "commonIntense": "强烈", "@commonIntense": {"description": "Intense button text"}, "commonModerate": "适中", "@commonModerate": {"description": "Moderate button text"}, "commonAverage": "平均", "@commonAverage": {"description": "Average button text"}, "commonNormal": "正常", "@commonNormal": {"description": "Normal button text"}, "commonStandard": "标准", "@commonStandard": {"description": "Standard button text"}, "commonRegular": "常规", "@commonRegular": {"description": "Regular button text"}, "commonTypical": "典型", "@commonTypical": {"description": "Typical button text"}, "commonUsual": "通常", "@commonUsual": {"description": "Usual button text"}, "commonCommon": "常见", "@commonCommon": {"description": "Common button text"}, "commonOrdinary": "普通", "@commonOrdinary": {"description": "Ordinary button text"}, "commonGeneral": "通用", "@commonGeneral": {"description": "General button text"}, "commonBasic": "基本", "@commonBasic": {"description": "Basic button text"}, "commonSimple": "简单", "@commonSimple": {"description": "Simple button text"}, "commonEasy": "容易", "@commonEasy": {"description": "Easy button text"}, "commonDifficult": "困难", "@commonDifficult": {"description": "Difficult button text"}, "commonComplex": "复杂", "@commonComplex": {"description": "Complex button text"}, "commonComplicated": "复杂", "@commonComplicated": {"description": "Complicated button text"}, "commonAdvanced": "高级", "@commonAdvanced": {"description": "Advanced button text"}, "commonExpert": "专家", "@commonExpert": {"description": "Expert button text"}, "commonProfessional": "专业", "@commonProfessional": {"description": "Professional button text"}, "commonSpecialized": "专门", "@commonSpecialized": {"description": "Specialized button text"}, "commonTechnical": "技术", "@commonTechnical": {"description": "Technical button text"}, "commonScientific": "科学", "@commonScientific": {"description": "Scientific button text"}, "commonAcademic": "学术", "@commonAcademic": {"description": "Academic button text"}, "commonEducational": "教育", "@commonEducational": {"description": "Educational button text"}, "commonInstructional": "指导", "@commonInstructional": {"description": "Instructional button text"}, "commonTutorial": "教程", "@commonTutorial": {"description": "Tutorial button text"}, "commonGuide": "引导", "@commonGuide": {"description": "Guide button text"}, "commonManual": "手册", "@commonManual": {"description": "Manual button text"}, "commonHandbook": "手册", "@commonHandbook": {"description": "Handbook button text"}, "commonReference": "参考", "@commonReference": {"description": "Reference button text"}, "commonDocumentation": "文档", "@commonDocumentation": {"description": "Documentation button text"}, "commonSupport": "支持", "@commonSupport": {"description": "Support button text"}, "commonAssistance": "协助", "@commonAssistance": {"description": "Assistance button text"}, "commonAid": "援助", "@commonAid": {"description": "Aid button text"}, "commonService": "服务", "@commonService": {"description": "Service button text"}, "commonMaintenance": "维护", "@commonMaintenance": {"description": "Maintenance button text"}, "commonRepair": "修复", "@commonRepair": {"description": "Repair button text"}, "commonFix": "修复", "@commonFix": {"description": "Fix button text"}, "commonSolve": "解决", "@commonSolve": {"description": "Solve button text"}, "commonResolve": "解决", "@commonResolve": {"description": "Resolve button text"}, "commonAddress": "地址", "@commonAddress": {"description": "Address button text"}, "commonHandle": "处理", "@commonHandle": {"description": "Handle button text"}, "commonManage": "管理", "@commonManage": {"description": "Manage button text"}, "commonControl": "控制", "@commonControl": {"description": "Control button text"}, "commonDirect": "指导", "@commonDirect": {"description": "Direct button text"}, "commonLead": "领导", "@commonLead": {"description": "Lead button text"}, "commonConduct": "进行", "@commonConduct": {"description": "Conduct button text"}, "commonOperate": "操作", "@commonOperate": {"description": "Operate button text"}, "commonRun": "运行", "@commonRun": {"description": "Run button text"}, "commonExecute": "执行", "@commonExecute": {"description": "Execute button text"}, "commonPerform": "执行", "@commonPerform": {"description": "Perform button text"}, "commonImplement": "实施", "@commonImplement": {"description": "Implement button text"}, "commonCarryOut": "执行", "@commonCarryOut": {"description": "Carry out button text"}, "commonAccomplish": "完成", "@commonAccomplish": {"description": "Accomplish button text"}, "commonAchieve": "实现", "@commonAchieve": {"description": "Achieve button text"}, "commonAttain": "达到", "@commonAttain": {"description": "Attain button text"}, "commonReach": "到达", "@commonReach": {"description": "Reach button text"}, "commonObtain": "获得", "@commonObtain": {"description": "Obtain button text"}, "commonGet": "获取", "@commonGet": {"description": "Get button text"}, "commonAcquire": "获取", "@commonAcquire": {"description": "Acquire button text"}, "commonGain": "获得", "@commonGain": {"description": "Gain button text"}, "commonReceive": "接收", "@commonReceive": {"description": "Receive button text"}, "commonCollect": "收集", "@commonCollect": {"description": "Collect button text"}, "commonGather": "聚集", "@commonGather": {"description": "Gather button text"}, "commonAssemble": "组装", "@commonAssemble": {"description": "Assemble button text"}, "commonCompile": "编译", "@commonCompile": {"description": "Compile button text"}, "commonCombine": "结合", "@commonCombine": {"description": "Combine button text"}, "commonMerge": "合并", "@commonMerge": {"description": "Merge button text"}, "commonJoin": "加入", "@commonJoin": {"description": "Join button text"}, "commonUnite": "联合", "@commonUnite": {"description": "Unite button text"}, "commonConnect": "连接", "@commonConnect": {"description": "Connect button text"}, "commonLink": "链接", "@commonLink": {"description": "Link button text"}, "commonAttach": "附加", "@commonAttach": {"description": "Attach button text"}, "commonFasten": "固定", "@commonFasten": {"description": "Fasten button text"}, "commonSecure": "保护", "@commonSecure": {"description": "Secure button text"}, "commonTie": "捆绑", "@commonTie": {"description": "Tie button text"}, "commonBind": "绑定", "@commonBind": {"description": "Bind button text"}, "commonWrap": "包裹", "@commonWrap": {"description": "Wrap button text"}, "commonCover": "覆盖", "@commonCover": {"description": "Cover button text"}, "commonEnclose": "包围", "@commonEnclose": {"description": "Enclose button text"}, "commonSurround": "环绕", "@commonSurround": {"description": "Surround button text"}, "commonEnvelop": "包裹", "@commonEnvelop": {"description": "Envelop button text"}, "commonContain": "包含", "@commonContain": {"description": "Contain button text"}, "commonInclude": "包括", "@commonInclude": {"description": "Include button text"}, "commonInvolve": "涉及", "@commonInvolve": {"description": "Involve button text"}, "commonEmbrace": "拥抱", "@commonEmbrace": {"description": "Embrace button text"}, "commonEncompass": "包含", "@commonEncompass": {"description": "Encompass button text"}, "commonSpan": "跨度", "@commonSpan": {"description": "Span button text"}, "commonExtend": "扩展", "@commonExtend": {"description": "Extend button text"}, "commonStretch": "拉伸", "@commonStretch": {"description": "Stretch button text"}, "commonExpand": "扩展", "@commonExpand": {"description": "Expand button text"}, "commonGrow": "成长", "@commonGrow": {"description": "Grow button text"}, "commonIncrease": "增加", "@commonIncrease": {"description": "Increase button text"}, "commonEnlarge": "放大", "@commonEnlarge": {"description": "Enlarge button text"}, "commonMagnify": "放大", "@commonMagnify": {"description": "Magnify button text"}, "commonAmplify": "放大", "@commonAmplify": {"description": "Amplify button text"}, "commonBoost": "提升", "@commonBoost": {"description": "Boost button text"}, "commonEnhance": "增强", "@commonEnhance": {"description": "Enhance button text"}, "commonImprove": "改进", "@commonImprove": {"description": "Improve button text"}, "commonBetter": "更好", "@commonBetter": {"description": "Better button text"}, "commonUpgrade": "升级", "@commonUpgrade": {"description": "Upgrade button text"}, "commonAdvance": "前进", "@commonAdvance": {"description": "Advance button text"}, "commonProgress": "进步", "@commonProgress": {"description": "Progress button text"}, "commonDevelop": "发展", "@commonDevelop": {"description": "Develop button text"}, "commonEvolve": "进化", "@commonEvolve": {"description": "Evolve button text"}, "commonMature": "成熟", "@commonMature": {"description": "Mature button text"}, "commonRipe": "成熟", "@commonRipe": {"description": "Ripe button text"}, "commonPerfect": "完美", "@commonPerfect": {"description": "Perfect button text"}, "commonComplete": "完成", "@commonComplete": {"description": "Complete button text"}, "commonEnd": "结束", "@commonEnd": {"description": "End button text"}, "commonTerminate": "终止", "@commonTerminate": {"description": "Terminate button text"}, "commonConclude": "总结", "@commonConclude": {"description": "Conclude button text"}, "commonFinalize": "最终确定", "@commonFinalize": {"description": "Finalize button text"}, "commonShut": "关闭", "@commonShut": {"description": "Shut button text"}, "commonSeal": "密封", "@commonSeal": {"description": "Seal button text"}, "commonLock": "锁定", "@commonLock": {"description": "Lock button text"}, "commonTighten": "收紧", "@commonTighten": {"description": "Tighten button text"}, "commonOrganize": "组织", "@commonOrganize": {"description": "Organize button text"}, "commonArrange": "安排", "@commonArrange": {"description": "Arrange button text"}, "commonOrder": "订单", "@commonOrder": {"description": "Order button text"}, "commonClassify": "分类", "@commonClassify": {"description": "Classify button text"}, "commonCategorize": "归类", "@commonCategorize": {"description": "Categorize button text"}, "commonGroup": "分组", "@commonGroup": {"description": "Group button text"}, "commonCluster": "集群", "@commonCluster": {"description": "Cluster button text"}, "commonBunch": "束", "@commonBunch": {"description": "Bunch button text"}, "commonBundle": "捆绑", "@commonBundle": {"description": "Bundle button text"}, "commonPack": "打包", "@commonPack": {"description": "Pack button text"}, "commonPackage": "包装", "@commonPackage": {"description": "Package button text"}, "commonHold": "持有", "@commonHold": {"description": "Hold button text"}, "commonCarry": "携带", "@commonCarry": {"description": "Carry button text"}, "commonBear": "承受", "@commonBear": {"description": "Bear button text"}, "commonSustain": "维持", "@commonSustain": {"description": "Sustain button text"}, "commonMaintain": "维护", "@commonMaintain": {"description": "Maintain button text"}, "commonKeep": "保持", "@commonKeep": {"description": "Keep button text"}, "commonRetain": "保留", "@commonRetain": {"description": "Retain button text"}, "commonPreserve": "保存", "@commonPreserve": {"description": "Preserve button text"}, "commonConserve": "保护", "@commonConserve": {"description": "Conserve button text"}, "commonSave": "保存", "@commonSave": {"description": "Save button text"}, "commonStore": "存储", "@commonStore": {"description": "Store button text"}, "commonReserve": "保留", "@commonReserve": {"description": "Reserve button text"}, "commonSetAside": "留出", "@commonSetAside": {"description": "Set aside button text"}, "commonPutAway": "收起", "@commonPutAway": {"description": "Put away button text"}, "commonPlace": "放置", "@commonPlace": {"description": "Place button text"}, "commonPosition": "位置", "@commonPosition": {"description": "Position button text"}, "commonLocate": "定位", "@commonLocate": {"description": "Locate button text"}, "commonSituate": "安置", "@commonSituate": {"description": "Situate button text"}, "commonInstall": "安装", "@commonInstall": {"description": "Install button text"}, "commonSet": "设置", "@commonSet": {"description": "Set button text"}, "commonEstablish": "建立", "@commonEstablish": {"description": "Establish button text"}, "commonFound": "建立", "@commonFound": {"description": "Found button text"}, "commonCreate": "创建", "@commonCreate": {"description": "Create button text"}, "commonMake": "制作", "@commonMake": {"description": "Make button text"}, "commonBuild": "构建", "@commonBuild": {"description": "Build button text"}, "commonConstruct": "构建", "@commonConstruct": {"description": "Construct button text"}, "commonForm": "形成", "@commonForm": {"description": "Form button text"}, "commonShape": "塑造", "@commonShape": {"description": "Shape button text"}, "commonMold": "塑造", "@commonMold": {"description": "Mold button text"}, "commonFashion": "时尚", "@commonFashion": {"description": "Fashion button text"}, "commonDesign": "设计", "@commonDesign": {"description": "Design button text"}, "commonPlan": "计划", "@commonPlan": {"description": "Plan button text"}, "commonDevise": "设计", "@commonDevise": {"description": "Devise button text"}, "commonConceive": "构思", "@commonConceive": {"description": "Conceive button text"}, "commonImagine": "想象", "@commonImagine": {"description": "Imagine button text"}, "commonEnvision": "设想", "@commonEnvision": {"description": "Envision button text"}, "commonVisualize": "可视化", "@commonVisualize": {"description": "Visualize button text"}, "commonDream": "梦想", "@commonDream": {"description": "Dream button text"}, "commonThink": "思考", "@commonThink": {"description": "Think button text"}, "commonConsider": "考虑", "@commonConsider": {"description": "Consider button text"}, "commonPonder": "思考", "@commonPonder": {"description": "Ponder button text"}, "commonReflect": "反思", "@commonReflect": {"description": "Reflect button text"}, "commonMeditate": "冥想", "@commonMeditate": {"description": "Meditate button text"}, "commonContemplate": "沉思", "@commonContemplate": {"description": "Contemplate button text"}, "commonStudy": "学习", "@commonStudy": {"description": "Study button text"}, "commonLearn": "学习", "@commonLearn": {"description": "Learn button text"}, "commonDiscover": "发现", "@commonDiscover": {"description": "Discover button text"}, "commonFind": "查找", "@commonFind": {"description": "Find button text"}, "commonUncover": "揭露", "@commonUncover": {"description": "Uncover button text"}, "commonReveal": "揭示", "@commonReveal": {"description": "Reveal button text"}, "commonExpose": "暴露", "@commonExpose": {"description": "Expose button text"}, "commonDisclose": "披露", "@commonDisclose": {"description": "Disclose button text"}, "commonDivulge": "泄露", "@commonDivulge": {"description": "Divulge button text"}, "commonTell": "告诉", "@commonTell": {"description": "Tell button text"}, "commonInform": "通知", "@commonInform": {"description": "Inform button text"}, "commonNotify": "通知", "@commonNotify": {"description": "Notify button text"}, "commonAnnounce": "宣布", "@commonAnnounce": {"description": "Announce button text"}, "commonDeclare": "声明", "@commonDeclare": {"description": "Declare button text"}, "commonProclaim": "宣告", "@commonProclaim": {"description": "Proclaim button text"}, "commonPronounce": "发音", "@commonPronounce": {"description": "Pronounce button text"}, "commonState": "状态", "@commonState": {"description": "State button text"}, "commonExpress": "表达", "@commonExpress": {"description": "Express button text"}, "commonVoice": "声音", "@commonVoice": {"description": "Voice button text"}, "commonArticulate": "清晰表达", "@commonArticulate": {"description": "Articulate button text"}, "commonUtter": "发出", "@commonUtter": {"description": "Utter button text"}, "commonSay": "说", "@commonSay": {"description": "Say button text"}, "commonSpeak": "说话", "@commonSpeak": {"description": "Speak button text"}, "commonTalk": "谈话", "@commonTalk": {"description": "Talk button text"}, "commonConverse": "交谈", "@commonConverse": {"description": "Converse button text"}, "commonCommunicate": "交流", "@commonCommunicate": {"description": "Communicate button text"}, "commonCorrespond": "对应", "@commonCorrespond": {"description": "Correspond button text"}, "commonContact": "联系", "@commonContact": {"description": "Contact button text"}, "commonApproach": "接近", "@commonApproach": {"description": "Approach button text"}, "commonAccost": "搭讪", "@commonAccost": {"description": "Accost button text"}, "commonGreet": "问候", "@commonGreet": {"description": "Greet button text"}, "commonWelcome": "欢迎", "@commonWelcome": {"description": "Welcome button text"}, "commonAccept": "接受", "@commonAccept": {"description": "Accept button text"}, "commonTake": "拿", "@commonTake": {"description": "Take button text"}, "commonProcure": "获得", "@commonProcure": {"description": "Procure button text"}, "commonPurchase": "购买", "@commonPurchase": {"description": "Purchase button text"}, "commonBuy": "购买", "@commonBuy": {"description": "Buy button text"}, "commonShop": "购物", "@commonShop": {"description": "Shop button text"}, "commonTrade": "交易", "@commonTrade": {"description": "Trade button text"}, "commonExchange": "交换", "@commonExchange": {"description": "Exchange button text"}, "commonSwap": "交换", "@commonSwap": {"description": "Swap button text"}, "commonSwitch": "切换", "@commonSwitch": {"description": "Switch button text"}, "commonChange": "改变", "@commonChange": {"description": "Change button text"}, "commonAlter": "改变", "@commonAlter": {"description": "Alter button text"}, "commonModify": "修改", "@commonModify": {"description": "Modify button text"}, "commonAdjust": "调整", "@commonAdjust": {"description": "Adjust button text"}, "commonTweak": "微调", "@commonTweak": {"description": "Tweak button text"}, "commonFineTune": "微调", "@commonFineTune": {"description": "Fine tune button text"}, "commonOptimize": "优化", "@commonOptimize": {"description": "Optimize button text"}, "commonRefine": "精炼", "@commonRefine": {"description": "Refine button text"}, "commonPolish": "润色", "@commonPolish": {"description": "Polish button text"}, "commonCease": "停止", "@commonCease": {"description": "Cease button text"}, "commonHalt": "停止", "@commonHalt": {"description": "Halt button text"}, "commonBreak": "中断", "@commonBreak": {"description": "Break button text"}, "commonInterrupt": "中断", "@commonInterrupt": {"description": "Interrupt button text"}, "commonSuspend": "暂停", "@commonSuspend": {"description": "Suspend button text"}, "commonDelay": "延迟", "@commonDelay": {"description": "Delay button text"}, "commonPostpone": "推迟", "@commonPostpone": {"description": "Postpone button text"}, "commonDefer": "推迟", "@commonDefer": {"description": "Defer button text"}, "commonWait": "等待", "@commonWait": {"description": "Wait button text"}, "commonRemain": "保持", "@commonRemain": {"description": "Remain button text"}, "commonStay": "停留", "@commonStay": {"description": "Stay button text"}, "commonProceed": "继续", "@commonProceed": {"description": "Proceed button text"}, "commonMove": "移动", "@commonMove": {"description": "Move button text"}, "commonGo": "去", "@commonGo": {"description": "Go button text"}, "commonTravel": "旅行", "@commonTravel": {"description": "Travel button text"}, "commonJourney": "旅程", "@commonJourney": {"description": "Journey button text"}, "commonPass": "通过", "@commonPass": {"description": "Pass button text"}, "commonCross": "穿过", "@commonCross": {"description": "Cross button text"}, "commonTransit": "运输", "@commonTransit": {"description": "Transit button text"}, "commonTransfer": "转移", "@commonTransfer": {"description": "Transfer button text"}, "commonConvey": "传达", "@commonConvey": {"description": "Convey button text"}, "commonTransport": "运输", "@commonTransport": {"description": "Transport button text"}, "commonBring": "带来", "@commonBring": {"description": "Bring button text"}, "commonFetch": "取", "@commonFetch": {"description": "Fetch button text"}, "commonSalute": "致敬", "@commonSalute": {"description": "Salute button text"}, "commonHail": "欢呼", "@commonHail": {"description": "Hail button text"}, "commonNigh": "接近", "@commonNigh": {"description": "Nigh button text"}, "commonDrawNear": "靠近", "@commonDrawNear": {"description": "Draw near button text"}, "commonCome": "来", "@commonCome": {"description": "Come button text"}, "commonArrive": "到达", "@commonArrive": {"description": "Arrive button text"}, "commonLand": "着陆", "@commonLand": {"description": "Land button text"}, "commonEnter": "进入", "@commonEnter": {"description": "Enter button text"}, "commonAccess": "访问", "@commonAccess": {"description": "Access button text"}, "markdownSaveButton": "保存", "@markdownSaveButton": {"description": "Save button text in markdown module"}, "markdownCancelButton": "取消", "@markdownCancelButton": {"description": "Cancel button text in markdown module"}, "markdownConfirmButton": "确认", "@markdownConfirmButton": {"description": "Confirm button text in markdown module"}, "markdownResetButton": "重置", "@markdownResetButton": {"description": "Reset button text in markdown module"}, "markdownApplyButton": "应用", "@markdownApplyButton": {"description": "Apply button text in markdown module"}, "markdownCloseButton": "关闭", "@markdownCloseButton": {"description": "Close button text in markdown module"}, "markdownSelectButton": "选择", "@markdownSelectButton": {"description": "Select button text in markdown module"}, "markdownBrowseButton": "浏览", "@markdownBrowseButton": {"description": "Browse button text in markdown module"}, "markdownSearchButton": "搜索", "@markdownSearchButton": {"description": "Search button text in markdown module"}, "markdownClearButton": "清除", "@markdownClearButton": {"description": "Clear button text in markdown module"}, "markdownDeleteButton": "删除", "@markdownDeleteButton": {"description": "Delete button text in markdown module"}, "markdownEditButton": "编辑", "@markdownEditButton": {"description": "Edit button text in markdown module"}, "markdownExportButton": "导出", "@markdownExportButton": {"description": "Export button text in markdown module"}, "markdownImportButton": "导入", "@markdownImportButton": {"description": "Import button text in markdown module"}, "markdownShareButton": "分享", "@markdownShareButton": {"description": "Share button text in markdown module"}, "markdownCopyButton": "复制", "@markdownCopyButton": {"description": "Copy button text in markdown module"}, "markdownPasteButton": "粘贴", "@markdownPasteButton": {"description": "Paste button text in markdown module"}, "markdownCutButton": "剪切", "@markdownCutButton": {"description": "Cut button text in markdown module"}, "markdownUndoButton": "撤销", "@markdownUndoButton": {"description": "Undo button text in markdown module"}, "markdownRedoButton": "重做", "@markdownRedoButton": {"description": "Redo button text in markdown module"}, "markdownEditTab": "编辑", "@markdownEditTab": {"description": "Edit tab label in markdown editor"}, "markdownTemplateTab": "模板", "@markdownTemplateTab": {"description": "Template tab label in markdown editor"}, "markdownStyleTab": "样式", "@markdownStyleTab": {"description": "Style tab label in markdown editor"}, "markdownWatermarkTab": "水印", "@markdownWatermarkTab": {"description": "Watermark tab label in markdown editor"}, "markdownBlockTab": "分块", "@markdownBlockTab": {"description": "Block tab label in markdown editor"}, "markdownTemplateSelector": "模板选择器", "@markdownTemplateSelector": {"description": "Template selector title in markdown module"}, "markdownStyleSelector": "样式选择器", "@markdownStyleSelector": {"description": "Style selector title in markdown module"}, "markdownWatermarkSettings": "水印设置", "@markdownWatermarkSettings": {"description": "Watermark settings title in markdown module"}, "markdownBlockSettings": "分块设置", "@markdownBlockSettings": {"description": "Block settings title in markdown module"}, "markdownBlockConfigPanel": "分块配置面板", "@markdownBlockConfigPanel": {"description": "Block configuration panel title in markdown module"}, "markdownBlockManagerPanel": "分块管理面板", "@markdownBlockManagerPanel": {"description": "Block management panel title in markdown module"}, "markdownTextLabel": "文本", "@markdownTextLabel": {"description": "Text field label in markdown module"}, "markdownMarkdownContent": "Markdown内容", "@markdownMarkdownContent": {"description": "Markdown content field label in markdown module"}, "markdownWatermarkText": "水印文本", "@markdownWatermarkText": {"description": "Watermark text field label in markdown module"}, "markdownEnterWatermarkText": "请输入水印文本", "@markdownEnterWatermarkText": {"description": "Watermark text input hint in markdown module"}, "markdownEnterMarkdownContent": "请输入Markdown内容...", "@markdownEnterMarkdownContent": {"description": "Markdown content input hint in markdown module"}, "markdownFontSettings": "字体设置", "@markdownFontSettings": {"description": "Font settings section title in markdown module"}, "markdownFontSize": "字体大小", "@markdownFontSize": {"description": "Font size label in markdown module"}, "markdownFontFamily": "字体族", "@markdownFontFamily": {"description": "Font family label in markdown module"}, "markdownCodeFont": "代码字体", "@markdownCodeFont": {"description": "Code font label in markdown module"}, "markdownColorSettings": "颜色设置", "@markdownColorSettings": {"description": "Color settings section title in markdown module"}, "markdownTextColor": "文字颜色", "@markdownTextColor": {"description": "Text color label in markdown module"}, "markdownBackgroundColor": "背景颜色", "@markdownBackgroundColor": {"description": "Background color label in markdown module"}, "markdownBorderColor": "边框颜色", "@markdownBorderColor": {"description": "Border color label in markdown module"}, "markdownBorderWidth": "边框宽度", "@markdownBorderWidth": {"description": "Border width label in markdown module"}, "markdownShadowSettings": "阴影设置", "@markdownShadowSettings": {"description": "Shadow settings section title in markdown module"}, "markdownShadowColor": "阴影颜色", "@markdownShadowColor": {"description": "Shadow color label in markdown module"}, "markdownBorderRadius": "边框圆角", "@markdownBorderRadius": {"description": "Border radius label in markdown module"}, "markdownPadding": "内边距", "@markdownPadding": {"description": "Padding label in markdown module"}, "markdownMargin": "外边距", "@markdownMargin": {"description": "Margin label in markdown module"}, "markdownWatermarkContent": "水印内容", "@markdownWatermarkContent": {"description": "Watermark content section title in markdown module"}, "markdownWatermarkTextStyle": "文字样式", "@markdownWatermarkTextStyle": {"description": "Watermark text style label in markdown module"}, "markdownWatermarkNormal": "普通", "@markdownWatermarkNormal": {"description": "Normal watermark style option in markdown module"}, "markdownWatermarkBold": "粗体", "@markdownWatermarkBold": {"description": "Bold watermark style option in markdown module"}, "markdownWatermarkItalic": "斜体", "@markdownWatermarkItalic": {"description": "Italic watermark style option in markdown module"}, "markdownWatermarkPosition": "显示位置", "@markdownWatermarkPosition": {"description": "Watermark position label in markdown module"}, "markdownWatermarkTextColor": "文本颜色", "@markdownWatermarkTextColor": {"description": "Text color label in watermark settings"}, "markdownWatermarkOpacity": "透明度", "@markdownWatermarkOpacity": {"description": "Opacity slider label in watermark settings"}, "markdownWatermarkFontSize": "字体大小", "@markdownWatermarkFontSize": {"description": "Font size slider label in watermark settings"}, "markdownWatermarkRotation": "旋转角度", "@markdownWatermarkRotation": {"description": "Rotation slider label in watermark settings"}, "markdownWatermarkTileSettings": "平铺设置", "@markdownWatermarkTileSettings": {"description": "Tile settings section title in watermark settings"}, "markdownWatermarkHorizontalSpacing": "水平间距", "@markdownWatermarkHorizontalSpacing": {"description": "Watermark horizontal spacing label in markdown module"}, "markdownWatermarkVerticalSpacing": "垂直间距", "@markdownWatermarkVerticalSpacing": {"description": "Watermark vertical spacing label in markdown module"}, "markdownSelectWatermarkColor": "选择水印颜色", "@markdownSelectWatermarkColor": {"description": "Select watermark color button text in markdown module"}, "markdownResetToAppName": "重置为应用名称", "@markdownResetToAppName": {"description": "Reset to app name button text in markdown module"}, "markdownShowBlockTitle": "显示分块标题", "@markdownShowBlockTitle": {"description": "Show block title option in markdown module"}, "markdownShowBlockBorder": "显示分块边框", "@markdownShowBlockBorder": {"description": "Show block border option in markdown module"}, "markdownSortByIndex": "按索引排序", "@markdownSortByIndex": {"description": "Sort by index option in markdown module"}, "markdownSortByTitle": "按标题排序", "@markdownSortByTitle": {"description": "Sort by title option in markdown module"}, "markdownSortByType": "按类型排序", "@markdownSortByType": {"description": "Sort by type option in markdown module"}, "markdownSortByLength": "按长度排序", "@markdownSortByLength": {"description": "Sort by length option in markdown module"}, "markdownShareResult": "分享结果", "@markdownShareResult": {"description": "Share result button text in markdown module"}, "markdownExportResult": "导出结果", "@markdownExportResult": {"description": "Export result button text in markdown module"}, "markdownSaveSuccess": "保存成功", "@markdownSaveSuccess": {"description": "Save success message in markdown module"}, "markdownSaveFailed": "保存失败", "@markdownSaveFailed": {"description": "Save failed message in markdown module"}, "markdownTemplateDescription": "模板描述", "@markdownTemplateDescription": {"description": "Template description label in markdown module"}, "markdownTemplateFeatures": "模板特性", "@markdownTemplateFeatures": {"description": "Template features label in markdown module"}, "markdownBorderStyle": "边框样式", "@markdownBorderStyle": {"description": "Border style label in markdown module"}, "markdownShadowEffect": "阴影效果", "@markdownShadowEffect": {"description": "Shadow effect label in markdown module"}, "markdownShowHeader": "显示标题", "@markdownShowHeader": {"description": "Show header option in markdown module"}, "markdownInnerShadow": "内阴影", "@markdownInnerShadow": {"description": "Inner shadow option in markdown module"}, "markdownHeadingAlignment": "标题对齐", "@markdownHeadingAlignment": {"description": "Heading alignment label in markdown module"}, "markdownLeftAlign": "左对齐", "@markdownLeftAlign": {"description": "Left align option in markdown module"}, "markdownCenterAlign": "居中对齐", "@markdownCenterAlign": {"description": "Center align option in markdown module"}, "markdownRightAlign": "右对齐", "@markdownRightAlign": {"description": "Right align option in markdown module"}, "markdownGradientBackground": "渐变背景", "@markdownGradientBackground": {"description": "Gradient background option in markdown module"}, "markdownBackgroundPattern": "背景图案", "@markdownBackgroundPattern": {"description": "Background pattern option in markdown module"}, "markdownListItemStyle": "列表项样式", "@markdownListItemStyle": {"description": "List item style label in markdown module"}, "markdownCheckboxStyle": "复选框样式", "@markdownCheckboxStyle": {"description": "Checkbox style label in markdown module"}, "markdownMoreActions": "更多操作", "@markdownMoreActions": {"description": "More actions button tooltip in markdown editor"}, "markdownShareImageSubtitle": "将渲染结果分享给他人", "@markdownShareImageSubtitle": {"description": "Share image subtitle in markdown module"}, "markdownCopyContentSubtitle": "将Markdown文本复制到剪贴板", "@markdownCopyContentSubtitle": {"description": "Copy content subtitle in markdown module"}, "markdownSaveToAlbumSubtitle": "将图片保存到本地相册", "@markdownSaveToAlbumSubtitle": {"description": "Save to album subtitle in markdown module"}, "markdownOperationOptions": "操作选项", "@markdownOperationOptions": {"description": "Operation options title in markdown module"}, "markdownSelectColor": "选择颜色", "@markdownSelectColor": {"description": "Select color button text in markdown module"}, "markdownChooseColor": "选择颜色", "@markdownChooseColor": {"description": "Choose color button text in markdown module"}, "markdownColorPicker": "颜色选择器", "@markdownColorPicker": {"description": "Color picker title in markdown module"}, "markdownResetSettings": "重置设置", "@markdownResetSettings": {"description": "Reset settings button text in markdown module"}, "markdownApplySettings": "应用设置", "@markdownApplySettings": {"description": "Apply settings button text in markdown module"}, "markdownLoading": "加载中...", "@markdownLoading": {"description": "Loading message in markdown module"}, "markdownGenerating": "生成中...", "@markdownGenerating": {"description": "Generating message in markdown module"}, "markdownProcessing": "处理中...", "@markdownProcessing": {"description": "Processing message in markdown module"}, "markdownSaving": "保存中...", "@markdownSaving": {"description": "Saving message in markdown module"}, "markdownExporting": "导出中...", "@markdownExporting": {"description": "Exporting message in markdown module"}, "markdownSharing": "分享中...", "@markdownSharing": {"description": "Sharing message in markdown module"}, "markdownCopying": "复制中...", "@markdownCopying": {"description": "Copying message in markdown module"}, "markdownSuccess": "成功", "@markdownSuccess": {"description": "Success message in markdown module"}, "markdownError": "错误", "@markdownError": {"description": "Error message in markdown module"}, "markdownWarning": "警告", "@markdownWarning": {"description": "Warning message in markdown module"}, "markdownInfo": "信息", "@markdownInfo": {"description": "Info message in markdown module"}, "markdownComplete": "完成", "@markdownComplete": {"description": "Complete message in markdown module"}, "markdownFailed": "失败", "@markdownFailed": {"description": "Failed message in markdown module"}, "markdownCancelled": "已取消", "@markdownCancelled": {"description": "Cancelled message in markdown module"}, "markdownContentSaved": "内容已保存到内容库", "@markdownContentSaved": {"description": "Content saved message in markdown module"}, "markdownTemplateSelected": "已选择模板 \"{name}\"", "@markdownTemplateSelected": {"description": "Template selected message in markdown module", "placeholders": {"name": {"type": "String", "description": "Template name"}}}, "markdownSaveError": "保存失败：{error}", "@markdownSaveError": {"description": "Save error message in markdown module", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownLoadError": "加载错误：{error}", "@markdownLoadError": {"description": "Load error message in markdown module", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownProcessError": "处理错误：{error}", "@markdownProcessError": {"description": "Process error message in markdown module", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownBlockModeEnabled": "分块模式已启用", "@markdownBlockModeEnabled": {"description": "Block mode enabled message in markdown module"}, "markdownBlockModeDisabled": "分块模式已禁用", "@markdownBlockModeDisabled": {"description": "Block mode disabled message in markdown module"}, "markdownBlockAdded": "分块已添加", "@markdownBlockAdded": {"description": "Block added message in markdown module"}, "markdownBlockRemoved": "分块已移除", "@markdownBlockRemoved": {"description": "Block removed message in markdown module"}, "markdownBlockUpdated": "分块已更新", "@markdownBlockUpdated": {"description": "Block updated message in markdown module"}, "markdownBlockHidden": "分块已隐藏", "@markdownBlockHidden": {"description": "Block hidden message in markdown module"}, "markdownBlockShown": "分块已显示", "@markdownBlockShown": {"description": "Block shown message in markdown module"}, "markdownBlockSelected": "分块已选择", "@markdownBlockSelected": {"description": "Block selected message in markdown module"}, "markdownBlockDeselected": "分块已取消选择", "@markdownBlockDeselected": {"description": "Block deselected message in markdown module"}, "markdownBlockMoved": "分块已移动", "@markdownBlockMoved": {"description": "Block moved message in markdown module"}, "markdownBlockResized": "分块已调整大小", "@markdownBlockResized": {"description": "Block resized message in markdown module"}, "markdownBlockReordered": "分块已重新排序", "@markdownBlockReordered": {"description": "Block reordered message in markdown module"}, "markdownBlockExported": "分块已导出", "@markdownBlockExported": {"description": "Block exported message in markdown module"}, "markdownBlockImported": "分块已导入", "@markdownBlockImported": {"description": "Block imported message in markdown module"}, "markdownBlockRenderFeature1": "• 分块渲染可以将长文档拆分为多个独立的分块", "@markdownBlockRenderFeature1": {"description": "Block render feature description 1 in markdown module"}, "markdownBlockRenderFeature2": "• 每个分块可以独立显示或隐藏", "@markdownBlockRenderFeature2": {"description": "Block render feature description 2 in markdown module"}, "markdownBlockRenderFeature3": "• 支持多种分隔方式：标题、自定义分隔符、手动分隔", "@markdownBlockRenderFeature3": {"description": "Block render feature description 3 in markdown module"}, "markdownBlockRenderFeature4": "• 在左侧配置面板中调整分块设置", "@markdownBlockRenderFeature4": {"description": "Block render feature description 4 in markdown module"}, "markdownBlockRenderFeature5": "• 在预览区域点击可以添加新的分隔条", "@markdownBlockRenderFeature5": {"description": "Block render feature description 5 in markdown module"}, "markdownBlockRenderFeature6": "• 拖动分隔条可以重新调整分块位置", "@markdownBlockRenderFeature6": {"description": "Block render feature description 6 in markdown module"}, "markdownBlockRenderFeature7": "• 点击分块标题栏的眼睛图标可以隐藏/显示分块", "@markdownBlockRenderFeature7": {"description": "Block render feature description 7 in markdown module"}, "markdownBlockRenderFeature8": "• 不同类型的分块用不同颜色的边框区分", "@markdownBlockRenderFeature8": {"description": "Block render feature description 8 in markdown module"}, "markdownBlockRenderFeature9": "• 蓝色：H1标题分块", "@markdownBlockRenderFeature9": {"description": "Block render feature description 9 in markdown module"}, "markdownBlockRenderFeature10": "• 绿色：H2标题分块", "@markdownBlockRenderFeature10": {"description": "Block render feature description 10 in markdown module"}, "markdownBlockRenderFeature11": "• 橙色：自定义分隔符分块", "@markdownBlockRenderFeature11": {"description": "Block render feature description 11 in markdown module"}, "markdownBlockRenderFeature12": "• 灰色：手动分隔符分块", "@markdownBlockRenderFeature12": {"description": "Block render feature description 12 in markdown module"}, "markdownGotIt": "知道了", "@markdownGotIt": {"description": "Got it button text in markdown module"}, "markdownIKnow": "我知道了", "@markdownIKnow": {"description": "I know button text in markdown module"}, "markdownUnderstood": "了解了", "@markdownUnderstood": {"description": "Understood button text in markdown module"}, "markdownAlignLeft": "左对齐", "@markdownAlignLeft": {"description": "Left align option in markdown module"}, "markdownAlignCenter": "居中对齐", "@markdownAlignCenter": {"description": "Center align option in markdown module"}, "markdownAlignRight": "右对齐", "@markdownAlignRight": {"description": "Right align option in markdown module"}, "markdownPositionTopLeft": "左上", "@markdownPositionTopLeft": {"description": "Top left position option in markdown module"}, "markdownPositionTopCenter": "上中", "@markdownPositionTopCenter": {"description": "Top center position option in markdown module"}, "markdownPositionTopRight": "右上", "@markdownPositionTopRight": {"description": "Top right position option in markdown module"}, "markdownPositionBottomLeft": "左下", "@markdownPositionBottomLeft": {"description": "Bottom left position option in markdown module"}, "markdownPositionBottomCenter": "下中", "@markdownPositionBottomCenter": {"description": "Bottom center position option in markdown module"}, "markdownPositionBottomRight": "右下", "@markdownPositionBottomRight": {"description": "Bottom right position option in markdown module"}, "markdownPositionTiled": "平铺", "@markdownPositionTiled": {"description": "Tiled position option in markdown module"}, "markdownExportingBlocks": "正在导出分块...", "@markdownExportingBlocks": {"description": "Exporting blocks message in markdown module"}, "markdownGeneratingReport": "正在生成报告...", "@markdownGeneratingReport": {"description": "Generating report message in markdown module"}, "markdownProcessingComplete": "处理完成", "@markdownProcessingComplete": {"description": "Processing complete message in markdown module"}, "markdownOperationSuccessful": "操作成功", "@markdownOperationSuccessful": {"description": "Operation successful message in markdown module"}, "markdownOperationFailed": "操作失败", "@markdownOperationFailed": {"description": "Operation failed message in markdown module"}, "markdownWatermarkVisible": "显示水印", "@markdownWatermarkVisible": {"description": "Show watermark text in markdown module"}, "markdownWatermarkHidden": "隐藏水印", "@markdownWatermarkHidden": {"description": "Hide watermark text in markdown module"}, "markdownWatermarkPositionAppearance": "位置与外观", "@markdownWatermarkPositionAppearance": {"description": "Position and appearance section title in watermark settings"}, "markdownWatermarkDisplayPosition": "显示位置", "@markdownWatermarkDisplayPosition": {"description": "Display position dropdown label in watermark settings"}, "markdownWatermarkHorizontalGap": "水平间距", "@markdownWatermarkHorizontalGap": {"description": "Horizontal gap slider label in watermark settings"}, "markdownWatermarkVerticalGap": "垂直间距", "@markdownWatermarkVerticalGap": {"description": "Vertical gap slider label in watermark settings"}, "markdownWatermarkSelectColor": "选择水印颜色", "@markdownWatermarkSelectColor": {"description": "Color picker dialog title in watermark settings"}, "markdownWatermarkCancel": "取消", "@markdownWatermarkCancel": {"description": "Cancel button text in watermark color picker"}, "markdownWatermarkConfirm": "确定", "@markdownWatermarkConfirm": {"description": "Confirm button text in watermark color picker"}, "voiceInitializationFailed": "初始化失败", "@voiceInitializationFailed": {"description": "Voice module initialization failed message"}, "voicePlayingAllRecordings": "正在播放所有录音", "@voicePlayingAllRecordings": {"description": "Message when playing all voice recordings"}, "voiceMyRecordings": "我的录音", "@voiceMyRecordings": {"description": "Voice recordings list page title"}, "voicePlayAll": "播放所有", "@voicePlayAll": {"description": "Play all recordings tooltip"}, "voiceNoRecordings": "暂无语音记录", "@voiceNoRecordings": {"description": "Empty state message when no recordings exist"}, "voiceStartRecording": "开始录制", "@voiceStartRecording": {"description": "<PERSON><PERSON> to start recording"}, "voiceTapToStartRecording": "点击下方按钮开始录制您的第一条语音", "@voiceTapToStartRecording": {"description": "Instruction text for empty state"}, "voiceConfirmDelete": "确认删除", "@voiceConfirmDelete": {"description": "Delete confirmation dialog title"}, "voiceDeleteConfirmation": "确定要删除这条语音记录吗？", "@voiceDeleteConfirmation": {"description": "Delete confirmation dialog message"}, "voiceCancel": "取消", "@voiceCancel": {"description": "Cancel button text"}, "voiceDelete": "删除", "@voiceDelete": {"description": "Delete button text"}, "voiceRecordingDeleted": "记录已删除", "@voiceRecordingDeleted": {"description": "Message when recording is deleted"}, "voiceTranscriptionContent": "转录内容:", "@voiceTranscriptionContent": {"description": "Transcription content label"}, "voiceToday": "今天", "@voiceToday": {"description": "Today prefix for date formatting"}, "voiceYesterday": "昨天", "@voiceYesterday": {"description": "Yesterday prefix for date formatting"}, "voiceRecording": "录音", "@voiceRecording": {"description": "Recording text"}, "voiceRecordingPageTitle": "语音录制", "@voiceRecordingPageTitle": {"description": "Voice recording page title"}, "voiceRequestPermission": "请求权限", "@voiceRequestPermission": {"description": "Request permission button text"}, "voiceOpenSettings": "打开设置", "@voiceOpenSettings": {"description": "Open settings button text"}, "voiceRecordingInProgress": "正在录音...", "@voiceRecordingInProgress": {"description": "Recording in progress status"}, "voiceReadyToRecord": "准备好开始录音", "@voiceReadyToRecord": {"description": "Ready to record status"}, "voiceStartSpeaking": "请开始说话...", "@voiceStartSpeaking": {"description": "Prompt text to start speaking"}, "voiceClickToStart": "点击开始", "@voiceClickToStart": {"description": "Click to start instruction"}, "voiceClickToStop": "点击停止", "@voiceClickToStop": {"description": "Click to stop instruction"}, "voiceRecordingStopped": "录音已停止", "@voiceRecordingStopped": {"description": "Message when recording is stopped"}, "voiceRecordingFailed": "录音失败", "@voiceRecordingFailed": {"description": "Message when recording fails"}, "voiceStopRecordingFailed": "停止录音失败: {error}", "@voiceStopRecordingFailed": {"description": "Stop recording failure message"}, "voiceRecordingFileInvalid": "录音文件无效，请重试", "@voiceRecordingFileInvalid": {"description": "Invalid recording file message"}, "voiceRecordingFileNotFound": "录音文件不存在，可能录音失败", "@voiceRecordingFileNotFound": {"description": "Message when recording file is not found"}, "voiceSaveRecording": "保存录音", "@voiceSaveRecording": {"description": "Save recording dialog title"}, "voiceTitle": "标题", "@voiceTitle": {"description": "Title field label"}, "voiceDuration": "时长", "@voiceDuration": {"description": "Duration label"}, "voiceTranscription": "转录", "@voiceTranscription": {"description": "Transcription label"}, "voiceRecordingAndTranscriptionSaved": "录音和转录文本已保存", "@voiceRecordingAndTranscriptionSaved": {"description": "Recording and transcription saved message"}, "voiceRecordingSaved": "录音已保存", "@voiceRecordingSaved": {"description": "Recording saved message"}, "voiceSaveRecordingFailed": "保存录音记录失败: {error}", "@voiceSaveRecordingFailed": {"description": "Save recording failure message"}, "voiceNoMicrophonePermission": "没有麦克风权限，无法进行录音", "@voiceNoMicrophonePermission": {"description": "No microphone permission message"}, "voicePermissionRequired": "需要权限", "@voicePermissionRequired": {"description": "Permission dialog title"}, "voicePermissionInstructions": "请按照以下步骤开启权限:", "@voicePermissionInstructions": {"description": "Permission instruction text"}, "voicePermissionStep1": "1. 点击\"去设置\"按钮", "@voicePermissionStep1": {"description": "Permission setup step 1"}, "voicePermissionStep2": "2. 在设置中点击\"隐私与安全性\"", "@voicePermissionStep2": {"description": "Permission setup step 2"}, "voicePermissionStep3": "3. 分别点击\"麦克风\"和\"语音识别\"", "@voicePermissionStep3": {"description": "Permission setup step 3"}, "voicePermissionStep4": "4. 在列表中找到\"内容君\"并开启权限", "@voicePermissionStep4": {"description": "Permission setup step 4"}, "voicePermissionNote": "注意: 如果看不到App，请回到应用中重新点击\"请求权限\"按钮，然后再次查看设置", "@voicePermissionNote": {"description": "Permission setup note"}, "voiceGoToSettings": "去设置", "@voiceGoToSettings": {"description": "Go to settings button text"}, "voiceEnableMicrophonePermission": "请到设置中开启麦克风权限，以便进行录音", "@voiceEnableMicrophonePermission": {"description": "Permission enable instruction"}, "voiceInitializationError": "初始化错误", "@voiceInitializationError": {"description": "Initialization error dialog title"}, "voiceRequestPermissionAgain": "重新请求权限", "@voiceRequestPermissionAgain": {"description": "Request permission again button text"}, "voiceStartRecordingFailed": "开始录音失败", "@voiceStartRecordingFailed": {"description": "Start recording failure message"}, "voiceNeedMicrophonePermission": "需要麦克风权限才能使用录音功能", "@voiceNeedMicrophonePermission": {"description": "Permission requirement message"}, "voiceNeedMicrophonePermissionForRecording": "需要麦克风权限才能进行录音", "@voiceNeedMicrophonePermissionForRecording": {"description": "Permission requirement message for recording"}, "voiceSpeechRecognitionInitFailed": "语音识别初始化失败，请确保允许使用麦克风权限", "@voiceSpeechRecognitionInitFailed": {"description": "Speech recognition initialization failure message"}, "voiceRecordingTitle": "语音录制", "@voiceRecordingTitle": {"description": "Voice recording page title"}, "voicePermissionGuide": "请按照以下步骤开启权限:", "@voicePermissionGuide": {"description": "Permission guide instruction"}, "voicePermissionGuideStep1": "1. 点击\"去设置\"按钮", "@voicePermissionGuideStep1": {"description": "Permission guide step 1"}, "voicePermissionGuideStep2": "2. 在设置中点击\"隐私与安全性\"", "@voicePermissionGuideStep2": {"description": "Permission guide step 2"}, "voicePermissionGuideStep3": "3. 分别点击\"麦克风\"和\"语音识别\"", "@voicePermissionGuideStep3": {"description": "Permission guide step 3"}, "voicePermissionGuideStep4": "4. 在列表中找到\"内容君\"并开启权限", "@voicePermissionGuideStep4": {"description": "Permission guide step 4"}, "voicePermissionGuideNote": "注意: 如果看不到App，请回到应用中重新点击\"请求权限\"按钮，然后再次查看设置", "@voicePermissionGuideNote": {"description": "Permission guide note"}, "voiceRecordingTitleLabel": "标题", "@voiceRecordingTitleLabel": {"description": "Recording title field label"}, "voiceRecordingDuration": "时长: {duration}", "@voiceRecordingDuration": {"description": "Recording duration label"}, "voiceRecordingTranscription": "转录: {transcription}", "@voiceRecordingTranscription": {"description": "Recording transcription label"}, "voiceRecordingFileNotExist": "录音文件不存在，可能录音失败", "@voiceRecordingFileNotExist": {"description": "Recording file not found message"}, "voicePleaseStartSpeaking": "请开始说话...", "@voicePleaseStartSpeaking": {"description": "Please start speaking prompt"}, "voiceClickToStartRecording": "点击下方按钮开始录音\n语音将实时转为文字", "@voiceClickToStartRecording": {"description": "Start recording instruction"}, "voiceSpeechRecognitionInProgress": "语音识别中，实时转录显示在上方", "@voiceSpeechRecognitionInProgress": {"description": "Speech recognition in progress message"}, "voiceSave": "保存", "@voiceSave": {"description": "Save button text"}, "@trafficGuideTitle": {"description": "Traffic guide module title"}, "trafficGuideContentTools": "功能工具", "@trafficGuideContentTools": {"description": "Content tools section title"}, "trafficGuideToolsDescription": "选择合适的工具来创建引流内容", "@trafficGuideToolsDescription": {"description": "Description for content tools section"}, "trafficGuideTextTransformer": "文本转换", "@trafficGuideTextTransformer": {"description": "Text transformer tool title"}, "trafficGuideTextTransformerSubtitle": "Emoji转换和字符干扰", "@trafficGuideTextTransformerSubtitle": {"description": "Subtitle for text transformer"}, "trafficGuideWatermarkProcessor": "水印处理", "@trafficGuideWatermarkProcessor": {"description": "Watermark processor tool title"}, "trafficGuideWatermarkProcessorSubtitle": "添加和移除隐形水印", "@trafficGuideWatermarkProcessorSubtitle": {"description": "Subtitle for watermark processor"}, "trafficGuideNewProject": "新建项目", "@trafficGuideNewProject": {"description": "New project button title"}, "trafficGuideNewProjectSubtitle": "创建引流项目配置", "@trafficGuideNewProjectSubtitle": {"description": "Subtitle for new project"}, "trafficGuideMyProjects": "我的项目", "@trafficGuideMyProjects": {"description": "My projects section title"}, "trafficGuideProjectsDescription": "管理您的引流项目配置", "@trafficGuideProjectsDescription": {"description": "Description for projects section"}, "trafficGuideNoProjects": "暂无项目", "@trafficGuideNoProjects": {"description": "No projects message"}, "trafficGuideNoProjectsDescription": "点击\"新建项目\"开始创建您的第一个引流项目", "@trafficGuideNoProjectsDescription": {"description": "Description for no projects state"}, "trafficGuideRefresh": "刷新", "@trafficGuideRefresh": {"description": "Refresh tooltip"}, "trafficGuideLoading": "加载中...", "@trafficGuideLoading": {"description": "Loading message"}, "trafficGuideLastUpdated": "更新时间: {date}", "@trafficGuideLastUpdated": {"description": "Last updated date format"}, "trafficGuideConfirmDelete": "确认删除", "@trafficGuideConfirmDelete": {"description": "Confirm delete dialog title"}, "trafficGuideDeleteConfirmation": "确定要删除项目\"{name}\"吗？", "@trafficGuideDeleteConfirmation": {"description": "Delete confirmation message"}, "trafficGuideProjectDeleted": "项目\"{name}\"已删除", "@trafficGuideProjectDeleted": {"description": "Project deleted success message"}, "trafficGuideEdit": "编辑", "@trafficGuideEdit": {"description": "Edit action"}, "trafficGuideDelete": "删除", "@trafficGuideDelete": {"description": "Delete action"}, "trafficGuideProjectEditor": "项目编辑", "@trafficGuideProjectEditor": {"description": "Project editor page title"}, "trafficGuideBasicInfo": "基本信息", "@trafficGuideBasicInfo": {"description": "Basic information section title"}, "trafficGuideProjectName": "项目名称", "@trafficGuideProjectName": {"description": "Project name field label"}, "trafficGuideProjectNameHint": "输入项目名称", "@trafficGuideProjectNameHint": {"description": "Project name field hint"}, "trafficGuideProjectDescription": "项目描述", "@trafficGuideProjectDescription": {"description": "Project description field label"}, "trafficGuideProjectDescriptionHint": "输入项目描述", "@trafficGuideProjectDescriptionHint": {"description": "Project description field hint"}, "trafficGuideProjectNameRequired": "请输入项目名称", "@trafficGuideProjectNameRequired": {"description": "Project name validation error"}, "trafficGuideImageConfig": "图片配置", "@trafficGuideImageConfig": {"description": "Image configuration section title"}, "trafficGuideDefaultText": "默认文本", "@trafficGuideDefaultText": {"description": "Default text field label"}, "trafficGuideDefaultTextHint": "输入默认显示的文本", "@trafficGuideDefaultTextHint": {"description": "Default text field hint"}, "trafficGuideFontFamily": "字体", "@trafficGuideFontFamily": {"description": "Font family field label"}, "trafficGuideTextTransformConfig": "文本转换配置", "@trafficGuideTextTransformConfig": {"description": "Text transform configuration section title"}, "trafficGuideEmojiConversion": "Emoji转换", "@trafficGuideEmojiConversion": {"description": "Emoji conversion toggle title"}, "trafficGuideEmojiConversionSubtitle": "将数字和字母转换为特殊Unicode字符", "@trafficGuideEmojiConversionSubtitle": {"description": "Emoji conversion subtitle"}, "trafficGuideUnicodeVariation": "Unicode变体", "@trafficGuideUnicodeVariation": {"description": "Unicode variation toggle title"}, "trafficGuideUnicodeVariationSubtitle": "添加变音字符和特殊Unicode", "@trafficGuideUnicodeVariationSubtitle": {"description": "Unicode variation subtitle"}, "trafficGuideInvisibleChars": "不可见字符", "@trafficGuideInvisibleChars": {"description": "Invisible characters toggle title"}, "trafficGuideInvisibleCharsSubtitle": "在文本中插入不可见字符", "@trafficGuideInvisibleCharsSubtitle": {"description": "Invisible characters subtitle"}, "trafficGuideSensitiveWordMasking": "敏感词干扰", "@trafficGuideSensitiveWordMasking": {"description": "Sensitive word masking toggle title"}, "trafficGuideSensitiveWordMaskingSubtitle": "对敏感词进行字符干扰", "@trafficGuideSensitiveWordMaskingSubtitle": {"description": "Sensitive word masking subtitle"}, "trafficGuideSensitiveWords": "敏感词列表", "@trafficGuideSensitiveWords": {"description": "Sensitive words field label"}, "trafficGuideSensitiveWordsHint": "输入敏感词，用逗号分隔", "@trafficGuideSensitiveWordsHint": {"description": "Sensitive words field hint"}, "trafficGuideWatermarkConfig": "水印配置", "@trafficGuideWatermarkConfig": {"description": "Watermark configuration section title"}, "trafficGuideWatermarkTextHint": "输入水印内容", "@trafficGuideWatermarkTextHint": {"description": "Watermark text field hint"}, "trafficGuideInvisibleWatermark": "隐形水印", "@trafficGuideInvisibleWatermark": {"description": "Invisible watermark checkbox label"}, "trafficGuideOpacity": "透明度", "@trafficGuideOpacity": {"description": "Opacity field label"}, "trafficGuideWatermarkFontSize": "字体大小", "@trafficGuideWatermarkFontSize": {"description": "Watermark font size field label"}, "trafficGuideProjectSaved": "项目保存成功", "@trafficGuideProjectSaved": {"description": "Project save success message"}, "trafficGuideSaveProject": "保存项目", "@trafficGuideSaveProject": {"description": "Save project button text"}, "trafficGuideSaving": "保存中...", "@trafficGuideSaving": {"description": "Saving progress text"}, "trafficGuideNewProjectName": "新项目", "@trafficGuideNewProjectName": {"description": "Default new project name"}, "trafficGuideNewProjectDescription": "引流项目配置", "@trafficGuideNewProjectDescription": {"description": "Default new project description"}, "trafficGuideImageGeneratorTitle": "引流图片生成", "@trafficGuideImageGeneratorTitle": {"description": "Image generator screen title"}, "trafficGuideImageConfiguration": "图片配置", "@trafficGuideImageConfiguration": {"description": "Image configuration section title"}, "trafficGuideTextRequired": "请输入文本内容", "@trafficGuideTextRequired": {"description": "Text content validation error"}, "trafficGuideInterferenceSettings": "干扰设置", "@trafficGuideInterferenceSettings": {"description": "Interference settings section title"}, "trafficGuideInterferenceLevel": "干扰程度", "@trafficGuideInterferenceLevel": {"description": "Interference level slider label"}, "trafficGuideWatermarkSettings": "水印设置", "@trafficGuideWatermarkSettings": {"description": "Watermark settings section title"}, "trafficGuideWatermarkContent": "水印内容", "@trafficGuideWatermarkContent": {"description": "Watermark content field label"}, "trafficGuideWatermarkContentHint": "输入要添加的水印内容...", "@trafficGuideWatermarkContentHint": {"description": "Watermark content field hint"}, "trafficGuidePreview": "预览", "@trafficGuidePreview": {"description": "Preview section title"}, "trafficGuideSaveToAlbum": "保存到相册", "@trafficGuideSaveToAlbum": {"description": "Save to album tooltip"}, "trafficGuideShare": "分享", "@trafficGuideShare": {"description": "Share tooltip"}, "trafficGuideSelectColor": "选择颜色", "@trafficGuideSelectColor": {"description": "Select color dialog title"}, "trafficGuideBlack": "黑色", "@trafficGuideBlack": {"description": "Black color name"}, "trafficGuideWhite": "白色", "@trafficGuideWhite": {"description": "White color name"}, "trafficGuideRed": "红色", "@trafficGuideRed": {"description": "Red color name"}, "trafficGuideGreen": "绿色", "@trafficGuideGreen": {"description": "Green color name"}, "trafficGuideBlue": "蓝色", "@trafficGuideBlue": {"description": "Blue color name"}, "trafficGuideYellow": "黄色", "@trafficGuideYellow": {"description": "Yellow color name"}, "trafficGuidePurple": "紫色", "@trafficGuidePurple": {"description": "Purple color name"}, "trafficGuideCyan": "青色", "@trafficGuideCyan": {"description": "Cyan color name"}, "trafficGuideGenerateImage": "生成图片", "@trafficGuideGenerateImage": {"description": "Generate image button text"}, "trafficGuideGenerating": "生成中...", "@trafficGuideGenerating": {"description": "Generating progress text"}, "trafficGuideImageGenerationFailed": "生成图片失败: {error}", "@trafficGuideImageGenerationFailed": {"description": "Image generation error message"}, "trafficGuideLongPressToSave": "长按图片可保存到相册", "@trafficGuideLongPressToSave": {"description": "Long press to save hint"}, "trafficGuideShareFeatureInProgress": "分享功能开发中...", "@trafficGuideShareFeatureInProgress": {"description": "Share feature in development message"}, "trafficGuideTextTransformerTitle": "文本转换", "@trafficGuideTextTransformerTitle": {"description": "Text transformer screen title"}, "trafficGuideTransformSettings": "转换设置", "@trafficGuideTransformSettings": {"description": "Transform settings section title"}, "trafficGuideTransformText": "转换文本", "@trafficGuideTransformText": {"description": "Transform text button text"}, "trafficGuideTransforming": "转换中...", "@trafficGuideTransforming": {"description": "Transforming progress text"}, "trafficGuideInputText": "输入文本", "@trafficGuideInputText": {"description": "Input text section title"}, "trafficGuideCharacters": "字符", "@trafficGuideCharacters": {"description": "Characters count unit"}, "trafficGuideInputHint": "输入要转换的文本...", "@trafficGuideInputHint": {"description": "Input text field hint"}, "trafficGuideTransformResult": "转换结果", "@trafficGuideTransformResult": {"description": "Transform result section title"}, "trafficGuideResultHint": "转换后的文本将显示在这里...", "@trafficGuideResultHint": {"description": "Transform result field hint"}, "trafficGuideTransformFailed": "转换失败: {error}", "@trafficGuideTransformFailed": {"description": "Transform error message"}, "trafficGuideCopyResult": "复制结果", "@trafficGuideCopyResult": {"description": "Copy result button tooltip"}, "trafficGuideClear": "清空", "@trafficGuideClear": {"description": "Clear button tooltip"}, "trafficGuideSettings": "设置", "@trafficGuideSettings": {"description": "Settings tooltip"}, "trafficGuideAdvancedSettings": "高级设置", "@trafficGuideAdvancedSettings": {"description": "Advanced settings dialog title"}, "trafficGuideCopiedToClipboard": "已复制到剪贴板", "@trafficGuideCopiedToClipboard": {"description": "Copied to clipboard message"}, "trafficGuideCustomCharacterMapping": "自定义字符映射", "@trafficGuideCustomCharacterMapping": {"description": "Custom character mapping section title"}, "trafficGuideMappingFormat": "格式: 原字符=目标字符 (每行一个)", "@trafficGuideMappingFormat": {"description": "Mapping format hint"}, "trafficGuideMappingExample": "例如:\na=ᴀ\nb=ʙ", "@trafficGuideMappingExample": {"description": "Mapping example"}, "trafficGuideConfirm": "确定", "@trafficGuideConfirm": {"description": "Confirm button text"}, "trafficGuideWatermarkTitle": "文本水印处理", "@trafficGuideWatermarkTitle": {"description": "Watermark processor screen title"}, "trafficGuideProcessingMode": "处理模式", "@trafficGuideProcessingMode": {"description": "Processing mode section title"}, "trafficGuideAddWatermarkMode": "添加水印", "@trafficGuideAddWatermarkMode": {"description": "Add watermark mode title"}, "trafficGuideAddWatermarkModeSubtitle": "在文本中添加可见或隐形水印", "@trafficGuideAddWatermarkModeSubtitle": {"description": "Add watermark mode subtitle"}, "trafficGuideRemoveWatermarkMode": "移除水印", "@trafficGuideRemoveWatermarkMode": {"description": "Remove watermark mode title"}, "trafficGuideRemoveWatermarkModeSubtitle": "从文本中移除已添加的水印", "@trafficGuideRemoveWatermarkModeSubtitle": {"description": "Remove watermark mode subtitle"}, "trafficGuideProcessText": "处理文本", "@trafficGuideProcessText": {"description": "Process text button text"}, "trafficGuideProcessing": "处理中...", "@trafficGuideProcessing": {"description": "Processing progress text"}, "trafficGuideOriginalText": "原始文本", "@trafficGuideOriginalText": {"description": "Original text section title"}, "trafficGuideWatermarkedText": "已添加水印文本", "@trafficGuideWatermarkedText": {"description": "Watermarked text section title"}, "trafficGuideProcessHint": "处理结果将在此显示", "@trafficGuideProcessHint": {"description": "Process result field hint"}, "trafficGuideWatermarkIdentifier": "水印标识符", "@trafficGuideWatermarkIdentifier": {"description": "Watermark identifier field label"}, "trafficGuideWatermarkIdentifierHint": "输入要移除的水印标识...", "@trafficGuideWatermarkIdentifierHint": {"description": "Watermark identifier field hint"}, "trafficGuideRotationAngle": "旋转角度", "@trafficGuideRotationAngle": {"description": "Rotation angle field label"}, "trafficGuideEnterTextToProcess": "请输入要处理的文本", "@trafficGuideEnterTextToProcess": {"description": "Enter text to process validation error"}, "trafficGuideEnterWatermarkContent": "请输入水印内容", "@trafficGuideEnterWatermarkContent": {"description": "Enter watermark content validation error"}, "trafficGuideProcessFailed": "处理失败: {error}", "@trafficGuideProcessFailed": {"description": "Process failed error message"}, "trafficGuideSensitiveWordsList": "敏感词列表", "@trafficGuideSensitiveWordsList": {"description": "Sensitive words list label"}, "trafficGuideSensitiveWordsListHint": "输入敏感词，用逗号分隔", "@trafficGuideSensitiveWordsListHint": {"description": "Sensitive words list hint text"}, "trafficGuideWatermarkAddHint": "在此输入需要添加水印的文本内容", "@trafficGuideWatermarkAddHint": {"description": "Watermark add hint text"}, "trafficGuideWatermarkRemoveHint": "在此输入需要移除水印的文本内容", "@trafficGuideWatermarkRemoveHint": {"description": "Watermark remove hint text"}, "textCardsHomePageTitle": "文本卡片", "@textCardsHomePageTitle": {"description": "Text Cards home page title"}, "textCardsHomePageSubtitle": "现代风格 • 内联编辑 • 高清导出", "@textCardsHomePageSubtitle": {"description": "Text Cards home page subtitle"}, "textCardsStartCreating": "开始创作", "@textCardsStartCreating": {"description": "Start creating button text"}, "textCardsQuickActions": "快速操作", "@textCardsQuickActions": {"description": "Quick actions section title"}, "textCardsTemplateLibrary": "模板库", "@textCardsTemplateLibrary": {"description": "Template library action title"}, "textCardsTemplateLibrarySubtitle": "16+ 精美模板", "@textCardsTemplateLibrarySubtitle": {"description": "Template library action subtitle"}, "textCardsSmartSplit": "智能拆分", "@textCardsSmartSplit": {"description": "Smart split action title"}, "textCardsSmartSplitSubtitle": "长文本分段", "@textCardsSmartSplitSubtitle": {"description": "Smart split action subtitle"}, "textCardsContentLibrary": "内容库", "@textCardsContentLibrary": {"description": "Content library action title"}, "textCardsContentLibrarySubtitle": "管理所有卡片", "@textCardsContentLibrarySubtitle": {"description": "Content library action subtitle"}, "textCardsShare": "分享", "@textCardsShare": {"description": "Share action title"}, "textCardsShareSubtitle": "导出高清图片", "@textCardsShareSubtitle": {"description": "Share action subtitle"}, "textCardsFeatures": "功能特色", "@textCardsFeatures": {"description": "Features section title"}, "textCardsModernTemplates": "现代风格模板", "@textCardsModernTemplates": {"description": "Modern templates feature title"}, "textCardsModernTemplatesDesc": "精心设计的现代社交和阅读风格模板，让你的内容更有吸引力", "@textCardsModernTemplatesDesc": {"description": "Modern templates feature description"}, "textCardsInlineEditing": "内联文本编辑", "@textCardsInlineEditing": {"description": "Inline editing feature title"}, "textCardsInlineEditingDesc": "选中任意文本片段，实时调整字体、颜色、大小，所见即所得", "@textCardsInlineEditingDesc": {"description": "Inline editing feature description"}, "textCardsHDExport": "高清图片导出", "@textCardsHDExport": {"description": "HD export feature title"}, "textCardsHDExportDesc": "支持多种分辨率和宽高比，一键保存到相册，完美适配各平台", "@textCardsHDExportDesc": {"description": "HD export feature description"}, "textCardsViewContentLibrary": "查看我的内容库", "@textCardsViewContentLibrary": {"description": "View content library button text"}, "textCardsManageCards": "管理和浏览所有创建的卡片", "@textCardsManageCards": {"description": "Content library button subtitle"}, "textCardsCreate": "创作", "@textCardsCreate": {"description": "Create button text"}, "textCardsPleaseCreateCardFirst": "请先创建卡片", "@textCardsPleaseCreateCardFirst": {"description": "Message when user tries to share without creating a card"}, "textCardsCardCreatedSuccess": "卡片创建成功！已保存到内容库", "@textCardsCardCreatedSuccess": {"description": "Success message when card is created"}, "textCardsBatchCreateSuccess": "批量创建成功！共创建 {count} 张卡片", "@textCardsBatchCreateSuccess": {"description": "Success message for batch creation"}, "textCardsBatchCreatePartial": "批量创建完成！成功创建 {success}/{total} 张卡片", "@textCardsBatchCreatePartial": {"description": "Partial success message for batch creation"}, "textCardsCreateFailed": "创建失败：{error}", "@textCardsCreateFailed": {"description": "Error message when card creation fails"}, "textCardsBatchCreateFailed": "批量创建失败：{error}", "@textCardsBatchCreateFailed": {"description": "Error message when batch creation fails"}, "textCardsEditorTitle": "卡片编辑器", "@textCardsEditorTitle": {"description": "Card editor page title"}, "textCardsCreateCard": "创建卡片", "@textCardsCreateCard": {"description": "Create card title"}, "textCardsEditCard": "编辑卡片", "@textCardsEditCard": {"description": "Edit card title"}, "textCardsSave": "保存", "@textCardsSave": {"description": "Save button text"}, "textCardsEdit": "编辑", "@textCardsEdit": {"description": "Edit tab text"}, "textCardsPreview": "预览", "@textCardsPreview": {"description": "Preview tab text"}, "textCardsEnterCardTitle": "输入卡片标题...", "@textCardsEnterCardTitle": {"description": "Card title input hint"}, "textCardsEnterContent": "输入内容...\n\n支持 Markdown 格式：\n• **粗体**\n• *斜体*\n• • 无序列表\n• 1. 有序列表", "@textCardsEnterContent": {"description": "Content input hint with markdown examples"}, "textCardsContentRequired": "请输入内容", "@textCardsContentRequired": {"description": "Validation message when content is empty"}, "textCardsSaveFailed": "保存失败：{error}", "@textCardsSaveFailed": {"description": "Error message when save fails"}, "textCardsChangeTemplate": "更换模板", "@textCardsChangeTemplate": {"description": "Change template dialog title"}, "textCardsHideTitle": "隐藏标题", "@textCardsHideTitle": {"description": "Hide title tooltip"}, "textCardsShowTitle": "显示标题", "@textCardsShowTitle": {"description": "Show title tooltip"}, "textCardsBoldText": "加粗文本", "@textCardsBoldText": {"description": "Bold text placeholder"}, "textCardsItalicText": "斜体文本", "@textCardsItalicText": {"description": "Italic text placeholder"}, "textCardsUnderlineText": "下划线文本", "@textCardsUnderlineText": {"description": "Underline text placeholder"}, "textCardsPreviewPlaceholder": "在编辑标签页输入内容以查看预览效果...", "@textCardsPreviewPlaceholder": {"description": "Preview placeholder text"}, "textCardsContentEditor": "内容编辑器", "@textCardsContentEditor": {"description": "Content editor page title"}, "textCardsEnterTitle": "输入标题（可选）", "@textCardsEnterTitle": {"description": "Title input hint"}, "textCardsAddSplitMarker": "添加拆分标记", "@textCardsAddSplitMarker": {"description": "Add split marker tooltip"}, "textCardsEnterRenderer": "进入渲染器", "@textCardsEnterRenderer": {"description": "Enter renderer tooltip"}, "textCardsSplitMarkerInfo": "已设置 {count} 个拆分标记，将生成 {sections} 个卡片", "@textCardsSplitMarkerInfo": {"description": "Split marker info text"}, "textCardsEnterContentHint": "在这里输入或粘贴您的内容...\n\n提示：\n- 使用 # 创建标题\n- 使用 - 或 * 创建列表\n- 使用 > 创建引用\n- 点击拆分按钮在光标位置添加拆分标记", "@textCardsEnterContentHint": {"description": "Content input hint with tips"}, "textCardsTextPreview": "文本预览", "@textCardsTextPreview": {"description": "Text preview label"}, "textCardsPreviewWillAppear": "输入内容后将显示预览", "@textCardsPreviewWillAppear": {"description": "Preview placeholder text"}, "textCardsSelectSplitMarker": "选择拆分标记", "@textCardsSelectSplitMarker": {"description": "Split marker dialog title"}, "textCardsPredefinedMarkers": "预定义标记：", "@textCardsPredefinedMarkers": {"description": "Predefined markers label"}, "textCardsCustomMarker": "自定义标记：", "@textCardsCustomMarker": {"description": "Custom marker label"}, "textCardsEnterCustomMarker": "输入自定义拆分标记", "@textCardsEnterCustomMarker": {"description": "Custom marker input hint"}, "textCardsUseCustom": "使用自定义", "@textCardsUseCustom": {"description": "Use custom marker button"}, "textCardsUnnamedCard": "未命名卡片", "@textCardsUnnamedCard": {"description": "Default card name"}, "textCardsUnnamedDocument": "未命名文档", "@textCardsUnnamedDocument": {"description": "Default document name"}, "svgEditorTitle": "SVG编辑器", "@svgEditorTitle": {"description": "SVG editor page title"}, "svgManagerTitle": "SVG管理", "@svgManagerTitle": {"description": "SVG manager page title"}, "svgUntitled": "未命名SVG", "@svgUntitled": {"description": "Default SVG name"}, "svgEditTab": "编辑", "@svgEditTab": {"description": "Edit tab label"}, "svgPreviewTab": "预览", "@svgPreviewTab": {"description": "Preview tab label"}, "svgMoreActions": "更多操作", "@svgMoreActions": {"description": "More actions tooltip"}, "svgImportFile": "导入SVG文件", "@svgImportFile": {"description": "Import SVG file menu item"}, "svgSave": "保存", "@svgSave": {"description": "Save button"}, "svgExportPng": "导出为PNG", "@svgExportPng": {"description": "Export as PNG menu item"}, "svgSharePng": "分享为PNG", "@svgSharePng": {"description": "Share as PNG menu item"}, "svgShareSvg": "分享SVG", "@svgShareSvg": {"description": "Share SVG menu item"}, "svgRename": "重命名", "@svgRename": {"description": "Rename menu item"}, "svgSaveChanges": "保存更改", "@svgSaveChanges": {"description": "Save changes button"}, "svgEnterFileName": "输入SVG文件名", "@svgEnterFileName": {"description": "File name input dialog title"}, "svgFileName": "文件名", "@svgFileName": {"description": "File name input label"}, "svgFileNameHint": "请输入SVG文件名", "@svgFileNameHint": {"description": "File name input hint"}, "svgFileNameRequired": "文件名不能为空", "@svgFileNameRequired": {"description": "File name validation error"}, "svgProcessing": "处理中...", "@svgProcessing": {"description": "Processing message"}, "svgLoading": "加载中...", "@svgLoading": {"description": "Loading message"}, "svgDocumentNotFound": "找不到文档", "@svgDocumentNotFound": {"description": "Document not found error"}, "svgLoadDocumentFailed": "加载文档失败: {error}", "@svgLoadDocumentFailed": {"description": "Load document error with parameter"}, "svgCreateDocumentFailed": "创建文档失败: {error}", "@svgCreateDocumentFailed": {"description": "Create document error with parameter"}, "svgSaveDocumentFailed": "保存文档失败: {error}", "@svgSaveDocumentFailed": {"description": "Save document error with parameter"}, "svgSaveSuccess": "保存成功", "@svgSaveSuccess": {"description": "Save success message"}, "svgImportFailed": "导入SVG文件失败: {error}", "@svgImportFailed": {"description": "Import file error with parameter"}, "svgExportPngSuccess": "导出PNG成功: {path}", "@svgExportPngSuccess": {"description": "PNG export success message with parameter"}, "svgExportPngFailed": "导出PNG失败", "@svgExportPngFailed": {"description": "PNG export failed message"}, "svgShareSvgFailed": "分享SVG失败: {error}", "@svgShareSvgFailed": {"description": "Share SVG error with parameter"}, "svgSharePngFailed": "分享PNG失败: {error}", "@svgSharePngFailed": {"description": "Share PNG error with parameter"}, "svgInvalidSvg": "无效的SVG: {error}", "@svgInvalidSvg": {"description": "Invalid SVG error with parameter"}, "svgSaveFirst": "请先保存文档", "@svgSaveFirst": {"description": "Save first reminder"}, "svgEnterSvgCode": "输入SVG代码", "@svgEnterSvgCode": {"description": "SVG code input hint"}, "svgNoContent": "无SVG内容", "@svgNoContent": {"description": "No content message"}, "svgEnterCodeInEditor": "请在编辑标签页中输入SVG代码", "@svgEnterCodeInEditor": {"description": "Instruction message"}, "svgCreateNew": "创建新SVG", "@svgCreateNew": {"description": "Create new SVG button"}, "svgImport": "导入SVG", "@svgImport": {"description": "Import SVG button"}, "svgImportSvgFile": "导入SVG文件", "@svgImportSvgFile": {"description": "Import SVG file button"}, "svgImportTooltip": "导入SVG", "@svgImportTooltip": {"description": "Import tooltip"}, "svgNewTooltip": "新建SVG", "@svgNewTooltip": {"description": "New SVG tooltip"}, "svgCreateNewTooltip": "创建新SVG", "@svgCreateNewTooltip": {"description": "Create new SVG tooltip"}, "svgNoDocuments": "没有SVG文档", "@svgNoDocuments": {"description": "No documents message"}, "svgNoDocumentsDesc": "创建新的SVG文档或导入现有文件开始使用", "@svgNoDocumentsDesc": {"description": "No documents description"}, "svgLoadFailed": "加载文档失败: {error}", "@svgLoadFailed": {"description": "Load documents error with parameter"}, "svgDeleteConfirm": "确认删除", "@svgDeleteConfirm": {"description": "Delete confirmation dialog title"}, "svgDeleteConfirmMessage": "确定要删除\"{title}\"吗？", "@svgDeleteConfirmMessage": {"description": "Delete confirmation message with parameter"}, "svgDeleteFailed": "删除文档失败: {error}", "@svgDeleteFailed": {"description": "Delete document error with parameter"}, "svgEdit": "编辑", "@svgEdit": {"description": "Edit button"}, "svgShare": "分享", "@svgShare": {"description": "Share button"}, "svgShareAsPng": "分享为PNG", "@svgShareAsPng": {"description": "Share as PNG button"}, "svgDelete": "删除", "@svgDelete": {"description": "Delete button"}, "svgSavedToLibrary": "SVG已保存到内容库", "@svgSavedToLibrary": {"description": "Saved to library message"}, "svgCreatedAt": "创建于: {date}", "@svgCreatedAt": {"description": "Created date with parameter"}, "@pdfModuleComment": "PDF Module Localization", "pdfManagerTitle": "PDF文档管理", "@pdfManagerTitle": {"description": "PDF manager page title"}, "pdfSearch": "搜索PDF文件...", "@pdfSearch": {"description": "PDF search placeholder"}, "pdfSearchHint": "搜索...", "@pdfSearchHint": {"description": "Search hint text"}, "pdfNoDocuments": "没有找到匹配的PDF文档", "@pdfNoDocuments": {"description": "No documents found message"}, "pdfTryDifferentSearch": "尝试使用不同的搜索关键词", "@pdfTryDifferentSearch": {"description": "Search suggestion"}, "pdfConfirmDelete": "确认删除", "@pdfConfirmDelete": {"description": "Delete confirmation title"}, "pdfDeleteConfirm": "确定要删除 \"{filename}\" 吗？此操作不可恢复。", "@pdfDeleteConfirm": {"description": "Delete confirmation message with filename"}, "pdfBatchDeleteConfirm": "确定要删除选中的 {count} 个文件吗？此操作不可恢复。", "@pdfBatchDeleteConfirm": {"description": "<PERSON><PERSON> delete confirmation message"}, "pdfCancel": "取消", "@pdfCancel": {"description": "Cancel button"}, "pdfDelete": "删除", "@pdfDelete": {"description": "Delete button"}, "pdfImport": "导入PDF", "@pdfImport": {"description": "Import PDF button"}, "pdfImportTooltip": "导入PDF", "@pdfImportTooltip": {"description": "Import PDF tooltip"}, "pdfSecuritySettings": "安全设置", "@pdfSecuritySettings": {"description": "Security settings menu item"}, "pdfSelect": "选择", "@pdfSelect": {"description": "Select menu item"}, "pdfMerge": "合并", "@pdfMerge": {"description": "Merge button"}, "pdfSelectAtLeastTwo": "请至少选择两个PDF文件进行合并", "@pdfSelectAtLeastTwo": {"description": "Merge validation message"}, "pdfMergeSuccess": "PDF合并成功", "@pdfMergeSuccess": {"description": "Merge success message"}, "pdfMergeFailed": "PDF合并失败", "@pdfMergeFailed": {"description": "<PERSON><PERSON> failed message"}, "pdfIntelligentCenter": "PDF 智能管理中心", "@pdfIntelligentCenter": {"description": "PDF center title"}, "pdfCenterSubtitle": "集成阅读、编辑、安全、分享于一体的专业PDF工具", "@pdfCenterSubtitle": {"description": "PDF center subtitle"}, "pdfVersion": "v2.0 专业版", "@pdfVersion": {"description": "Version info"}, "pdfCoreFeatures": "核心功能", "@pdfCoreFeatures": {"description": "Core features section title"}, "pdfProfessional": "专业版", "@pdfProfessional": {"description": "Professional label"}, "pdfSecurityEncryption": "安全加密", "@pdfSecurityEncryption": {"description": "Security feature title"}, "pdfPasswordProtection": "密码保护", "@pdfPasswordProtection": {"description": "Password protection feature"}, "pdfPermissionControl": "权限控制", "@pdfPermissionControl": {"description": "Permission control feature"}, "pdfSmartAnnotations": "智能注释", "@pdfSmartAnnotations": {"description": "Annotations feature title"}, "pdfHighlight": "高亮标记", "@pdfHighlight": {"description": "Highlight feature"}, "pdfTextAnnotations": "文字批注", "@pdfTextAnnotations": {"description": "Text annotations feature"}, "pdfFastSearch": "快速搜索", "@pdfFastSearch": {"description": "Search feature title"}, "pdfFullTextSearch": "全文检索", "@pdfFullTextSearch": {"description": "Full text search feature"}, "pdfPreciseLocation": "精确定位", "@pdfPreciseLocation": {"description": "Precise location feature"}, "pdfDocumentMerge": "文档合并", "@pdfDocumentMerge": {"description": "Document merge feature title"}, "pdfMultiFileMerge": "多文件合并", "@pdfMultiFileMerge": {"description": "Multi-file merge feature"}, "pdfEasySharing": "便捷分享", "@pdfEasySharing": {"description": "Sharing feature title"}, "pdfOneClickShare": "一键分享", "@pdfOneClickShare": {"description": "One-click share feature"}, "pdfCloudSync": "云端同步", "@pdfCloudSync": {"description": "Cloud sync feature title"}, "pdfAutoSync": "自动同步", "@pdfAutoSync": {"description": "Auto sync feature"}, "pdfQuickStart": "快速开始", "@pdfQuickStart": {"description": "Quick start section"}, "pdfImportDocument": "导入PDF文档", "@pdfImportDocument": {"description": "Import PDF button"}, "pdfViewDemo": "查看演示", "@pdfViewDemo": {"description": "View demo button"}, "pdfHelp": "使用帮助", "@pdfHelp": {"description": "Help button"}, "pdfSupportInfo": "支持导入 .pdf 格式文件，最大支持 100MB", "@pdfSupportInfo": {"description": "File support info"}, "pdfModified": "修改于: {date}", "@pdfModified": {"description": "Modified date with parameter"}, "pdfJustNow": "刚刚", "@pdfJustNow": {"description": "Just now time format"}, "pdfMinutesAgo": "{minutes}分钟前", "@pdfMinutesAgo": {"description": "Minutes ago format"}, "pdfHoursAgo": "{hours}小时前", "@pdfHoursAgo": {"description": "Hours ago format"}, "pdfDaysAgo": "{days}天前", "@pdfDaysAgo": {"description": "Days ago format"}, "pdfPages": "{count} 页", "@pdfPages": {"description": "Page count format"}, "pdfSecurityStatus": "安全状态", "@pdfSecurityStatus": {"description": "Security status"}, "pdfProtected": "已保护", "@pdfProtected": {"description": "Protected status"}, "pdfRestricted": "已限制", "@pdfRestricted": {"description": "Restricted status"}, "pdfUnprotected": "未保护", "@pdfUnprotected": {"description": "Unprotected status"}, "pdfUsageStats": "使用统计", "@pdfUsageStats": {"description": "Usage statistics section"}, "pdfTotalDocuments": "文档总数", "@pdfTotalDocuments": {"description": "Total documents label"}, "pdfTodayProcessed": "今日处理", "@pdfTodayProcessed": {"description": "Today processed label"}, "pdfStorageSpace": "存储空间", "@pdfStorageSpace": {"description": "Storage space label"}, "pdfTips": "使用技巧", "@pdfTips": {"description": "Usage tips section"}, "pdfLongPressSelect": "长按选择", "@pdfLongPressSelect": {"description": "Long press tip"}, "pdfLongPressDesc": "长按文档卡片可进入多选模式，批量操作更高效", "@pdfLongPressDesc": {"description": "Long press description"}, "pdfSecurityTip": "安全加密", "@pdfSecurityTip": {"description": "Security tip"}, "pdfSecurityTipDesc": "为重要文档设置密码保护，确保信息安全", "@pdfSecurityTipDesc": {"description": "Security tip description"}, "pdfMergeTip": "文档合并", "@pdfMergeTip": {"description": "Merge tip"}, "pdfMergeTipDesc": "选择多个PDF文档，一键合并成单个文件", "@pdfMergeTipDesc": {"description": "Merge tip description"}, "@pdfViewerComment": "PDF Viewer Localization", "pdfViewerTitle": "PDF查看器", "@pdfViewerTitle": {"description": "PDF viewer page title"}, "pdfSearchText": "搜索文本", "@pdfSearchText": {"description": "Search text field label"}, "pdfShowAnnotations": "显示注释", "@pdfShowAnnotations": {"description": "Show annotations menu item"}, "pdfHideAnnotations": "隐藏注释", "@pdfHideAnnotations": {"description": "Hide annotations menu item"}, "pdfDocumentInfo": "文档信息", "@pdfDocumentInfo": {"description": "Document info menu item"}, "pdfAnnotationDetails": "注释详情", "@pdfAnnotationDetails": {"description": "Annotation details title"}, "pdfAuthor": "作者: {author}", "@pdfAuthor": {"description": "Author label with parameter"}, "pdfCreatedAt": "创建时间: {datetime}", "@pdfCreatedAt": {"description": "Created at with parameter"}, "pdfContent": "内容: {content}", "@pdfContent": {"description": "Content label with parameter"}, "pdfHighlightedText": "高亮文本: {text}", "@pdfHighlightedText": {"description": "Highlighted text with parameter"}, "pdfFileName": "文件名", "@pdfFileName": {"description": "File name label"}, "pdfFileSize": "大小", "@pdfFileSize": {"description": "File size label"}, "pdfPageCount": "页数", "@pdfPageCount": {"description": "Page count label"}, "pdfCreatedDate": "创建时间", "@pdfCreatedDate": {"description": "Created date label"}, "pdfModifiedDate": "修改时间", "@pdfModifiedDate": {"description": "Modified date label"}, "pdfAnnotationCount": "注释数量", "@pdfAnnotationCount": {"description": "Annotation count label"}, "pdfClose": "关闭", "@pdfClose": {"description": "Close button"}, "@pdfSecurityComment": "PDF Security Localization", "pdfSecurityTitle": "PDF安全设置", "@pdfSecurityTitle": {"description": "PDF security page title"}, "pdfDocumentInfoSection": "文档信息", "@pdfDocumentInfoSection": {"description": "Document info section title"}, "pdfStatus": "状态: {status}", "@pdfStatus": {"description": "Status with parameter"}, "pdfDecryptPdf": "解密PDF", "@pdfDecryptPdf": {"description": "Decrypt PDF section title"}, "pdfEncryptedDesc": "该PDF已加密，请输入密码进行解密", "@pdfEncryptedDesc": {"description": "Encrypted PDF description"}, "pdfCurrentPassword": "当前密码", "@pdfCurrentPassword": {"description": "Current password field label"}, "pdfCurrentPasswordHint": "请输入当前密码", "@pdfCurrentPasswordHint": {"description": "Current password hint"}, "pdfDecryptButton": "解密", "@pdfDecryptButton": {"description": "Decrypt button"}, "pdfEncryptionSettings": "加密设置", "@pdfEncryptionSettings": {"description": "Encryption settings section title"}, "pdfUserPassword": "用户密码 *", "@pdfUserPassword": {"description": "User password field label"}, "pdfUserPasswordHint": "用于打开PDF的密码", "@pdfUserPasswordHint": {"description": "User password hint"}, "pdfOwnerPassword": "所有者密码（可选）", "@pdfOwnerPassword": {"description": "Owner password field label"}, "pdfOwnerPasswordHint": "用于修改权限的密码", "@pdfOwnerPasswordHint": {"description": "Owner password hint"}, "pdfPermissionSettings": "权限设置", "@pdfPermissionSettings": {"description": "Permission settings section title"}, "pdfAllowPrint": "允许打印", "@pdfAllowPrint": {"description": "Allow printing permission"}, "pdfAllowPrintDesc": "允许用户打印PDF文档", "@pdfAllowPrintDesc": {"description": "Allow printing description"}, "pdfAllowCopy": "允许复制", "@pdfAllowCopy": {"description": "Allow copying permission"}, "pdfAllowCopyDesc": "允许用户复制PDF内容", "@pdfAllowCopyDesc": {"description": "Allow copying description"}, "pdfAllowEdit": "允许编辑", "@pdfAllowEdit": {"description": "Allow editing permission"}, "pdfAllowEditDesc": "允许用户编辑PDF文档", "@pdfAllowEditDesc": {"description": "Allow editing description"}, "pdfAllowEditAnnotations": "允许编辑注释", "@pdfAllowEditAnnotations": {"description": "Allow edit annotations permission"}, "pdfAllowEditAnnotationsDesc": "允许用户添加或编辑注释", "@pdfAllowEditAnnotationsDesc": {"description": "Allow edit annotations description"}, "pdfAllowFillForms": "允许填写表单", "@pdfAllowFillForms": {"description": "Allow fill forms permission"}, "pdfAllowFillFormsDesc": "允许用户填写表单字段", "@pdfAllowFillFormsDesc": {"description": "Allow fill forms description"}, "pdfAllowExtractPages": "允许提取页面", "@pdfAllowExtractPages": {"description": "Allow extract pages permission"}, "pdfAllowExtractPagesDesc": "允许用户提取页面内容", "@pdfAllowExtractPagesDesc": {"description": "Allow extract pages description"}, "pdfAllowAssembleDocument": "允许装配文档", "@pdfAllowAssembleDocument": {"description": "Allow assemble document permission"}, "pdfAllowAssembleDocumentDesc": "允许用户插入、删除、旋转页面", "@pdfAllowAssembleDocumentDesc": {"description": "Allow assemble document description"}, "pdfAllowHighQualityPrint": "允许高质量打印", "@pdfAllowHighQualityPrint": {"description": "Allow high quality print permission"}, "pdfAllowHighQualityPrintDesc": "允许用户高质量打印", "@pdfAllowHighQualityPrintDesc": {"description": "Allow high quality print description"}, "pdfPresetPermissions": "预设权限", "@pdfPresetPermissions": {"description": "Preset permissions section title"}, "pdfAllPermissions": "全部权限", "@pdfAllPermissions": {"description": "All permissions preset"}, "pdfBasicPermissions": "基本权限", "@pdfBasicPermissions": {"description": "Basic permissions preset"}, "pdfReadOnly": "只读", "@pdfReadOnly": {"description": "Read only preset"}, "pdfSetPermissionsOnly": "仅设置权限", "@pdfSetPermissionsOnly": {"description": "Set permissions only button"}, "pdfEncryptAndSetPermissions": "加密并设置权限", "@pdfEncryptAndSetPermissions": {"description": "Encrypt and set permissions button"}, "pdfEnterUserPassword": "请输入用户密码", "@pdfEnterUserPassword": {"description": "Enter user password validation"}, "pdfEncryptSuccess": "PDF加密成功", "@pdfEncryptSuccess": {"description": "Encryption success message"}, "pdfEncryptFailed": "PDF加密失败", "@pdfEncryptFailed": {"description": "Encryption failed message"}, "pdfEncryptionFailed": "加密失败: {error}", "@pdfEncryptionFailed": {"description": "Encryption failed with error"}, "pdfEnterCurrentPassword": "请输入当前密码", "@pdfEnterCurrentPassword": {"description": "Enter current password validation"}, "pdfDecryptSuccess": "PDF解密成功", "@pdfDecryptSuccess": {"description": "Decryption success message"}, "pdfDecryptFailed": "PDF解密失败，请检查密码", "@pdfDecryptFailed": {"description": "Decryption failed message"}, "pdfDecryptionFailed": "解密失败: {error}", "@pdfDecryptionFailed": {"description": "Decryption failed with error"}, "pdfPermissionsSetSuccess": "权限设置成功", "@pdfPermissionsSetSuccess": {"description": "Permissions set success message"}, "pdfPermissionsSetFailed": "权限设置失败", "@pdfPermissionsSetFailed": {"description": "Permissions set failed message"}, "pdfSetPermissionsFailed": "设置失败: {error}", "@pdfSetPermissionsFailed": {"description": "Set permissions failed with error"}, "htmlManagerTitle": "HTML管理", "@htmlManagerTitle": {"description": "HTML manager screen title"}, "htmlManagerDescription": "创建和编辑HTML文档，所有内容将自动保存至内容库进行统一管理", "@htmlManagerDescription": {"description": "HTML manager description"}, "htmlCreateNew": "创建新HTML", "@htmlCreateNew": {"description": "Create new HTML button"}, "htmlImportFile": "导入HTML文件", "@htmlImportFile": {"description": "Import HTML file button"}, "htmlImporting": "导入中...", "@htmlImporting": {"description": "Importing HTML status"}, "htmlImportSuccess": "导入成功", "@htmlImportSuccess": {"description": "Import success dialog title"}, "htmlImportSuccessMessage": "已成功导入 {count} 个HTML文件到内容库", "@htmlImportSuccessMessage": {"description": "Import success message with count"}, "htmlImportFailed": "导入HTML文件失败: {error}", "@htmlImportFailed": {"description": "Import failed error message"}, "htmlImportingProgress": "正在导入HTML文件 ({imported}/{total})", "@htmlImportingProgress": {"description": "Import progress message"}, "htmlViewContentLibrary": "查看内容库", "@htmlViewContentLibrary": {"description": "View content library link"}, "htmlEditorTitle": "HTML编辑器", "@htmlEditorTitle": {"description": "HTML editor screen title"}, "htmlNewDocument": "新HTML文档", "@htmlNewDocument": {"description": "New HTML document default title"}, "htmlInputFilename": "输入HTML文件名", "@htmlInputFilename": {"description": "Filename input dialog title"}, "htmlFilename": "文件名", "@htmlFilename": {"description": "Filename input label"}, "htmlFilenameHint": "请输入HTML文件名", "@htmlFilenameHint": {"description": "Filename input hint"}, "htmlFilenameEmpty": "文件名不能为空", "@htmlFilenameEmpty": {"description": "Filename empty error message"}, "htmlCancel": "取消", "@htmlCancel": {"description": "Cancel button"}, "htmlConfirm": "确定", "@htmlConfirm": {"description": "Confirm button"}, "htmlSaveSuccess": "保存成功", "@htmlSaveSuccess": {"description": "Save success message"}, "htmlDocumentNotFound": "找不到文档", "@htmlDocumentNotFound": {"description": "Document not found error"}, "htmlLoadDocumentFailed": "加载文档失败: {error}", "@htmlLoadDocumentFailed": {"description": "Load document failed error"}, "htmlCreateDocumentFailed": "创建文档失败: {error}", "@htmlCreateDocumentFailed": {"description": "Create document failed error"}, "htmlSaveDocumentFailed": "保存文档失败: {error}", "@htmlSaveDocumentFailed": {"description": "Save document failed error"}, "htmlImportFileFailed": "导入HTML文件失败: {error}", "@htmlImportFileFailed": {"description": "Import file failed error"}, "htmlExportImageFailed": "导出图片失败: {error}", "@htmlExportImageFailed": {"description": "Export image failed error"}, "htmlShareHtmlFailed": "分享HTML失败: {error}", "@htmlShareHtmlFailed": {"description": "Share HTML failed error"}, "htmlShareImageFailed": "分享图片失败: {error}", "@htmlShareImageFailed": {"description": "Share image failed error"}, "htmlSaveToLibraryFailed": "保存到内容库失败: {error}", "@htmlSaveToLibraryFailed": {"description": "Save to library failed error"}, "htmlPleaseSaveFirst": "请先保存文档", "@htmlPleaseSaveFirst": {"description": "Please save first message"}, "htmlSelectSaveLocation": "选择保存位置", "@htmlSelectSaveLocation": {"description": "Select save location dialog title"}, "htmlExportImageSuccess": "导出图片成功: {path}", "@htmlExportImageSuccess": {"description": "Export image success message"}, "htmlSavedToLibrary": "已保存到内容库: {title}", "@htmlSavedToLibrary": {"description": "Saved to library message"}, "htmlProcessing": "处理中...", "@htmlProcessing": {"description": "Processing status"}, "htmlProcessingLargeText": "处理大文本...", "@htmlProcessingLargeText": {"description": "Processing large text status"}, "htmlInputHtmlCode": "输入HTML代码", "@htmlInputHtmlCode": {"description": "HTML input hint"}, "htmlNoContent": "无HTML内容", "@htmlNoContent": {"description": "No HTML content message"}, "htmlPleaseInputHtml": "请输入HTML代码", "@htmlPleaseInputHtml": {"description": "Please input HTML message"}, "htmlEditMode": "编辑模式", "@htmlEditMode": {"description": "Edit mode tooltip"}, "htmlPreviewMode": "预览模式", "@htmlPreviewMode": {"description": "Preview mode tooltip"}, "htmlSingleScreenMode": "单屏模式", "@htmlSingleScreenMode": {"description": "Single screen mode tooltip"}, "htmlSplitScreenMode": "分屏模式", "@htmlSplitScreenMode": {"description": "Split screen mode tooltip"}, "htmlMoreActions": "更多操作", "@htmlMoreActions": {"description": "More actions tooltip"}, "htmlImportHtmlFile": "导入HTML文件", "@htmlImportHtmlFile": {"description": "Import HTML file menu item"}, "htmlExportAsImage": "导出为图片", "@htmlExportAsImage": {"description": "Export as image menu item"}, "htmlShareAsImage": "分享为图片", "@htmlShareAsImage": {"description": "Share as image menu item"}, "htmlShareHtml": "分享HTML", "@htmlShareHtml": {"description": "Share HTML menu item"}, "htmlRename": "重命名", "@htmlRename": {"description": "Rename menu item"}, "htmlSaveToLibrary": "保存到内容库", "@htmlSaveToLibrary": {"description": "Save to library menu item"}, "htmlSaveToLibraryTooltip": "保存到内容库", "@htmlSaveToLibraryTooltip": {"description": "Save to library button tooltip"}, "htmlSaveTooltip": "保存", "@htmlSaveTooltip": {"description": "Save button tooltip"}, "htmlSaveToGallery": "保存到相册", "@htmlSaveToGallery": {"description": "Save to gallery button label"}, "htmlNewHtmlTooltip": "新建HTML", "@htmlNewHtmlTooltip": {"description": "New HTML button tooltip"}, "htmlImportHtmlTooltip": "导入HTML", "@htmlImportHtmlTooltip": {"description": "Import HTML button tooltip"}, "settingsLanguagePreference": "选择您偏好的应用语言", "settingsStorageManagement": "存储管理", "settingsHelpCenter": "帮助中心", "settingsFeedback": "意见反馈", "settingsVersionInfo": "版本信息", "settingsHelpAndFeedback": "帮助与反馈", "settingsGetHelpOrProvideFeedback": "获取帮助或提供反馈", "settingsHelpAndFeedbackContent": "如果您需要帮助或有建议，请通过反馈页面联系我们。", "settingsOk": "确定", "settingsSelectTheme": "选择主题", "settingsSystemMode": "跟随系统", "settingsLightMode": "浅色", "settingsDarkMode": "深色", "storageLoadingStorageInfo": "加载存储信息", "storageLoadStorageInfoFailed": "加载存储信息失败：{error}", "storageTotalUsage": "总存储使用", "storageDetails": "存储详情", "storageAppData": "应用数据", "storageCacheFiles": "缓存文件", "storageContentData": "内容数据", "storageVoiceFiles": "语音文件", "storageImageFiles": "图片文件", "storageSettingsData": "设置数据", "storageCleanupOptions": "清理选项", "storageClearCache": "清理缓存", "storageClearCacheDesc": "删除临时文件和缓存数据", "storageClearTempFiles": "清理临时文件", "storageClearTempFilesDesc": "删除处理过程中产生的临时文件", "storageDataManagement": "数据管理", "storageExportData": "导出数据", "storageExportDataDesc": "将应用数据导出到文件", "storageImportData": "导入数据", "storageImportDataDesc": "从文件导入应用数据", "storageResetAppData": "重置应用数据", "storageResetAppDataDesc": "清除所有数据并恢复默认设置", "storageCacheCleared": "缓存清理完成", "storageClearCacheFailed": "清理缓存失败：{error}", "storageTempFilesCleared": "临时文件清理完成", "storageClearTempFilesFailed": "清理临时文件失败：{error}", "storageVoiceManagementInDevelopment": "语音文件管理功能开发中", "storageImageManagementInDevelopment": "图片文件管理功能开发中", "storageDataExportInDevelopment": "数据导出功能开发中", "storageDataImportInDevelopment": "数据导入功能开发中", "storageResetDataTitle": "重置应用数据", "storageResetDataMessage": "此操作将删除所有数据并恢复默认设置，无法撤销。确定要继续吗？", "storageCancel": "取消", "storageConfirm": "确定", "storageDataResetComplete": "应用数据重置完成", "storageDataResetFailed": "重置应用数据失败：{error}", "iosSettingsGeneral": "通用", "iosSettingsLanguageRegion": "语言与地区", "iosSettingsDisplayBrightness": "显示与亮度", "iosSettingsAppearance": "外观", "iosSettingsPrivacySecurity": "隐私与安全", "iosSettingsPrivacySettings": "隐私设置", "iosSettingsStorage": "存储", "iosSettingsStorageManagement": "存储管理", "iosSettingsViewStorageUsage": "查看存储使用情况", "iosSettingsDataImportExport": "数据导入导出", "iosSettingsBackupRestoreData": "备份和恢复数据", "iosSettingsSupport": "支持", "iosSettingsHelpCenter": "帮助中心", "iosSettingsFeedback": "意见反馈", "iosSettingsRateApp": "评价应用", "iosSettingsAbout": "关于", "iosSettingsAboutApp": "关于应用", "iosSettingsCurrentLanguage": "简体中文", "iosSettingsLightTheme": "浅色", "iosSettingsDarkTheme": "深色", "iosSettingsSystemTheme": "跟随系统", "iosSettingsSelectLanguage": "选择语言", "iosSettingsSimplifiedChinese": "简体中文", "iosSettingsTraditionalChinese": "繁體中文", "iosSettingsEnglish": "English", "iosSettingsLanguageSelected": "已选择语言：{language}", "iosSettingsSelectAppearance": "选择外观", "iosSettingsPrivacyContent": "我们重视您的隐私。所有数据处理都在本地进行，不会上传到服务器。", "iosSettingsPrivacyManage": "您可以随时在设置中管理您的数据。", "iosSettingsUnderstand": "了解", "iosSettingsDataManagementTitle": "数据管理", "iosSettingsSelectOperation": "选择要执行的操作", "iosSettingsExportData": "导出数据", "iosSettingsImportData": "导入数据", "iosSettingsCreateBackup": "创建备份", "iosSettingsRateAppTitle": "评价应用", "iosSettingsRateAppMessage": "您喜欢这个应用吗？请在App Store中为我们评分！", "iosSettingsLater": "稍后", "iosSettingsRateNow": "去评价", "iosSettingsCannotOpenAppStore": "无法打开App Store", "iosSettingsAppStoreError": "打开App Store时出现错误", "iosSettingsVersion": "版本：{version}", "iosSettingsAppDescription": "一款强大的内容管理工具，帮助您更高效地创建和管理各种格式的内容。", "iosSettingsCopyright": "© 2023-2024 内容君团队", "iosSettingsClose": "关闭", "iosSettingsOK": "确定", "iosSettingsError": "错误", "iosSettingsDataExportInDevelopment": "数据导出功能开发中...", "iosSettingsDataImportInDevelopment": "数据导入功能开发中...", "iosSettingsBackupInDevelopment": "备份功能开发中...", "helpCenterTitle": "帮助中心", "helpCenterNeedHelp": "需要帮助？", "helpCenterDescription": "查找常见问题的解答，或联系我们获取支持", "helpCenterContactSupport": "联系支持", "helpCenterUserManual": "用户手册", "helpCenterGettingStarted": "如何开始使用", "helpCenterGettingStartedContent": "欢迎使用内容君！您可以从主页选择需要的功能模块，如文本卡片、Markdown编辑、PDF处理等。", "helpCenterTextCards": "文本卡片功能", "helpCenterTextCardsContent": "文本卡片功能可以帮您将文本内容转换为精美的卡片图片，支持多种模板和样式自定义。", "helpCenterMarkdownEditing": "Markdown编辑", "helpCenterMarkdownEditingContent": "Markdown编辑器支持实时预览、多种主题、导出为HTML/PDF等功能，让您的文档编写更高效。", "helpCenterTrafficGuideGeneration": "引流图片生成", "helpCenterTrafficGuideGenerationContent": "引流图片生成功能可以创建吸引眼球的营销图片，支持添加干扰、水印等防盗用功能。", "helpCenterVoiceFeatures": "语音功能", "helpCenterVoiceFeaturesContent": "语音功能包括录音、转录、文字转语音等，支持多种语言和高质量的语音处理。", "helpCenterPDFProcessing": "PDF处理", "helpCenterPDFProcessingContent": "PDF处理功能支持查看、注释、安全设置等，让您更好地管理PDF文档。", "helpCenterDataSyncBackup": "数据同步与备份", "helpCenterDataSyncBackupContent": "您的数据会自动保存在本地，建议定期使用导出功能备份重要内容。", "helpCenterPrivacySecurity": "隐私与安全", "helpCenterPrivacySecurityContent": "我们重视您的隐私，所有数据处理都在本地进行，不会上传到服务器。", "helpCenterSearchHelp": "搜索帮助", "helpCenterSearchPlaceholder": "输入关键词搜索...", "helpCenterSearchCancel": "取消", "helpCenterSearch": "搜索", "helpCenterSupportRequestSubject": "内容君 支持请求", "helpCenterSupportRequestBody": "请描述您遇到的问题...", "helpCenterCannotOpenEmailApp": "无法打开邮件应用", "helpCenterEmailError": "发送邮件时出现错误", "helpCenterCannotOpenManual": "无法打开用户手册", "helpCenterManualError": "打开用户手册时出现错误", "feedbackTitle": "意见反馈", "feedbackSubtitle": "您的意见很重要", "feedbackDescription": "告诉我们您的想法，帮助我们改进应用", "feedbackType": "反馈类型", "feedbackTitleLabel": "标题", "feedbackDetailedDescription": "详细描述", "feedbackContactEmail": "联系邮箱（可选）", "feedbackIncludeSystemInfo": "包含系统信息", "feedbackIncludeSystemInfoDesc": "帮助我们更好地诊断问题", "feedbackSubmit": "提交反馈", "feedbackBugReport": "错误报告", "feedbackBugReportDesc": "报告应用中的错误或异常", "feedbackFeatureSuggestion": "功能建议", "feedbackFeatureSuggestionDesc": "建议新功能或改进现有功能", "feedbackComplaint": "问题投诉", "feedbackComplaintDesc": "对应用的不满或问题", "feedbackPraise": "表扬赞美", "feedbackPraiseDesc": "对应用的赞美或好评", "feedbackTitleHint": "请简要描述您的反馈", "feedbackTitleRequired": "请输入反馈标题", "feedbackDescriptionHint": "请详细描述您的问题、建议或想法...", "feedbackDescriptionRequired": "请输入详细描述", "feedbackDescriptionTooShort": "描述内容至少需要10个字符", "feedbackEmailHint": "<EMAIL>", "feedbackEmailInvalid": "请输入有效的邮箱地址", "feedbackSubmitting": "提交中...", "feedbackSubmissionError": "提交反馈时出现错误：{error}", "feedbackSubmissionSuccessTitle": "反馈提交成功", "feedbackSubmissionSuccessMessage": "感谢您的反馈！我们会认真考虑您的建议。", "feedbackConfirm": "确定", "feedbackSystemInfoFailed": "系统信息获取失败", "feedbackAppVersion": "应用版本：{version}", "feedbackBuildNumber": "构建号：{buildNumber}", "feedbackDevice": "设备：{device}", "feedbackSystemVersion": "系统版本：{version}", "feedbackDeviceModel": "设备型号：{model}", "feedbackManufacturer": "制造商：{manufacturer}", "feedbackEmailSubject": "内容君 - {feedbackType}", "feedbackCannotOpenEmailApp": "无法打开邮件应用", "newContentLibraryExperience": "全新内容库体验", "@newContentLibraryExperience": {"description": "Content library feature card title"}, "supportMultipleContentTypes": "支持多种内容类型，优先显示渲染结果", "@supportMultipleContentTypes": {"description": "Content library feature card subtitle"}, "contentLibraryDemoPage": "内容库演示", "@contentLibraryDemoPage": {"description": "Content library demo page title"}, "contentServiceLoadItemFailed": "加载内容项失败: {error}", "@contentServiceLoadItemFailed": {"description": "Content service load item error message"}, "contentServiceMustBeTextType": "必须是文本类型内容", "@contentServiceMustBeTextType": {"description": "Content service text type validation error"}, "contentServiceMustBeImageType": "必须是图像类型内容", "@contentServiceMustBeImageType": {"description": "Content service image type validation error"}, "contentServiceItemNotFound": "内容项不存在", "@contentServiceItemNotFound": {"description": "Content service item not found error"}, "contentServiceNotInitialized": "ContentService 未初始化", "@contentServiceNotInitialized": {"description": "Content service not initialized error"}, "contentServiceRenderFailed": "渲染失败: {error}", "@contentServiceRenderFailed": {"description": "Content service render failed error"}, "permissionHelperRequestStorageFailed": "请求存储权限失败: {error}", "@permissionHelperRequestStorageFailed": {"description": "Permission helper storage permission request failed error"}, "permissionHelperRequestCameraFailed": "请求相机权限失败: {error}", "@permissionHelperRequestCameraFailed": {"description": "Permission helper camera permission request failed error"}, "permissionHelperRequestMultipleFailed": "请求多个权限失败: {error}", "@permissionHelperRequestMultipleFailed": {"description": "Permission helper multiple permissions request failed error"}, "permissionHelperIosPermissionCheckFailed": "启动时权限状态检查失败: {error}", "@permissionHelperIosPermissionCheckFailed": {"description": "Permission helper iOS permission check failed error"}, "chineseTraditionalColorTitle": "中国传统色", "@chineseTraditionalColorTitle": {"description": "Chinese traditional color theme selector title"}, "chineseTraditionalColorSubtitle": "选择您喜欢的传统色彩主题", "@chineseTraditionalColorSubtitle": {"description": "Chinese traditional color theme selector subtitle"}, "chineseTraditionalColorSystemTheme": "跟随系统主题", "@chineseTraditionalColorSystemTheme": {"description": "System theme option in Chinese traditional color selector"}, "chineseTraditionalColorSystemThemeDesc": "使用应用默认的主题色彩", "@chineseTraditionalColorSystemThemeDesc": {"description": "System theme description in Chinese traditional color selector"}, "chineseTraditionalColorSwitchedToTheme": "已切换到「{themeName}」主题", "@chineseTraditionalColorSwitchedToTheme": {"description": "Message when switching to a specific Chinese traditional color theme"}, "chineseTraditionalColorSwitchedToSystem": "已切换到系统默认主题", "@chineseTraditionalColorSwitchedToSystem": {"description": "Message when switching to system default theme"}, "subscriptionManagement": "订阅管理", "@subscriptionManagement": {"description": "Subscription management page title"}, "upgradeSubscription": "升级订阅", "@upgradeSubscription": {"description": "Upgrade subscription button text"}, "upgradeSubscriptionDesc": "查看和购买更高级的订阅计划", "@upgradeSubscriptionDesc": {"description": "Upgrade subscription description"}, "restorePurchase": "恢复购买", "@restorePurchase": {"description": "Restore purchase button text"}, "restorePurchaseDesc": "恢复您之前的订阅购买", "@restorePurchaseDesc": {"description": "Restore purchase description"}, "helpAndSupport": "帮助和支持", "@helpAndSupport": {"description": "Help and support section title"}, "frequentlyAskedQuestions": "常见问题", "@frequentlyAskedQuestions": {"description": "FAQ button text"}, "frequentlyAskedQuestionsDesc": "查看关于订阅的常见问题解答", "@frequentlyAskedQuestionsDesc": {"description": "FAQ description"}, "contactCustomerService": "联系客服", "@contactCustomerService": {"description": "Contact customer service button text"}, "contactCustomerServiceDesc": "获取关于订阅的帮助", "@contactCustomerServiceDesc": {"description": "Contact customer service description"}, "refundPolicy": "退款政策", "@refundPolicy": {"description": "Refund policy button text"}, "refundPolicyDesc": "了解我们的退款和取消政策", "@refundPolicyDesc": {"description": "Refund policy description"}, "@restoringPurchase": {"description": "Restoring purchase dialog title"}, "@communicatingWithAppStore": {"description": "Communicating with app store message"}, "@noRestorablePurchasesFound": {"description": "No restorable purchases found message"}, "currentSubscription": "当前订阅", "@currentSubscription": {"description": "Current subscription label"}, "freeVersion": "免费版", "@freeVersion": {"description": "Free version subscription name"}, "@active": {"description": "Active status label"}, "availableFeatures": "可用功能", "@availableFeatures": {"description": "Available features section title"}, "basicProcessing": "基础处理", "@basicProcessing": {"description": "Basic processing feature name"}, "exportWithWatermark": "带水印导出", "@exportWithWatermark": {"description": "Export with watermark feature name"}, "unlimitedExport": "无限导出", "@unlimitedExport": {"description": "Unlimited export feature name"}, "batchProcessing": "批量处理", "@batchProcessing": {"description": "Batch processing feature name"}, "advancedTools": "高级工具", "@advancedTools": {"description": "Advanced tools feature name"}, "markdownSavedMarkdown": "保存的 Markdown", "@markdownSavedMarkdown": {"description": "Title for saved markdown content"}, "textCardNoCardsYet": "还没有卡片", "@textCardNoCardsYet": {"description": "Message shown when no cards are available"}, "@textCardAddCard": {"description": "Button to add a new card"}, "@textCardEditCard": {"description": "Title for card editing dialog"}, "textCardTitle": "标题", "@textCardTitle": {"description": "Title field label in card editor"}, "textCardContent": "内容", "@textCardContent": {"description": "Content field label in card editor"}, "textCardNeedPhotoPermission": "需要相册权限才能保存图片，请在设置中开启权限", "@textCardNeedPhotoPermission": {"description": "Permission request message for photo album access"}, "textCardScreenshotFailed": "截图失败，请重试", "@textCardScreenshotFailed": {"description": "Error message when screenshot capture fails"}, "@textCardImageSavedSuccess": {"description": "Success message when image is saved"}, "@textCardExportFailed": {"description": "Error message prefix for export failures"}, "textCardPermissionDenied": "权限不足，请在设置中开启相册权限", "@textCardPermissionDenied": {"description": "Error message when permission is denied"}, "@textCardExportingImage": {"description": "Message shown during image export"}, "textCardExportCard": "导出卡片", "@textCardExportCard": {"description": "Title for exporting a single card"}, "textCardExportDocument": "导出文档", "@textCardExportDocument": {"description": "Title for exporting a document"}, "@textCardExportSuccess": {"description": "Success message for export"}, "textCardPreview": "预览", "@textCardPreview": {"description": "Preview section title"}, "textCardExporting": "导出中...", "@textCardExporting": {"description": "Message shown during export"}, "textCardStartExport": "开始导出", "@textCardStartExport": {"description": "Button to start export"}, "textCardExportSize": "导出尺寸", "@textCardExportSize": {"description": "Export size section title"}, "textCardTargetPlatform": "目标平台", "@textCardTargetPlatform": {"description": "Target platform section title"}, "textCardFileFormat": "文件格式", "@textCardFileFormat": {"description": "File format section title"}, "textCardWatermarkSettings": "水印设置", "@textCardWatermarkSettings": {"description": "Watermark settings section title"}, "@textCardAddWatermark": {"description": "Switch to add watermark"}, "textCardAddWatermarkSubtitle": "在图片角落添加应用水印", "@textCardAddWatermarkSubtitle": {"description": "Subtitle for watermark switch"}, "textCardCustomWatermarkText": "自定义水印文字", "@textCardCustomWatermarkText": {"description": "Label for custom watermark text field"}, "textCardCustomWatermarkHint": "留空使用默认水印", "@textCardCustomWatermarkHint": {"description": "Hint for custom watermark text field"}, "textCardAdvancedOptions": "高级选项", "@textCardAdvancedOptions": {"description": "Advanced options section title"}, "@textCardIncludeTitle": {"description": "Switch to include title"}, "textCardIncludeTitleSubtitle": "在导出的图片中显示标题", "@textCardIncludeTitleSubtitle": {"description": "Subtitle for title switch"}, "@textCardIncludeTimestamp": {"description": "Switch to include timestamp"}, "textCardIncludeTimestampSubtitle": "在图片中显示创建时间", "@textCardIncludeTimestampSubtitle": {"description": "Subtitle for timestamp switch"}, "textCardMore": "更多", "@textCardMore": {"description": "More items label"}, "@textCardSelectColor": {"description": "Color picker dialog title"}, "textCardPart": "第", "@textCardPart": {"description": "Part label for card sections"}, "textCardSection": "部分", "@textCardSection": {"description": "Section label for card sections"}, "@textCardTextStyleCustomization": {"description": "Title for text style customization"}, "@textCardExportCurrentCard": {"description": "Button to export current card"}, "@textCardBatchExport": {"description": "Button to export multiple cards"}, "@textCardClearSelection": {"description": "Button to clear text selection"}, "@textCardResetStyle": {"description": "Button to reset text style"}, "textCardSelectionInstructions": "选择下方文本的任意范围，即可对选中内容应用样式", "@textCardSelectionInstructions": {"description": "Instructions for text selection"}, "@textCardResetStyleConfirm": {"description": "Confirmation message for resetting styles"}, "textCardExportOptions": "导出选项", "@textCardExportOptions": {"description": "Export options section title"}, "textCardSaveToGallery": "保存到相册", "@textCardSaveToGallery": {"description": "Save to gallery button text"}, "textCardExportSingleCard": "将导出 1 张卡片图片", "@textCardExportSingleCard": {"description": "Single card export message"}, "textCardExportMultipleCards": "将导出", "@textCardExportMultipleCards": {"description": "Multiple cards export prefix"}, "textCardImages": "张卡片图片", "@textCardImages": {"description": "Card images suffix"}, "textCardCard": "第", "@textCardCard": {"description": "Card label"}, "textCardCardNumber": "张", "@textCardCardNumber": {"description": "Card number suffix"}, "textCardShareFromApp": "来自 内容君 的", "@textCardShareFromApp": {"description": "Share message prefix"}, "textCardShareFailed": "分享失败", "@textCardShareFailed": {"description": "Share failed message"}, "textCardSavedToGallery": "已保存", "@textCardSavedToGallery": {"description": "Saved to gallery prefix"}, "textCardCardsToGallery": "张卡片到相册", "@textCardCardsToGallery": {"description": "Cards to gallery suffix"}, "@textCardSaveFailed": {"description": "Save failed message"}, "@textCardEditDocument": {"description": "Edit document title"}, "textCardCreateDocument": "创建文档", "@textCardCreateDocument": {"description": "Create document title"}, "textCardTextEditMode": "文本编辑模式", "@textCardTextEditMode": {"description": "Text edit mode label"}, "textCardPreviewMode": "预览模式", "@textCardPreviewMode": {"description": "Preview mode label"}, "textCardTotalCards": "共", "@textCardTotalCards": {"description": "Total cards prefix"}, "textCardCards": "个卡片", "@textCardCards": {"description": "Cards suffix"}, "@textCardInsertSeparator": {"description": "Insert separator button"}, "textCardUseSeparator": "使用", "@textCardUseSeparator": {"description": "Use separator prefix"}, "textCardSeparatorHint": "分隔卡片", "@textCardSeparatorHint": {"description": "Separator hint suffix"}, "textCardDocumentTitle": "文档标题", "@textCardDocumentTitle": {"description": "Document title label"}, "textCardDocumentTitleHint": "为这组卡片起个标题...", "@textCardDocumentTitleHint": {"description": "Document title hint"}, "textCardDocumentContentHint": "输入或粘贴长文本...\n\n💡 使用技巧：\n• 在想要分割的地方点击\"插入分隔符\"按钮\n• 第一行如果是标题会自动识别\n• 点击右上角\"预览\"查看拆分效果", "@textCardDocumentContentHint": {"description": "Document content hint"}, "@textCardUniformStyle": {"description": "Uniform style button"}, "textCardGoBackToEditMode": "回到编辑模式添加分隔符来创建卡片", "@textCardGoBackToEditMode": {"description": "Instruction to go back to edit mode"}, "textCardListView": "列表视图", "@textCardListView": {"description": "List view tooltip"}, "textCardGridView": "网格视图", "@textCardGridView": {"description": "Grid view tooltip"}, "textCardTemplate": "模板", "@textCardTemplate": {"description": "Template label"}, "textCardNoCardsInDocument": "文档中没有卡片", "@textCardNoCardsInDocument": {"description": "No cards in document message"}, "textCardEditDocumentToAddCards": "编辑文档来添加卡片", "@textCardEditDocumentToAddCards": {"description": "Edit document to add cards hint"}, "textCardDaysAgo": "天前", "@textCardDaysAgo": {"description": "Days ago suffix"}, "textCardHoursAgo": "小时前", "@textCardHoursAgo": {"description": "Hours ago suffix"}, "textCardMinutesAgo": "分钟前", "@textCardMinutesAgo": {"description": "Minutes ago suffix"}, "textCardJustNow": "刚刚", "@textCardJustNow": {"description": "Just now label"}, "textCardPureTextCustomRendering": "纯文本定制渲染", "@textCardPureTextCustomRendering": {"description": "Pure text custom rendering title"}, "textCardRenderContentToCards": "将您的内容渲染成精美的卡片", "@textCardRenderContentToCards": {"description": "Render content to cards subtitle"}, "textCardDesignDescription": "这是一个分离式的设计：简单的编辑器用于内容编辑和拆分，强大的可视化渲染器用于样式定制和最终展示。", "@textCardDesignDescription": {"description": "Design description"}, "textCardSimpleEdit": "简单编辑", "@textCardSimpleEdit": {"description": "Simple edit feature title"}, "textCardSimpleEditDesc": "专注于纯文本编辑和内容拆分，无复杂格式干扰", "@textCardSimpleEditDesc": {"description": "Simple edit feature description"}, "textCardVisualRendering": "可视化渲染", "@textCardVisualRendering": {"description": "Visual rendering feature title"}, "textCardVisualRenderingDesc": "所见即所得的样式定制，选中文本直接修改样式", "@textCardVisualRenderingDesc": {"description": "Visual rendering feature description"}, "textCardSmartRecognition": "智能识别", "@textCardSmartRecognition": {"description": "Smart recognition feature title"}, "textCardSmartRecognitionDesc": "自动识别标题、列表、引用等内容类型并美化渲染", "@textCardSmartRecognitionDesc": {"description": "Smart recognition feature description"}, "textCardExportShare": "导出分享", "@textCardExportShare": {"description": "Export share feature title"}, "textCardExportShareDesc": "支持单卡片和批量导出，轻松分享精美内容", "@textCardExportShareDesc": {"description": "Export share feature description"}, "textCardCoreFeatures": "核心功能", "@textCardCoreFeatures": {"description": "Core features section title"}, "textCardMarkdownTip": "提示：支持Markdown格式的文本输入，包括标题、列表、引用等", "@textCardMarkdownTip": {"description": "Markdown tip"}, "textCardStartCreating": "开始创建", "@textCardStartCreating": {"description": "Start creating button text"}, "textCardClickToEditContent": "点击编辑内容", "@textCardClickToEditContent": {"description": "Edit content hint text"}, "textCardsLightCategory": "明亮", "@textCardsLightCategory": {"description": "Light template category"}, "@textCardsDarkCategory": {"description": "Dark template category"}, "@textCardsNatureCategory": {"description": "Nature template category"}, "@textCardsWarmCategory": {"description": "Warm template category"}, "@textCardsTechCategory": {"description": "Tech template category"}, "@textCardsElegantCategory": {"description": "Elegant template category"}, "@textCardsVintageCategory": {"description": "Vintage template category"}, "@textCardsPreviewEffect": {"description": "Preview effect title"}, "@textCardCreateBeautifulCard": {"description": "Create beautiful card button"}, "@textCardsSelectTextToModifyStyle": {"description": "Tip for selecting text to modify style"}, "@textCardsSelectTemplate": {"description": "Select template label"}, "@textCardsTemplateGallery": {"description": "Template gallery title"}, "@textCardsBrowseTemplates": {"description": "Browse templates button"}, "@textCardsBeautifulTemplates": {"description": "Beautiful templates text"}, "@textCardsAllCategories": {"description": "All categories filter"}, "@textCardsBusinessCategory": {"description": "Business category name"}, "@textCardsAcademicCategory": {"description": "Academic category name"}, "@textCardsCreativeCategory": {"description": "Creative category name"}, "@textCardsMinimalCategory": {"description": "Minimal category name"}, "@textCardsModernCategory": {"description": "Modern category name"}, "@textCardSplitFailed": {"description": "Split failed error message"}, "@textCardCreateFailed": {"description": "Create failed error message"}, "@textCardExportSettings": {"description": "Export settings title"}, "@textCardImageSize": {"description": "Image size setting"}, "@textCardImageRatio": {"description": "Image ratio setting"}, "@textCardQuality": {"description": "Quality setting"}, "@textCardIncludeWatermark": {"description": "Include watermark option"}, "smartTextSplitter": "智能文本拆分", "@smartTextSplitter": {"description": "Smart text splitter title"}, "smartTextSplitterSubtitle": "将长文本智能分割为多个卡片", "@smartTextSplitterSubtitle": {"description": "Smart text splitter subtitle"}, "@inputTextToSplit": {"description": "Input text to split title"}, "@pasteOrInputLongText": {"description": "Paste or input long text description"}, "@pasteClipboard": {"description": "Paste clipboard button"}, "@clearContent": {"description": "Clear content button"}, "characterCount": "字符数：{count}", "@characterCount": {"description": "Character count format", "placeholders": {"count": {"type": "int", "description": "Character count"}}}, "splitConfig": "拆分配置", "@splitConfig": {"description": "Split configuration title"}, "splitConfigDescription": "选择拆分模式和相关参数", "@splitConfigDescription": {"description": "Split configuration description"}, "splitMode": "拆分模式", "@splitMode": {"description": "Split mode section title"}, "customSeparator": "自定义分隔符", "@customSeparator": {"description": "Custom separator field"}, "customSeparatorHint": "输入分隔符，如：---", "@customSeparatorHint": {"description": "Custom separator hint"}, "advancedOptions": "高级选项", "@advancedOptions": {"description": "Advanced options section title"}, "autoDetectTitles": "自动识别标题", "@autoDetectTitles": {"description": "Auto-detect titles option"}, "autoDetectTitlesDescription": "智能识别文本中的标题内容", "@autoDetectTitlesDescription": {"description": "Auto-detect titles description"}, "preserveFormatting": "保留格式", "@preserveFormatting": {"description": "Preserve formatting option"}, "preserveFormattingDescription": "保留原文本的Markdown格式", "@preserveFormattingDescription": {"description": "Preserve formatting description"}, "smartMerge": "智能合并", "@smartMerge": {"description": "Smart merge option"}, "smartMergeDescription": "自动合并过短的段落", "@smartMergeDescription": {"description": "Smart merge description"}, "maxLength": "最大长度", "@maxLength": {"description": "Max length setting"}, "maxLengthDescription": "每个卡片的最大字符数", "@maxLengthDescription": {"description": "Max length description"}, "splitPreview": "拆分预览", "@splitPreview": {"description": "Split preview title"}, "totalCards": "共 {count} 个卡片", "@totalCards": {"description": "Total cards count format", "placeholders": {"count": {"type": "int", "description": "Card count"}}}, "splitPreviewDescription": "预览拆分结果，可以编辑、合并或删除卡片", "@splitPreviewDescription": {"description": "Split preview description"}, "@selectAll": {"description": "Select all button"}, "deselectAll": "取消选择", "@deselectAll": {"description": "Deselect all button"}, "deleteSelected": "删除选中", "@deleteSelected": {"description": "Delete selected button"}, "noSplitResults": "没有拆分结果", "@noSplitResults": {"description": "No split results message"}, "noSplitResultsDescription": "请返回上一步检查输入文本和配置", "@noSplitResultsDescription": {"description": "No split results description"}, "previousStep": "上一步", "@previousStep": {"description": "Previous step button"}, "nextStep": "下一步", "@nextStep": {"description": "Next step button"}, "startSplitting": "开始拆分", "@startSplitting": {"description": "Start splitting button"}, "createCards": "创建卡片", "@createCards": {"description": "Create cards button"}, "inputTextHint": "在这里输入或粘贴文本内容...", "@inputTextHint": {"description": "Input text hint"}, "titleOptional": "标题（可选）", "@titleOptional": {"description": "Title optional field"}, "@content": {"description": "Content field"}, "exportSettings": "导出设置", "@exportSettings": {"description": "Export settings title"}, "confirmExport": "确定导出", "@confirmExport": {"description": "Confirm export button"}, "previewInfo": "预览信息", "@previewInfo": {"description": "Preview info section"}, "dimensions": "尺寸", "@dimensions": {"description": "Dimensions label"}, "ratio": "比例", "@ratio": {"description": "Ratio label"}, "qualityPercent": "质量", "@qualityPercent": {"description": "Quality percentage label"}, "watermarkStatus": "水印", "@watermarkStatus": {"description": "Watermark status label"}, "include": "包含", "@include": {"description": "Include status"}, "notInclude": "不包含", "@notInclude": {"description": "Not include status"}, "pixels": "像素", "@pixels": {"description": "Pixels unit"}, "commonCarryOn": "Carry On", "commonEndure": "Endure", "commonGoOn": "Go On", "commonKeepOn": "Keep On", "commonLast": "Last", "commonPersevere": "Persevere", "commonPersist": "Persist", "complete": "完成", "htmlCopy": "Copy", "htmlNewDocumentTitle": "New HTML Document", "htmlRenderError": "Cannot render HTML content", "htmlUntitled": "Untitled HTML", "pdfAllowCopying": "Allow Copying", "pdfCreatedTime": "Created", "pdfDocumentInformation": "Document Information", "pdfEncryptedMessage": "This PDF is encrypted, please enter password to decrypt", "pdfEncryptionAlgorithm": "Encryption Algorithm", "pdfEncryptionAlgorithmDesc": "Adopt industry-standard AES encryption algorithm to ensure document security", "pdfImportToStart": "Import PDF to Start", "pdfModifiedTime": "Modified", "pdfMultiDeviceSync": "Multi-device Sync", "pdfMultipleFormats": "Multiple Formats", "pdfPasswordProtectionDesc": "Set user and owner passwords for PDF documents to ensure document security", "pdfPermissionControlDesc": "Fine-grained control over document printing, copying, editing and other permissions", "pdfPermissionsFailed": "Permissions set failed", "pdfPermissionsSuccess": "Permissions set successfully", "pdfProtectYourDocuments": "Protect your important documents", "pdfSecurityEncrypted": "Encrypted", "pdfSecurityOpen": "Open", "pdfSecurityReadOnly": "Read Only", "pdfSecurityRestricted": "Restricted", "pdfSettingsFailed": "Settings failed: {error}", "pdfUsageTips": "Usage Tips", "voiceAddToPlaylist": "Add to playlist", "voiceAddedToPlaylist": "Added to playlist", "voiceAudioAdjustment": "Audio Adjustment", "voiceAudioAdjustmentDesc": "Adjust speed, pitch and volume", "voiceAudioDurationAbnormal": "Audio duration abnormal, may not play properly", "voiceAudioFileNotExist": "Audio file does not exist, cannot play", "voiceAudioFileNotExistOrCorrupted": "Audio file does not exist or is corrupted", "voiceAutoAddPunctuation": "Automatically add punctuation", "voiceBatchProcessingProgress": "Batch Processing Progress", "voiceBatchTranscription": "Batch Transcription", "voiceBatchTranscriptionFeature": "Batch Transcription Feature", "voiceBatchTranscriptionInDev": "Batch transcription feature in development...", "voiceClear": "Clear", "voiceClickToStartRealtime": "Click button below to start realtime transcription", "voiceClickToStartTranscription": "Click start button to begin transcription...", "voiceCloudSync": "Cloud Sync", "voiceCloudSyncDesc": "Sync your recordings across devices", "voiceConfidenceThreshold": "Confidence Threshold", "voiceConvertTextToVoice": "Convert text to voice", "voiceCreateTime": "Create Time: {time}", "voiceCumulativeDuration": "Cumulative Duration", "voiceDetectDifferentSpeakers": "Detect different speakers", "voiceDirectIosPermissionTest": "Direct iOS Permission Test", "voiceEnablePunctuation": "Enable Punctuation", "voiceExport": "Export", "voiceExportFeatureInDev": "Export feature in development...", "voiceFileButCorrupted": ", but file may be corrupted", "voiceFileExists": "file exists", "voiceFileNotExists": "file does not exist", "voiceFilePath": "File path: {path}", "voiceFileSelected": "File Selected", "voiceFileStatusRechecked": "File status rechecked: {status}{corrupted}", "voiceFileTranscription": "File Transcription", "voiceFilesSelected": "Selected {count} files", "voiceHomeSubtitle": "Record ideas, convert to text, smart reading", "voiceHomeTitle": "Voice Assistant", "voiceInitialMicPermissionStatus": "Initial microphone permission status: {status}", "voiceInitialSpeechPermissionStatus": "Initial speech recognition permission status: {status}", "voiceInitializationException": "Initialization exception: {error}", "voiceInitializationFailedCheckPermission": "Initialization failed, please check microphone permission", "voiceInputTextHint": "Input text to read, click \"Add\" button to add to playlist", "voiceInputTextToRead": "Input text to read", "voiceIosDirectPermissionTest": "Direct iOS Permission Test", "voiceIosFailed": "Failed", "voiceIosInitialMicrophonePermission": "Initial microphone permission status", "voiceIosInitialSpeechPermission": "Initial speech recognition permission status", "voiceIosMicrophonePermissionStatus": "Microphone permission status", "voiceIosNonIosPlatform": "Non-iOS platform, not checking permissions", "voiceIosOpenAppSettings": "Open app settings", "voiceIosOperationLogs": "Operation Logs", "voiceIosPageLoaded": "Page loaded", "voiceIosPermissionTest": "iOS Permission Test", "voiceIosPermissionTestTitle": "iOS Permission Test", "voiceIosRecognitionResult": "Recognition result", "voiceIosRecorderClosed": "Recorder closed", "voiceIosRecorderError": "Recorder error", "voiceIosRecorderInitialized": "Recorder initialized", "voiceIosRecorderInstanceCreated": "Recorder instance created", "voiceIosRecorderTestCompleted": "Recorder test completed", "voiceIosRequestMicrophonePermission": "Requesting microphone permission...", "voiceIosRequestSpeechPermission": "Requesting speech recognition permission...", "voiceIosSpeechPermissionStatus": "Speech recognition permission status", "voiceIosSpeechRecognitionError": "Speech recognition error", "voiceIosSpeechRecognitionInitialization": "Speech recognition initialization", "voiceIosSpeechRecognitionStatus": "Speech recognition status", "voiceIosSpeechRecognitionTestError": "Speech recognition test error", "voiceIosStartListening": "Start listening...", "voiceIosStopListening": "Stop listening", "voiceIosSuccess": "Success", "voiceIosTestRecordingFunction": "Testing recording function...", "voiceIosTestSpeechRecognition": "Testing speech recognition...", "voiceLanguage": "Language:", "voiceLanguageChineseSimplified": "中文（简体）", "voiceLanguageChineseTraditional": "中文（繁体）", "voiceLanguageEnglish": "English (US)", "voiceLanguageJapanese": "日本語", "voiceLanguageKorean": "한국어", "voiceLanguageSelector": "Language:", "voiceLoadAudioFailed": "Failed to load audio: {error}", "voiceMicPermissionStatus": "Microphone permission status: {status}", "voiceMicrophonePermissionRequired": "Voice transcription requires microphone permission. Please allow microphone access in settings.", "voiceNoRecordingsYet": "No recordings yet", "voiceNoTranscriptionContent": "No transcription content to export", "voiceNoTranscriptionContentToSave": "No transcription content to save", "voiceNoTranscriptionContentToShare": "No transcription content to share", "voiceNoTranscriptionText": "No transcription text yet", "voiceNonIosPlatform": "Non-iOS platform, not checking permissions", "voiceOK": "OK", "voiceOpenAppSettings": "Open App Settings", "voiceOperationLog": "Operation Log:", "voicePageLoaded": "Page loaded", "voicePaused": "Paused", "voicePermissionRequiredMessage": "Voice transcription feature requires microphone permission. Please enable microphone access in settings.", "voicePitch": "Pitch:", "voicePlayFailed": "Play failed: {error}", "voicePlaying": "Playing...", "voicePlaylist": "Playlist", "voicePlaylistDesc": "Manage and play multiple audio files", "voicePlaylistEmpty": "Playlist is empty", "voicePlaylistTitle": "Playlist", "voicePowerfulFeatures": "Powerful Features", "voiceProcessAudioFileFailed": "Failed to process audio file: {error}", "voiceQuickActions": "Quick Actions", "voiceReadText": "Read Text", "voiceReadyToStart": "Ready to start", "voiceRealtimeTranscription": "Realtime Transcription", "voiceRecentRecordings": "Recent Recordings", "voiceRecheck": "Recheck", "voiceRecognitionResult": "Recognition result: {result}", "voiceRecordDetailTitle": "Voice Details", "voiceRecordNewVoice": "Record new voice", "voiceRecorderClosed": "Recorder closed", "voiceRecorderError": "Recorder error: {error}", "voiceRecorderInitialized": "Recorder initialized", "voiceRecorderInstanceCreated": "Recorder instance created", "voiceRecorderTestComplete": "Recorder test complete", "voiceRecordingComplete": "Recording complete", "voiceRecordingCount": "Recording Count", "voiceRecordingFailedRetry": "Recording failed, please retry", "voiceRecordingFileMayBeCorrupted": "Recording file may be corrupted, cannot play", "voiceRecordingsUnit": "recordings", "voiceRequestMicPermission": "Request Microphone Permission", "voiceRequestSpeechPermission": "Request Speech Recognition Permission", "voiceReselectFile": "Reselect File", "voiceSaveAllChanges": "Save all changes", "voiceSaveFailed": "Save failed: {error}", "voiceSaveTranscriptionResult": "Save Transcription Result", "voiceSaveTranscriptionText": "Save Transcription Text", "voiceSelectAudioFile": "Select audio file for transcription", "voiceSelectBatchFilesFailed": "Failed to select batch files: {error}", "voiceSelectFile": "Select File", "voiceSelectFileFailed": "Failed to select file: {error}", "voiceSelectMultipleFiles": "Select multiple files for batch transcription", "voiceSelectMultipleFilesBtn": "Select Multiple Files", "voiceServiceInitializationFailed": "Service initialization failed", "voiceShare": "Share", "voiceShareFeatureInDev": "Share feature in development...", "voiceSmartTranscription": "Smart Transcription", "voiceSmartTranscriptionDesc": "Automatically convert voice to text", "voiceSmartTranscriptionPageTitle": "Smart Transcription", "voiceSpeakerDetection": "Speaker Detection", "voiceSpeechPermissionStatus": "Speech recognition permission status: {status}", "voiceSpeechRate": "Speech Rate:", "voiceSpeechRecognitionError": "Speech recognition error: {error}", "voiceSpeechRecognitionInit": "Speech recognition init: {success}", "voiceSpeechRecognitionStatus": "Speech recognition status: {status}", "voiceSpeechRecognitionTestError": "Speech recognition test error: {error}", "voiceStartFirstRecording": "Click the button below to start your first recording", "voiceStartListening": "Start listening...", "voiceStartTranscriptionFailed": "Failed to start transcription", "voiceStop": "Stop", "voiceStopListening": "Stop listening", "voiceStopTranscription": "Stop Transcription", "voiceTestRecording": "Test Recording", "voiceTestRecordingFunction": "Test recording function...", "voiceTestSpeechRecognition": "Test Speech Recognition", "voiceTestSpeechRecognitionFunction": "Test speech recognition...", "voiceTextToSpeech": "Text to Speech", "voiceTitleSaved": "Title saved", "voiceTotalDuration": "Total Duration", "voiceTranscribing": "Transcribing...", "voiceTranscriptionCompleted": "Transcription Completed", "voiceTranscriptionContentPreview": "Transcription content preview:", "voiceTranscriptionError": "Transcription Error", "voiceTranscriptionFailedRetry": "Transcription failed, please retry", "voiceTranscriptionIdle": "Ready", "voiceTranscriptionInProgress": "Transcribing...", "voiceTranscriptionResult": "Transcription Result", "voiceTranscriptionResultSaved": "Transcription result saved", "voiceTranscriptionResultTitle": "Transcription Result", "voiceTranscriptionSettings": "Transcription Settings", "voiceTranscriptionTextSaved": "Transcription text saved", "voiceTranscriptionTitle": "Smart Transcription", "voiceTtsPlayerTitle": "Text to Speech", "voiceTtsSettings": "TTS Settings", "voiceUsageStats": "Usage Statistics", "voiceViewAll": "View All", "voiceVoiceTranscription": "Voice Transcription", "voiceVolume": "Volume:", "trafficGuideVisibleWatermark": "可见水印", "trafficGuideWatermarkType": "水印类型", "trafficGuideShowPreview": "显示预览", "trafficGuideHidePreview": "隐藏预览", "trafficGuideProcessSuccess": "处理成功", "trafficGuideDetectedWatermark": "检测到水印", "@trafficGuideDetectedWatermark": {"description": "检测到水印信息的标签"}, "trafficGuideUnknownWatermark": "未知水印", "@trafficGuideUnknownWatermark": {"description": "未知水印类型的标签"}, "trafficGuideNoWatermarkDetected": "未检测到水印", "@trafficGuideNoWatermarkDetected": {"description": "未找到水印时的消息"}, "trafficGuideProcessedText": "处理后的文本", "@trafficGuideProcessedText": {"description": "处理后的文本输出的标签"}, "trafficGuideInvisibleWatermarkInfo": "这将使用特殊的Unicode字符添加不可见水印，读者无法看到但可以被此工具检测到。", "@trafficGuideInvisibleWatermarkInfo": {"description": "关于不可见水印功能的信息"}, "trafficGuideWatermarkRemovedSuccess": "水印移除成功！", "@trafficGuideWatermarkRemovedSuccess": {"description": "水印移除成功时的消息"}, "trafficGuideWatermarkAddedSuccess": "水印添加成功！", "@trafficGuideWatermarkAddedSuccess": {"description": "水印添加成功时的消息"}, "exportSocialWeChatMoments": "微信朋友圈", "exportSocialWeibo": "微博配图", "exportSocialXiaohongshu": "小红书", "exportSocialInstagram": "Instagram", "exportSocialTwitter": "Twitter", "exportWidthLabel": "宽度", "exportHeightLabel": "高度", "exportOptimizeForSocial": "社交媒体优化", "exportOptimizeForSocialSubtitle": "针对社交平台优化尺寸", "exportSocialPlatformSizes": "社交平台尺寸", "exportSizeSmall": "小 (400×300)", "exportSizeMedium": "中 (800×600)", "exportSizeLarge": "大 (1200×900)", "exportSizeCustom": "自定义", "general": "通用", "@general": {"description": "通用设置标题"}, "appearanceAndBrightness": "显示与亮度", "@appearanceAndBrightness": {"description": "显示与亮度设置标题"}, "privacyAndSecurity": "隐私与安全", "@privacyAndSecurity": {"description": "隐私与安全设置标题"}, "storage": "存储", "@storage": {"description": "存储设置标题"}, "support": "支持", "@support": {"description": "支持设置标题"}, "languageAndRegion": "语言与地区", "@languageAndRegion": {"description": "语言与地区设置项"}, "privacySettings": "隐私设置", "@privacySettings": {"description": "隐私设置项"}, "storageManagement": "存储管理", "@storageManagement": {"description": "存储管理项"}, "viewStorageUsage": "查看存储使用情况", "@viewStorageUsage": {"description": "查看存储使用情况描述"}, "dataImportExport": "数据导入导出", "@dataImportExport": {"description": "数据导入导出项"}, "backupAndRestoreData": "备份和恢复数据", "@backupAndRestoreData": {"description": "备份和恢复数据描述"}, "helpCenter": "帮助中心", "@helpCenter": {"description": "帮助中心项"}, "feedback": "意见反馈", "@feedback": {"description": "意见反馈项"}, "rateApp": "评价应用", "@rateApp": {"description": "评价应用项"}, "aboutApp": "关于应用", "@aboutApp": {"description": "关于应用项"}, "dataManagement": "数据管理", "@dataManagement": {"description": "数据管理标题"}, "selectOperation": "选择要执行的操作", "@selectOperation": {"description": "选择操作提示"}, "exportData": "导出数据", "@exportData": {"description": "导出数据操作"}, "importData": "导入数据", "@importData": {"description": "导入数据操作"}, "createBackup": "创建备份", "@createBackup": {"description": "创建备份操作"}, "dataExportInProgress": "数据导出功能开发中...", "@dataExportInProgress": {"description": "数据导出开发中提示"}, "dataImportInProgress": "数据导入功能开发中...", "@dataImportInProgress": {"description": "数据导入开发中提示"}, "backupInProgress": "备份功能开发中...", "@backupInProgress": {"description": "备份开发中提示"}, "doYouLikeThisApp": "您喜欢这个应用吗？请在App Store中为我们评分！", "@doYouLikeThisApp": {"description": "评价应用提示"}, "later": "稍后", "@later": {"description": "稍后按钮"}, "goToRate": "去评价", "@goToRate": {"description": "去评价按钮"}, "cannotOpenAppStore": "无法打开App Store", "@cannotOpenAppStore": {"description": "无法打开App Store错误"}, "errorOpeningAppStore": "打开App Store时出现错误", "@errorOpeningAppStore": {"description": "打开App Store错误"}, "@close": {"description": "关闭按钮"}, "@confirm": {"description": "确定按钮"}, "understand": "了解", "@understand": {"description": "了解按钮"}, "weValueYourPrivacy": "我们重视您的隐私。所有数据处理都在本地进行，不会上传到服务器。", "@weValueYourPrivacy": {"description": "隐私说明"}, "manageDataAnytime": "您可以随时在设置中管理您的数据。", "@manageDataAnytime": {"description": "数据管理说明"}, "languageSelected": "已选择语言：{language}", "@languageSelected": {"description": "语言选择提示"}, "light": "浅色", "@light": {"description": "浅色主题"}, "dark": "深色", "@dark": {"description": "深色主题"}, "selectLanguage": "选择语言", "@selectLanguage": {"description": "选择语言标题"}, "selectAppearance": "选择外观", "@selectAppearance": {"description": "选择外观标题"}, "traditionalChinese": "繁體中文", "@traditionalChinese": {"description": "繁体中文"}, "simplifiedChinese": "简体中文", "@simplifiedChinese": {"description": "简体中文"}, "contentSaveButtonFavorite": "收藏", "@contentSaveButtonFavorite": {"description": "内容管理器中保存/收藏按钮的文本"}, "textTransformerSelectMode": "选择转换模式", "@textTransformerSelectMode": {"description": "模板选择区域标题"}, "textTransformerInputText": "输入文本", "@textTransformerInputText": {"description": "输入文本区域标签"}, "textTransformerOutputResult": "转换结果", "@textTransformerOutputResult": {"description": "输出结果区域标签"}, "textTransformerHint": "在这里输入要转换的文本...", "@textTransformerHint": {"description": "输入字段提示文本"}, "textTransformerOutputHint": "转换后的文本将显示在这里...", "@textTransformerOutputHint": {"description": "输出字段提示文本"}, "textTransformerCharacters": "字符", "@textTransformerCharacters": {"description": "字符计数文本"}, "textTransformerTransform": "切换效果", "@textTransformerTransform": {"description": "转换按钮文本"}, "textTransformerTransforming": "转换中...", "@textTransformerTransforming": {"description": "转换时显示的文本"}, "textTransformerClearAll": "清空所有", "@textTransformerClearAll": {"description": "清空所有按钮提示"}, "textTransformerCopyResult": "复制结果", "@textTransformerCopyResult": {"description": "复制结果按钮提示"}, "textTransformerCopied": "已复制到剪贴板", "@textTransformerCopied": {"description": "文本复制后显示的消息"}, "textTransformerTemplateEmojiName": "表情符号转换", "@textTransformerTemplateEmojiName": {"description": "表情符号模板名称"}, "textTransformerTemplateEmojiDesc": "将文字转换为特殊表情符号", "@textTransformerTemplateEmojiDesc": {"description": "表情符号模板描述"}, "textTransformerTemplateFancyName": "花体字母", "@textTransformerTemplateFancyName": {"description": "花体字母模板名称"}, "textTransformerTemplateFancyDesc": "转换为优雅的花体字母", "@textTransformerTemplateFancyDesc": {"description": "花体字母模板描述"}, "textTransformerTemplateBoldName": "粗体文字", "@textTransformerTemplateBoldName": {"description": "粗体文字模板名称"}, "textTransformerTemplateBoldDesc": "转换为粗体Unicode字符", "@textTransformerTemplateBoldDesc": {"description": "粗体文字模板描述"}, "textTransformerTemplateDecorativeName": "装饰文字", "@textTransformerTemplateDecorativeName": {"description": "装饰文字模板名称"}, "textTransformerTemplateDecorativeDesc": "添加装饰性符号", "@textTransformerTemplateDecorativeDesc": {"description": "装饰文字模板描述"}, "textTransformerTemplateMixedName": "混合效果", "@textTransformerTemplateMixedName": {"description": "混合效果模板名称"}, "textTransformerTemplateMixedDesc": "随机组合多种转换效果", "@textTransformerTemplateMixedDesc": {"description": "混合效果模板描述"}, "textTransformerTemplateInvisibleName": "隐形字符", "@textTransformerTemplateInvisibleName": {"description": "隐形字符模板名称"}, "textTransformerTemplateInvisibleDesc": "添加不可见字符绕过检测", "@textTransformerTemplateInvisibleDesc": {"description": "隐形字符模板描述"}, "textTransformerTemplateUnicodeName": "Unicode变体", "@textTransformerTemplateUnicodeName": {"description": "Unicode变体模板名称"}, "textTransformerTemplateUnicodeDesc": "使用Unicode变体字符", "@textTransformerTemplateUnicodeDesc": {"description": "Unicode变体模板描述"}, "textTransformerEffectEmojiDesc": "数字和字母转换为特殊Unicode字符", "@textTransformerEffectEmojiDesc": {"description": "表情符号模式效果描述"}, "textTransformerEffectFancyDesc": "转换为优雅的花体字母", "@textTransformerEffectFancyDesc": {"description": "花体字母模式效果描述"}, "textTransformerEffectBoldDesc": "转换为粗体Unicode字符", "@textTransformerEffectBoldDesc": {"description": "粗体文字模式效果描述"}, "textTransformerEffectDecorativeDesc": "添加装饰性符号", "@textTransformerEffectDecorativeDesc": {"description": "装饰文字模式效果描述"}, "textTransformerEffectMixedDesc": "随机组合多种转换效果", "@textTransformerEffectMixedDesc": {"description": "混合效果模式效果描述"}, "textTransformerEffectInvisibleDesc": "在字符间添加不可见字符，绕过检测", "@textTransformerEffectInvisibleDesc": {"description": "隐形字符模式效果描述"}, "textTransformerEffectUnicodeDesc": "添加变音符号，改变字符外观", "@textTransformerEffectUnicodeDesc": {"description": "Unicode变体模式效果描述"}, "textTransformerSample": "测试文本", "@textTransformerSample": {"description": "转换用的通用示例文本"}, "textTransformerSampleInvisible": "敏感内容检测绕过测试", "@textTransformerSampleInvisible": {"description": "隐形字符模式示例文本"}, "textTransformerSampleUnicode": "特殊字符转换测试", "@textTransformerSampleUnicode": {"description": "Unicode变体模式示例文本"}}